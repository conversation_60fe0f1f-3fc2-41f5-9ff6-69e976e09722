// 星座数据同步云函数
const cloud = require('wx-server-sdk')

cloud.init({
    env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
    const { type, data, timestamp, version } = event
    const wxContext = cloud.getWXContext()
    const openid = wxContext.OPENID

    console.log('星座数据同步请求:', { type, openid, timestamp, version })

    try {
        switch (type) {
            case 'full':
                return await performFullSync(openid, data, timestamp, version)
            
            case 'incremental':
                return await performIncrementalSync(openid, data, timestamp, version)
            
            case 'upload':
                return await uploadConstellationData(openid, data, timestamp, version)
            
            default:
                throw new Error(`不支持的同步类型: ${type}`)
        }

    } catch (error) {
        console.error('星座数据同步失败:', error)
        return {
            success: false,
            error: error.message,
            timestamp: Date.now()
        }
    }
}

/**
 * 执行完整同步
 */
async function performFullSync(openid, data, timestamp, version) {
    try {
        console.log('执行完整同步...')

        // 保存用户的星座数据
        const saveResult = await saveUserConstellationData(openid, data, version)
        
        if (!saveResult.success) {
            throw new Error('保存星座数据失败')
        }

        // 获取最新的完整数据
        const latestData = await getUserConstellationData(openid)

        return {
            success: true,
            syncId: `sync_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            type: 'full',
            data: latestData,
            timestamp: Date.now(),
            version: version
        }

    } catch (error) {
        console.error('完整同步失败:', error)
        throw error
    }
}

/**
 * 执行增量同步
 */
async function performIncrementalSync(openid, data, lastSyncTime, version) {
    try {
        console.log('执行增量同步...')

        // 获取自上次同步以来的变更
        const changes = await getDataChangesSince(openid, lastSyncTime)
        
        // 如果有本地变更，先保存
        if (data && data.changes) {
            await applyDataChanges(openid, data.changes, version)
        }

        return {
            success: true,
            syncId: `sync_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            type: 'incremental',
            changes: changes,
            timestamp: Date.now(),
            version: version
        }

    } catch (error) {
        console.error('增量同步失败:', error)
        throw error
    }
}

/**
 * 上传星座数据
 */
async function uploadConstellationData(openid, data, timestamp, version) {
    try {
        console.log('上传星座数据...')

        const saveResult = await saveUserConstellationData(openid, data, version)
        
        if (!saveResult.success) {
            throw new Error('上传数据失败')
        }

        return {
            success: true,
            syncId: saveResult.syncId,
            type: 'upload',
            timestamp: Date.now(),
            version: version
        }

    } catch (error) {
        console.error('上传数据失败:', error)
        throw error
    }
}

/**
 * 保存用户星座数据
 */
async function saveUserConstellationData(openid, data, version) {
    try {
        const syncId = `sync_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        
        // 准备保存的数据
        const saveData = {
            openid: openid,
            constellationData: data,
            version: version,
            syncId: syncId,
            lastModified: db.serverDate(),
            createdAt: db.serverDate()
        }

        // 检查是否已存在用户数据
        const existingData = await db.collection('constellation_sync')
            .where({ openid: openid })
            .get()

        if (existingData.data.length > 0) {
            // 更新现有数据
            const updateResult = await db.collection('constellation_sync')
                .where({ openid: openid })
                .update({
                    data: {
                        constellationData: data,
                        version: version,
                        syncId: syncId,
                        lastModified: db.serverDate()
                    }
                })

            console.log('更新星座数据成功:', updateResult)
        } else {
            // 创建新记录
            const addResult = await db.collection('constellation_sync')
                .add({ data: saveData })

            console.log('创建星座数据成功:', addResult)
        }

        // 记录同步日志
        await recordSyncLog(openid, 'save', syncId, data)

        return {
            success: true,
            syncId: syncId
        }

    } catch (error) {
        console.error('保存用户星座数据失败:', error)
        throw error
    }
}

/**
 * 获取用户星座数据
 */
async function getUserConstellationData(openid) {
    try {
        const result = await db.collection('constellation_sync')
            .where({ openid: openid })
            .orderBy('lastModified', 'desc')
            .limit(1)
            .get()

        if (result.data.length > 0) {
            return result.data[0].constellationData
        }

        return null

    } catch (error) {
        console.error('获取用户星座数据失败:', error)
        throw error
    }
}

/**
 * 获取数据变更
 */
async function getDataChangesSince(openid, lastSyncTime) {
    try {
        // 获取三问记录的变更
        const recordChanges = await db.collection('three_questions_records')
            .where({
                openid: openid,
                updateTime: db.command.gt(new Date(lastSyncTime))
            })
            .get()

        // 获取星星位置的变更
        const positionChanges = await db.collection('star_positions')
            .where({
                openid: openid,
                updateTime: db.command.gt(new Date(lastSyncTime))
            })
            .get()

        return {
            recordChanges: recordChanges.data,
            positionChanges: positionChanges.data,
            changeCount: recordChanges.data.length + positionChanges.data.length
        }

    } catch (error) {
        console.error('获取数据变更失败:', error)
        return { recordChanges: [], positionChanges: [], changeCount: 0 }
    }
}

/**
 * 应用数据变更
 */
async function applyDataChanges(openid, changes, version) {
    try {
        const results = []

        // 应用记录变更
        if (changes.recordChanges) {
            for (const change of changes.recordChanges) {
                const result = await applyRecordChange(openid, change)
                results.push(result)
            }
        }

        // 应用位置变更
        if (changes.positionChanges) {
            for (const change of changes.positionChanges) {
                const result = await applyPositionChange(openid, change)
                results.push(result)
            }
        }

        return {
            success: true,
            appliedChanges: results.length,
            results: results
        }

    } catch (error) {
        console.error('应用数据变更失败:', error)
        throw error
    }
}

/**
 * 应用记录变更
 */
async function applyRecordChange(openid, change) {
    try {
        if (change.operation === 'update') {
            const result = await db.collection('three_questions_records')
                .doc(change.recordId)
                .update({
                    data: change.data
                })
            return { type: 'record', operation: 'update', success: true, result }
        }

        if (change.operation === 'add') {
            const result = await db.collection('three_questions_records')
                .add({
                    data: { ...change.data, openid: openid }
                })
            return { type: 'record', operation: 'add', success: true, result }
        }

        return { type: 'record', operation: change.operation, success: false, error: '不支持的操作' }

    } catch (error) {
        console.error('应用记录变更失败:', error)
        return { type: 'record', operation: change.operation, success: false, error: error.message }
    }
}

/**
 * 应用位置变更
 */
async function applyPositionChange(openid, change) {
    try {
        if (change.operation === 'update') {
            const result = await db.collection('star_positions')
                .where({
                    openid: openid,
                    recordId: change.recordId
                })
                .update({
                    data: change.data
                })
            return { type: 'position', operation: 'update', success: true, result }
        }

        return { type: 'position', operation: change.operation, success: false, error: '不支持的操作' }

    } catch (error) {
        console.error('应用位置变更失败:', error)
        return { type: 'position', operation: change.operation, success: false, error: error.message }
    }
}

/**
 * 记录同步日志
 */
async function recordSyncLog(openid, operation, syncId, data) {
    try {
        await db.collection('sync_logs').add({
            data: {
                openid: openid,
                operation: operation,
                syncId: syncId,
                dataSize: JSON.stringify(data).length,
                timestamp: db.serverDate(),
                status: 'success'
            }
        })
    } catch (error) {
        console.error('记录同步日志失败:', error)
        // 日志记录失败不影响主流程
    }
}
