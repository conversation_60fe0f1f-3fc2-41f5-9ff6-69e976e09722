.vip-container {
  background-color: #f4f4f4;
  min-height: 100vh;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
}

.vip-header {
  background-color: #4a4a4a;
  color: white;
  padding: 30rpx;
  border-radius: 15rpx;
  margin-bottom: 20rpx;
}

.title {
  font-size: 40rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.vip-status {
  font-size: 28rpx;
  color: #e0e0e0;
}

.vip-privileges {
  background-color: white;
  border-radius: 15rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.privileges-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
  display: block;
}

.privileges-list {
  display: flex;
  flex-wrap: wrap;
}

.privilege-item {
  width: 50%;
  padding: 10rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.privilege-name {
  font-size: 28rpx;
  color: #666;
}

.privilege-value {
  font-size: 28rpx;
  color: #4CAF50;
  font-weight: bold;
}

.vip-packages {
  background-color: white;
  border-radius: 15rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.packages-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
  display: block;
}

.packages-list {
  display: flex;
  justify-content: space-between;
}

.package-item {
  width: 30%;
  border: 2rpx solid #e0e0e0;
  border-radius: 10rpx;
  padding: 20rpx;
  text-align: center;
  transition: all 0.3s;
}

.package-item.selected {
  border-color: #4CAF50;
  background-color: #e8f5e9;
}

.package-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.package-price {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #4CAF50;
}

.package-discount {
  display: block;
  font-size: 24rpx;
  color: #ff5722;
  margin-top: 10rpx;
}

.purchase-btn {
  background-color: #4CAF50;
  color: white;
  border-radius: 50rpx;
  font-size: 32rpx;
  padding: 20rpx;
  margin-top: 20rpx;
  transition: background-color 0.3s;
}

.purchase-btn:hover {
  background-color: #45a049;
} 