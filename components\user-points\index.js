Component({
    properties: {
        // 是否显示光点余额
        showPoints: {
            type: Boolean,
            value: true
        },
        // 自定义样式类
        customClass: {
            type: String,
            value: ''
        }
    },

    data: {
        points: 0,
        loading: false
    },

    lifetimes: {
        attached() {
            this.getUserPoints();
        }
    },

    methods: {
        // 获取用户光点余额
        getUserPoints() {
            if (!this.data.showPoints) return;

            this.setData({ loading: true });

            wx.cloud.callFunction({
                name: 'userPurchase',
                data: {
                    action: 'getUserPoints'
                }
            }).then(res => {
                this.setData({ loading: false });
                if (res.result && res.result.success) {
                    this.setData({
                        points: res.result.data.points
                    });
                }
            }).catch(err => {
                this.setData({ loading: false });
                console.error('获取用户光点失败:', err);
            });
        },

        // 刷新光点余额
        refreshPoints() {
            this.getUserPoints();
        },

        // 点击光点余额
        onPointsTap() {
            this.triggerEvent('pointstap', {
                points: this.data.points
            });
        }
    }
});