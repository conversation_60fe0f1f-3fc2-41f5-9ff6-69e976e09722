Page({
  data: {
    currentUser: null,
    isVip: false,
    vipExpireTime: null,
    vipPrivileges: [],
    selectedPackage: {
      months: 1,
      price: 68,
      discount: 0
    },
    packages: [
      { months: 1, price: 68, discount: 0, label: '月度会员' },
      { months: 3, price: 188, discount: 0.1, label: '季度会员' },
      { months: 12, price: 688, discount: 0.2, label: '年度会员' }
    ]
  },

  onLoad() {
    this.checkUserVipStatus()
    this.fetchVipPrivileges()
  },

  // 检查用户 VIP 状态
  checkUserVipStatus() {
    wx.cloud.callFunction({
      name: 'vipManager',
      data: {
        type: 'checkVipStatus',
        openid: wx.getStorageSync('openid')
      }
    }).then(res => {
      if (res.result.success) {
        this.setData({
          isVip: res.result.isVip,
          vipExpireTime: res.result.vipExpireTime
        })
      }
    })
  },

  // 获取 VIP 特权
  fetchVipPrivileges() {
    wx.cloud.callFunction({
      name: 'vipManager',
      data: {
        type: 'getVipPrivileges'
      }
    }).then(res => {
      if (res.result.success) {
        this.setData({
          vipPrivileges: res.result.privileges
        })
      }
    })
  },

  // 选择会员套餐
  selectPackage(e) {
    const selectedPackage = e.currentTarget.dataset.package
    this.setData({ selectedPackage })
  },

  // 购买 VIP
  purchaseVip() {
    const { months, price } = this.data.selectedPackage

    wx.showModal({
      title: '确认购买',
      content: `确定购买${months}个月VIP会员，共${price}元？`,
      success: (res) => {
        if (res.confirm) {
          this.initiateWechatPay(months, price)
        }
      }
    })
  },

  // 发起微信支付
  initiateWechatPay(months, price) {
    wx.cloud.callFunction({
      name: 'payment',
      data: {
        type: 'createVipOrder',
        months: months,
        price: price
      }
    }).then(paymentRes => {
      if (paymentRes.result.success) {
        wx.requestPayment({
          ...paymentRes.result.paymentParams,
          success: (payRes) => {
            this.confirmVipPurchase(months)
          },
          fail: (err) => {
            wx.showToast({
              title: '支付取消',
              icon: 'none'
            })
          }
        })
      } else {
        wx.showToast({
          title: '创建订单失败',
          icon: 'none'
        })
      }
    })
  },

  // 确认 VIP 购买
  confirmVipPurchase(months) {
    wx.cloud.callFunction({
      name: 'vipManager',
      data: {
        type: 'purchaseVip',
        openid: wx.getStorageSync('openid'),
        paymentInfo: { months }
      }
    }).then(res => {
      if (res.result.success) {
        wx.showToast({
          title: 'VIP开通成功',
          icon: 'success'
        })
        this.checkUserVipStatus()
      } else {
        wx.showToast({
          title: '开通失败',
          icon: 'none'
        })
      }
    })
  }
}) 