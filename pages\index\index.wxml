<view class="container">
  <image class="background" src="../../assets/background.jpg" mode="aspectFill" />

  <!-- 黑幕 -->
  <view wx:if="{{showBlackout}}" class="blackout"></view>

  <!-- 内容 -->
  <view class="overlay">
    <view class="content-box">
      <view wx:for="{{lines}}" wx:key="index" wx:if="{{visibleLines[index]}}" class="text-line">
        {{item}}
      </view>

      <view wx:if="{{showButtons}}" class="button-group">
        <button class="glass-button" bindtap="onBelieve">我愿意相信</button>
        <button class="glass-button" bindtap="onNotReady">我现在还不敢相信</button>
      </view>

      <view wx:if="{{responseLines.length > 0}}">
        <view wx:for="{{responseLines}}" wx:key="index" wx:if="{{visibleResponse[index]}}" class="text-line">
          {{item}}
        </view>
      </view>

      <view wx:if="{{showFinalButton}}" class="final-button">
        <button class="glass-button" bindtap="onNext">去看看你的微光吧</button>
      </view>

      <!-- 测试入口 -->
      <view class="test-entry">
        <button class="test-button" bindtap="goToTest">上传图片测试</button>
      </view>
    </view>
  </view>
</view>
