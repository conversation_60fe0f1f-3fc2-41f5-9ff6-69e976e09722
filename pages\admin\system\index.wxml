<!--系统管理页面-->
<view class="system-admin">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">系统管理</text>
    <view class="header-actions">
      <button class="refresh-btn" bindtap="onRefresh" disabled="{{isLoading}}">
        <text class="iconfont icon-refresh"></text>
      </button>
      <text class="last-update">{{formatTime(lastUpdate)}}</text>
    </view>
  </view>

  <!-- 标签页导航 -->
  <view class="tab-navigation">
    <view class="tab-item {{activeTab === 'overview' ? 'active' : ''}}" 
          data-tab="overview" bindtap="switchTab">
      <text>概览</text>
    </view>
    <view class="tab-item {{activeTab === 'performance' ? 'active' : ''}}" 
          data-tab="performance" bindtap="switchTab">
      <text>性能</text>
    </view>
    <view class="tab-item {{activeTab === 'deployment' ? 'active' : ''}}" 
          data-tab="deployment" bindtap="switchTab">
      <text>部署</text>
    </view>
    <view class="tab-item {{activeTab === 'testing' ? 'active' : ''}}" 
          data-tab="testing" bindtap="switchTab">
      <text>测试</text>
    </view>
  </view>

  <!-- 概览标签页 -->
  <view wx:if="{{activeTab === 'overview'}}" class="tab-content">
    <!-- 系统健康状态 -->
    <view class="status-card">
      <view class="card-header">
        <text class="card-title">系统健康</text>
        <view class="health-indicator {{systemHealth.status}}">
          <text class="health-score">{{systemHealth.score}}</text>
          <text class="health-status">{{systemHealth.status}}</text>
        </view>
      </view>
      
      <view class="health-metrics">
        <view class="metric-item">
          <text class="metric-label">运行时间</text>
          <text class="metric-value">{{formatDuration(systemHealth.uptime)}}</text>
        </view>
        <view class="metric-item">
          <text class="metric-label">帧率</text>
          <text class="metric-value">{{performanceMetrics.frameRate}}fps</text>
        </view>
        <view class="metric-item">
          <text class="metric-label">内存使用</text>
          <text class="metric-value">{{formatMemorySize(performanceMetrics.memoryUsage)}}</text>
        </view>
        <view class="metric-item">
          <text class="metric-label">响应时间</text>
          <text class="metric-value">{{performanceMetrics.responseTime}}ms</text>
        </view>
      </view>
    </view>

    <!-- 告警信息 -->
    <view class="alerts-card" wx:if="{{alerts.length > 0}}">
      <view class="card-header">
        <text class="card-title">系统告警 ({{alerts.length}})</text>
      </view>
      
      <view class="alerts-list">
        <view wx:for="{{alerts}}" wx:key="timestamp" class="alert-item {{item.level}}">
          <view class="alert-content">
            <text class="alert-type">{{item.type}}</text>
            <text class="alert-message">{{item.message}}</text>
          </view>
          <text class="alert-time">{{formatTime(item.timestamp)}}</text>
        </view>
      </view>
    </view>

    <!-- 快速操作 -->
    <view class="quick-actions">
      <button class="action-btn primary" bindtap="runSystemTests">
        运行测试
      </button>
      <button class="action-btn secondary" bindtap="clearSystemCache">
        清理缓存
      </button>
      <button class="action-btn secondary" bindtap="exportSystemReport">
        导出报告
      </button>
    </view>
  </view>

  <!-- 性能标签页 -->
  <view wx:if="{{activeTab === 'performance'}}" class="tab-content">
    <!-- 性能指标 -->
    <view class="performance-card">
      <view class="card-header">
        <text class="card-title">性能指标</text>
        <button class="run-benchmark-btn" bindtap="runPerformanceBenchmark">
          运行基准测试
        </button>
      </view>
      
      <view class="performance-metrics">
        <view class="metric-group">
          <text class="group-title">渲染性能</text>
          <view class="metric-item">
            <text class="metric-label">平均FPS</text>
            <text class="metric-value">{{benchmarkResults.averageRenderingFPS || 'N/A'}}</text>
          </view>
          <view class="metric-item">
            <text class="metric-label">渲染时间</text>
            <text class="metric-value">{{performanceMetrics.renderTime}}ms</text>
          </view>
        </view>
        
        <view class="metric-group">
          <text class="group-title">动画性能</text>
          <view class="metric-item">
            <text class="metric-label">动画FPS</text>
            <text class="metric-value">{{benchmarkResults.averageAnimationFPS || 'N/A'}}</text>
          </view>
          <view class="metric-item">
            <text class="metric-label">活跃动画</text>
            <text class="metric-value">{{performanceMetrics.activeAnimations || 0}}</text>
          </view>
        </view>
        
        <view class="metric-group">
          <text class="group-title">内存性能</text>
          <view class="metric-item">
            <text class="metric-label">内存效率</text>
            <text class="metric-value">{{benchmarkResults.memoryEfficiency || 'N/A'}}</text>
          </view>
          <view class="metric-item">
            <text class="metric-label">网络延迟</text>
            <text class="metric-value">{{performanceMetrics.networkLatency}}ms</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 性能模式设置 -->
    <view class="performance-modes">
      <view class="card-header">
        <text class="card-title">性能模式</text>
      </view>
      
      <view class="mode-buttons">
        <button class="mode-btn" data-mode="high" bindtap="setPerformanceMode">
          高性能
        </button>
        <button class="mode-btn" data-mode="balanced" bindtap="setPerformanceMode">
          平衡
        </button>
        <button class="mode-btn" data-mode="low" bindtap="setPerformanceMode">
          省电
        </button>
        <button class="mode-btn" data-mode="auto" bindtap="setPerformanceMode">
          自动
        </button>
      </view>
    </view>
  </view>

  <!-- 部署标签页 -->
  <view wx:if="{{activeTab === 'deployment'}}" class="tab-content">
    <!-- 部署状态 -->
    <view class="deployment-card">
      <view class="card-header">
        <text class="card-title">部署状态</text>
        <view class="deployment-status {{deploymentStatus.isDeploying ? 'deploying' : 'idle'}}">
          {{deploymentStatus.isDeploying ? '部署中' : '空闲'}}
        </view>
      </view>
      
      <view class="deployment-info">
        <view class="info-item">
          <text class="info-label">当前版本</text>
          <text class="info-value">{{deploymentStatus.currentVersion}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">环境</text>
          <text class="info-value">{{deploymentStatus.environment}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">回滚点</text>
          <text class="info-value">{{deploymentStatus.rollbackPoints}}个</text>
        </view>
      </view>
    </view>

    <!-- 功能开关 -->
    <view class="feature-flags-card">
      <view class="card-header">
        <text class="card-title">功能开关</text>
      </view>
      
      <view class="feature-flags-list">
        <view wx:for="{{featureFlags}}" wx:key="*this" class="feature-flag-item">
          <text class="flag-name">{{item[0]}}</text>
          <switch class="flag-switch" 
                 checked="{{item[1]}}" 
                 data-feature="{{item[0]}}" 
                 data-enabled="{{item[1]}}"
                 bindchange="toggleFeatureFlag" />
        </view>
      </view>
    </view>
  </view>

  <!-- 测试标签页 -->
  <view wx:if="{{activeTab === 'testing'}}" class="tab-content">
    <!-- 测试结果 -->
    <view class="testing-card">
      <view class="card-header">
        <text class="card-title">测试结果</text>
        <button class="run-tests-btn" bindtap="runSystemTests">
          运行测试
        </button>
      </view>
      
      <view class="test-summary">
        <view class="summary-item">
          <text class="summary-label">总测试数</text>
          <text class="summary-value">{{testResults.total}}</text>
        </view>
        <view class="summary-item success">
          <text class="summary-label">通过</text>
          <text class="summary-value">{{testResults.passed}}</text>
        </view>
        <view class="summary-item failed">
          <text class="summary-label">失败</text>
          <text class="summary-value">{{testResults.failed}}</text>
        </view>
        <view class="summary-item">
          <text class="summary-label">成功率</text>
          <text class="summary-value">{{testResults.successRate}}</text>
        </view>
      </view>
      
      <!-- 测试进度条 -->
      <view class="test-progress">
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{testResults.successRate}}"></view>
        </view>
        <text class="progress-text">{{testResults.successRate}}</text>
      </view>
    </view>

    <!-- 系统操作 -->
    <view class="system-operations">
      <view class="card-header">
        <text class="card-title">系统操作</text>
      </view>
      
      <view class="operation-buttons">
        <button class="operation-btn" bindtap="restartMonitoring">
          重启监控
        </button>
        <button class="operation-btn" bindtap="clearSystemCache">
          清理缓存
        </button>
        <button class="operation-btn" bindtap="exportSystemReport">
          导出报告
        </button>
      </view>
    </view>
  </view>

  <!-- 加载遮罩 -->
  <view wx:if="{{isLoading}}" class="loading-overlay">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>
</view>
