<view class="container">
  <!-- 背景 -->
  <image class="background" src="cloud://cloudbase-8gji862jcfb501e7.636c-cloudbase-8gji862jcfb501e7-1365531166/images/homeBG.png" mode="aspectFill" />
  <view class="background-mask"></view>

  <!-- 顶部进度条 -->
  <view class="progress-bar">
    <block wx:for="{{7}}" wx:key="index">
      <view class="progress-dot {{index < currentQuestionIndex ? 'active' : ''}}"></view>
    </block>
  </view>

  <!-- 核心内容区 -->
  <view class="content">
    <!-- 开篇介绍 -->
    <view class="intro-section fade-in" wx:if="{{step === 'intro'}}">
      <view class="intro-text">{{introText}}</view>
      <button class="start-btn" bindtap="startQuiz">我准备好了</button>
    </view>

    <!-- 问题卡片 -->
    <view class="quiz-section" wx:if="{{step === 'quiz'}}">
      <block wx:for="{{questions}}" wx:key="index">
        <view 
          class="question-card {{index === currentQuestionIndex ? 'current' : (index < currentQuestionIndex ? 'previous' : '')}}"
          wx:if="{{index === currentQuestionIndex || index === currentQuestionIndex - 1}}"
        >
          <view class="question-text">{{item.question}}</view>
          <view class="options-container">
            <block wx:for="{{item.options}}" wx:for-item="option" wx:key="optionIndex">
              <button 
                class="option-btn {{selectedOptionIndex === optionIndex ? 'selected' : ''}}"
                data-index="{{optionIndex}}"
                bindtap="selectOption"
              >
                <text class="option-label">{{option.label}}.</text>
                <text class="option-text">{{option.text}}</text>
              </button>
            </block>
          </view>
        </view>
      </block>
    </view>

    <!-- 完成页 -->
    <view class="outro-section fade-in" wx:if="{{step === 'outro'}}">
      <view class="outro-text">{{outroText}}</view>
      <button class="start-btn" bindtap="startDialogue">开启我们的对话</button>
    </view>
  </view>
</view>