:host {
    /* ✅ 质感升级：定义一个更优雅、更有层次感的微光金 */
    --gold-color: #EAD3A3;
    --gold-glow: rgba(234, 211, 163, 0.5);
    --text-color: #f5f0e8;
    --card-bg: linear-gradient(145deg, rgba(50, 45, 40, 0.5), rgba(30, 25, 20, 0.6));
  }
  
  .container {
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    position: relative;
  }
  
  .background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
    filter: brightness(0.6) blur(3px);
  }
  
  .background-mask {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at center, rgba(0,0,0,0.1), #0a0a14 90%);
    z-index: -1;
  }
  
  .progress-bar {
    position: fixed;
    top: 120rpx;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 20rpx;
    z-index: 10;
  }
  
  .progress-dot {
    width: 16rpx;
    height: 16rpx;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transition: all 0.5s ease;
  }
  
  .progress-dot.active {
    background-color: var(--gold-color);
    transform: scale(1.2);
    box-shadow: 0 0 12rpx var(--gold-glow);
  }
  
  .content {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 50rpx;
    box-sizing: border-box;
  }
  
  .fade-in {
    animation: fadeIn 1s ease-in-out forwards;
  }
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
  }
  
  .intro-section, .outro-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    color: var(--text-color);
  }
  
  .intro-text, .outro-text {
    font-size: 32rpx;
    line-height: 1.8;
    white-space: pre-wrap;
    margin-bottom: 80rpx;
  }
  
  /* ✅ 质感升级：启动按钮改为更有层次感的渐变微光金 */
  .start-btn {
    background: linear-gradient(180deg, #F5E6B8, #D4AF37); /* 从淡金到深金的渐变 */
    color: #6F4E00; /* 深琥珀色文字，更显高级 */
    font-weight: bold;
    border-radius: 50rpx;
    font-size: 32rpx;
    padding: 24rpx 80rpx;
    box-shadow: 0 4rpx 15rpx rgba(222, 184, 135, 0.4); /* 使用更柔和的阴影 */
    animation: breath 2.5s infinite ease-in-out;
    border: 1px solid rgba(255, 255, 255, 0.2); /* 增加一个细微的高光边 */
  }
  
  @keyframes breath {
    0%, 100% { transform: scale(1); box-shadow: 0 4rpx 15rpx rgba(222, 184, 135, 0.4); }
    50% { transform: scale(1.05); box-shadow: 0 8rpx 30rpx rgba(222, 184, 135, 0.6); }
  }
  
  .quiz-section {
    width: 100%;
    height: 100%;
    position: relative;
  }
  
  .question-card {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    transform: translate(-50%, -50%);
    background: var(--card-bg);
    backdrop-filter: blur(28px) saturate(180%);
    border-radius: 36rpx;
    padding: 56rpx;
    border: 1px solid rgba(234, 211, 163, 0.3);
    box-shadow: 0 16rpx 40rpx rgba(0,0,0,0.3);
    transition: all 0.7s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 0;
  }
  
  .question-card.current {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
    z-index: 2;
  }
  
  .question-card.previous {
    opacity: 0;
    transform: translate(-50%, -60%) scale(0.95);
    z-index: 1;
  }
  
  .question-text {
    font-size: 36rpx;
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 70rpx;
    line-height: 1.7;
    text-align: justify;
  }
  
  .options-container {
    display: flex;
    flex-direction: column;
    gap: 28rpx;
  }
  
  .option-btn {
    width: 100%;
    background-color: rgba(255, 255, 255, 0.08);
    color: var(--text-color);
    border: 1px solid rgba(234, 211, 163, 0.25);
    border-radius: 28rpx;
    padding: 32rpx;
    font-size: 28rpx;
    text-align: left;
    line-height: 1.5;
    transition: all 0.2s ease-in-out;
    display: flex;
    align-items: flex-start;
  }
  
  .option-btn::after {
    border: none;
  }
  
  .option-label {
    font-weight: bold;
    color: var(--gold-color);
    margin-right: 20rpx;
    opacity: 0.8;
  }
  
  .option-btn.selected {
    background-color: rgba(234, 211, 163, 0.2);
    border-color: var(--gold-color);
    transform: scale(1.02);
    box-shadow: 0 0 20rpx var(--gold-glow);
  }
  
  .option-btn.selected .option-text {
    color: #fff;
  }
  
  .option-btn.selected .option-label {
    color: var(--gold-color);
    opacity: 1;
  }