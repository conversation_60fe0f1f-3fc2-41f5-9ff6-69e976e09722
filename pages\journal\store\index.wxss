.store-container {
    background-color: #000;
    padding: 40rpx 24rpx 120rpx;
    color: #f0e6d2;
    max-width: 100vw;
    box-sizing: border-box;
  }
  
  /* 顶部信息栏 */
  .top-info-bar,
  .card,
  .card-img {
    width: 100%;
    box-sizing: border-box;
  }
  
  .top-info-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16rpx 24rpx;
    margin-bottom: 20rpx;
    background: rgba(255, 255, 255, 0.03);
    backdrop-filter: blur(2rpx);
    border-radius: 20rpx;
    border: 1rpx solid rgba(255, 255, 255, 0.06);
  }
  
  .info-block {
    display: flex;
    align-items: center;
    gap: 12rpx;
    padding: 8rpx 12rpx;
    border-radius: 16rpx;
  }
  
  .info-icon {
    width: 42rpx;
    height: 42rpx;
    border-radius: 50%;
    box-shadow: 0 0 12rpx #ffd27f;
    background: radial-gradient(circle, rgba(255, 210, 127, 0.3), transparent 70%);
  }
  
  @keyframes pulse {
    0%   { transform: scale(1);    opacity: 1; }
    50%  { transform: scale(1.08); opacity: 0.92; }
    100% { transform: scale(1);    opacity: 1; }
  }
  
  .pulse {
    animation: pulse 3s ease-in-out infinite;
  }
  
  .info-text {
    font-size: 24rpx;
    color: #ffe399;
    font-weight: 500;
  }
  
  /* slogan & 页尾祝福语 */
  .slogan,
  .page-end {
    font-size: 28rpx;
    font-weight: 400;
    color: #fff8e8;
    text-align: center;
    letter-spacing: 2rpx;
    text-shadow: 0 0 14rpx rgba(255, 240, 200, 0.5);
  }
  
  .slogan-wrapper {
    position: relative;
    margin-bottom: 40rpx;
    text-align: center;
  }
  
  .slogan-background {
    position: absolute;
    width: 80%;
    height: 100%;
    top: 0;
    left: 10%;
    background: radial-gradient(circle at center, rgba(255, 230, 170, 0.15), transparent 70%);
    border-radius: 40rpx;
    z-index: -1;
  }
  
  /* 卡片基础样式 */
  .card {
    background: linear-gradient(145deg, #0b0b0b, #111111);
    border-radius: 24rpx;
    padding: 20rpx;
    margin-bottom: 48rpx;
    box-shadow: 0 0 12rpx rgba(255, 222, 150, 0.08);
    transition: transform 0.2s ease;
    position: relative;
  }
  
  .card:hover {
    transform: scale(1.02);
    box-shadow: 0 0 18rpx rgba(255, 222, 150, 0.25);
  }
  
  .card-divider {
    width: 100%;
    height: 2rpx;
    background: linear-gradient(to right, #ffc973, transparent);
    border-radius: 1rpx;
    margin-bottom: 16rpx;
  }
  
  .card-img {
    width: 100%;
    border-radius: 20rpx;
    margin-bottom: 20rpx;
  }
  
  .glow-border {
    box-shadow: 0 0 18rpx rgba(255, 230, 160, 0.3);
  }
  
  /* 大号角标 */
  .corner-tag.glow-tag.large-tag {
    position: absolute;
    top: 20rpx;
    left: 20rpx;
    font-size: 28rpx;
    padding: 8rpx 24rpx;
    background: #1e1200;
    color: #fff8e8;
    font-weight: bold;
    border-radius: 40rpx;
    border: 1rpx solid rgba(255, 255, 255, 0.1);
    text-shadow: 0 0 10rpx rgba(255, 255, 255, 0.4);
    box-shadow: 0 0 16rpx rgba(255, 215, 130, 0.4);
    letter-spacing: 1rpx;
    z-index: 2;
  }
  
  /* 副文案发光 */
  .glow-desc {
    font-size: 24rpx;
    color: #ffeac4;
    font-style: normal;
    font-weight: 300;
    letter-spacing: 1rpx;
    margin-bottom: 10rpx;
    text-shadow: 0 0 10rpx rgba(255, 230, 180, 0.4);
  }
  
  /* 标题发光 */
  .glow-text {
    font-size: 36rpx;
    font-weight: bold;
    color: #fff8e8;
    margin-bottom: 16rpx;
    letter-spacing: 2rpx;
    text-shadow: 0 0 10rpx rgba(255, 235, 180, 0.5);
  }
  
  /* 价格 */
  .card-price {
    font-size: 26rpx;
    color: #ffc66d;
    font-weight: 500;
    margin: 20rpx 0 24rpx;
    text-align: right;
  }
  
  /* 父容器居中按钮 */
  .card-content {
    text-align: center;
  }
  
  /* 居中发光按钮 + 动效 */
  .card-button {
    display: inline-block;
    padding: 20rpx 40rpx;
    font-size: 30rpx;
    font-weight: bold;
    background: linear-gradient(145deg, #3a2600, #ffcc88);
    color: #fffaf0;
    border-radius: 36rpx;
    box-shadow:
      0 0 14rpx rgba(255, 222, 150, 0.4),
      inset 0 0 10rpx rgba(255, 248, 220, 0.4);
    transition: all 0.2s ease-in-out;
    max-width: 80%;
    white-space: nowrap;
    animation: pulse 3s infinite ease-in-out;
  }
  
  .card-button:active {
    transform: scale(0.98);
  }
  
  /* 卡片排布 */
  .card-row {
    display: flex;
    justify-content: space-between;
    gap: 20rpx;
  }
  .small-card {
    flex: 1;
  }
  .big-card {
    width: 100%;
  }
  button::after {
    border: none;
  }
  
  /* 主卡片发光加强 */
  .primary-card {
    border: 1rpx solid rgba(255, 225, 150, 0.4);
    box-shadow: 0 0 28rpx rgba(255, 230, 150, 0.3);
    position: relative;
  }

.journal-container {
  padding: 20rpx;
  background-color: #f4f4f4;
  min-height: 100vh;
}

.header {
  display: flex;
  flex-direction: column;
  margin-bottom: 20rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.today-reminder {
  font-size: 28rpx;
  color: #666;
  background-color: #fff3cd;
  padding: 10rpx;
  border-radius: 8rpx;
}

.diary-list {
  max-height: calc(100vh - 100rpx);
}

.diary-item {
  background-color: white;
  border-radius: 15rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 6rpx rgba(0, 0, 0, 0.1);
  position: relative;
}

.diary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
  border-bottom: 1rpx solid #f0f0f0;
  padding-bottom: 10rpx;
}

.diary-theme {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.diary-date {
  font-size: 24rpx;
  color: #999;
}

.diary-summary, .diary-emotion, .diary-tomorrow-message {
  margin-bottom: 15rpx;
  padding: 10rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
}

.summary-label, .emotion-label, .message-label {
  font-weight: bold;
  color: #666;
  margin-right: 10rpx;
}

.summary-text, .emotion-text, .message-text {
  color: #333;
}

.vip-badge {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  background-color: #FFD700;
  color: white;
  padding: 5rpx 10rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
}          