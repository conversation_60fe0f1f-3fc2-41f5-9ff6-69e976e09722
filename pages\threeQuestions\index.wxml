<view class="three-questions-container">
  <image 
    class="background" 
    src="cloud://cloudbase-8gji862jcfb501e7.636c-cloudbase-8gji862jcfb501e7-1365531166/images/homeBG.png" 
    mode="aspectFill" 
  />

  <!-- 反馈消息 -->
  <view class="feedback-message {{showFeedback ? 'show' : ''}} feedback-{{feedbackType}}" wx:if="{{showFeedback}}">
    <view class="feedback-content">
      <text class="feedback-icon">
        {{feedbackType === 'success' ? '✓' : feedbackType === 'error' ? '✗' : 'ℹ'}}
      </text>
      <text class="feedback-text">{{feedbackMessage}}</text>
    </view>
  </view>

  <!-- 页面头部 -->
  <view class="header">
    <text class="theme">{{dialogueTheme}} - 三问仪式</text>
    <!-- 自动保存状态指示 -->
    <view class="auto-save-indicator" wx:if="{{lastSaveTime}}">
      <text class="save-time">已保存 {{lastSaveTime}}</text>
    </view>
  </view>

  <!-- 对话总结 -->
  <view class="summary-container" wx:if="{{dialogueSummary}}">
    <text class="summary-title">对话总结</text>
    <text class="summary-text">{{dialogueSummary}}</text>
  </view>

  <!-- 问题容器 -->
  <view class="question-container">
    <!-- 问题标题 -->
    <view class="question-header">
      <text class="question-title {{isTransitioning ? 'transitioning' : ''}}">{{questions[currentQuestionIndex].title}}</text>
      <view class="progress-container" bindtap="toggleProgressDetail">
        <view 
          wx:for="{{questions}}" 
          wx:key="id" 
          class="progress-dot {{index <= currentQuestionIndex ? 'active' : ''}} {{index === currentQuestionIndex ? 'current' : ''}}"
        ></view>
        <!-- 详细进度显示 -->
        <view class="progress-detail" wx:if="{{showProgressDetail}}">
          <text class="progress-text">{{currentQuestionIndex + 1}}/{{totalQuestions}}</text>
        </view>
      </view>
    </view>
    
    <!-- 问题内容 -->
    <text class="question-text">{{questions[currentQuestionIndex].text}}</text>
    
    <!-- 第一问和第二问：文本输入 -->
    <view wx:if="{{questions[currentQuestionIndex].type !== 'diary'}}" class="input-section {{inputFocused ? 'focused' : ''}}">
      <!-- 输入容器 -->
      <view class="input-container {{isTransitioning ? 'transitioning' : ''}}">
        <textarea 
          class="answer-input {{questions[currentQuestionIndex].type === 'feeling' && inputErrors.feeling ? 'input-error' : ''}} {{inputFocused ? 'focused' : ''}}" 
          placeholder="{{questions[currentQuestionIndex].placeholder}}" 
          value="{{questions[currentQuestionIndex].type === 'feeling' ? answers.feeling : answers.message}}"
          bindinput="onInputChange"
          bindfocus="onInputFocus"
          bindblur="onInputBlur"
          maxlength="{{questions[currentQuestionIndex].maxLength}}"
          auto-focus="{{currentQuestionIndex === 0}}"
          cursor-spacing="20"
          show-confirm-bar="{{false}}"
          adjust-position="{{true}}"
        />
        
        <!-- 输入增强提示 -->
        <view class="input-enhancement" wx:if="{{inputFocused}}">
          <view class="enhancement-tips">
            <text class="tip-text">💡 输入时会自动保存</text>
          </view>
        </view>
      </view>
      
      <!-- 错误信息显示 -->
      <view wx:if="{{questions[currentQuestionIndex].type === 'feeling' && inputErrors.feeling}}" class="error-message slide-in">
        <text class="error-icon">⚠️</text>
        <text class="error-text">{{inputErrors.feeling}}</text>
      </view>
      <view wx:if="{{questions[currentQuestionIndex].type === 'message' && inputErrors.message}}" class="error-message slide-in">
        <text class="error-icon">⚠️</text>
        <text class="error-text">{{inputErrors.message}}</text>
      </view>
      
      <!-- 字数统计和状态 -->
      <view class="input-status">
        <view class="char-count">
          <text class="count-text {{questions[currentQuestionIndex].type === 'feeling' && answers.feeling.length > questions[currentQuestionIndex].maxLength ? 'count-error' : ''}}">
            {{questions[currentQuestionIndex].type === 'feeling' ? answers.feeling.length : answers.message.length}}/{{questions[currentQuestionIndex].maxLength}}
          </text>
        </view>
        <!-- 输入状态指示 -->
        <view class="input-indicator" wx:if="{{inputFocused}}">
          <view class="indicator-dot active"></view>
          <text class="indicator-text">正在输入</text>
        </view>
      </view>
      
      <!-- 操作按钮 -->
      <view class="action-buttons {{operationInProgress ? 'disabled' : ''}}">
        <button 
          class="skip-btn {{operationInProgress ? 'loading' : ''}}" 
          bindtap="skipQuestion"
          wx:if="{{canSkip}}"
          disabled="{{operationInProgress}}"
        >
          <view class="btn-content">
            <text class="btn-text">跳过</text>
            <view class="btn-loading" wx:if="{{operationInProgress}}">
              <view class="loading-spinner"></view>
            </view>
          </view>
        </button>
        <button 
          class="next-btn {{operationInProgress ? 'loading' : ''}}" 
          bindtap="nextQuestion"
          disabled="{{operationInProgress}}"
        >
          <view class="btn-content">
            <text class="btn-text">{{currentQuestionIndex < totalQuestions - 1 ? '下一问' : '完成'}}</text>
            <view class="btn-loading" wx:if="{{operationInProgress}}">
              <view class="loading-spinner"></view>
            </view>
          </view>
        </button>
      </view>
    </view>

    <!-- 第三问：日记生成选择 -->
    <view wx:if="{{questions[currentQuestionIndex].type === 'diary'}}" class="diary-section">
      <!-- 日记生成中状态 -->
      <view wx:if="{{isGeneratingDiary}}" class="generating-diary">
        <view class="loading-animation">
          <view class="loading-dots">
            <view class="dot dot1"></view>
            <view class="dot dot2"></view>
            <view class="dot dot3"></view>
          </view>
          <text class="loading-text">AI正在为你生成专属日记...</text>
          <text class="loading-subtitle">请稍候，这可能需要几秒钟</text>
        </view>
      </view>
      
      <!-- 已生成日记显示 -->
      <view wx:elif="{{generatedDiary}}" class="generated-diary">
        <view class="diary-header">
          <image class="diary-icon" src="cloud://cloudbase-8gji862jcfb501e7.636c-cloudbase-8gji862jcfb501e7-1365531166/images/diary-icon.png" mode="aspectFit" />
          <text class="diary-title">你的专属日记</text>
        </view>
        <view class="diary-content">
          <text class="diary-text">{{generatedDiary}}</text>
        </view>
        <view class="diary-actions">
          <button class="save-diary-btn" bindtap="saveDiaryToLocal">
            <text class="save-icon">💾</text>
            <text>保存日记</text>
          </button>
          <button class="complete-btn" bindtap="{{showLightUpButton ? 'lightUpStar' : 'completeThreeQuestions'}}">
            {{showLightUpButton ? '点亮星星 ⭐' : '点亮星星'}}
          </button>
        </view>
      </view>
      
      <!-- 日记生成选择 -->
      <view wx:else class="diary-choices">
        <view class="diary-intro">
          <text class="intro-text">AI将根据你们的对话内容，生成一篇专属于你的个人日记</text>
        </view>
        
        <view class="choice-buttons">
          <button 
            class="generate-diary-btn {{!isVip ? 'vip-required' : ''}}" 
            data-choice="generate"
            bindtap="handleDiaryChoice"
          >
            <view class="btn-content">
              <text class="btn-icon">✨</text>
              <view class="btn-text">
                <text class="btn-title">立即生成</text>
                <text class="btn-subtitle" wx:if="{{!isVip}}">VIP 专属功能</text>
                <text class="btn-subtitle" wx:else>个性化AI日记</text>
              </view>
            </view>
            <view wx:if="{{!isVip}}" class="vip-badge">VIP</view>
          </button>
          
          <button 
            class="skip-diary-btn" 
            data-choice="skip"
            bindtap="handleDiaryChoice"
          >
            <text class="skip-icon">⭐</text>
            <text>直接点亮星星</text>
          </button>
        </view>
        
        <!-- VIP特权说明 -->
        <view wx:if="{{!isVip}}" class="vip-benefits">
          <text class="benefits-title">VIP专属特权</text>
          <view class="benefits-list">
            <view class="benefit-item">
              <text class="benefit-icon">📝</text>
              <text class="benefit-text">AI个性化日记生成</text>
            </view>
            <view class="benefit-item">
              <text class="benefit-icon">💎</text>
              <text class="benefit-text">每日免费光点奖励</text>
            </view>
            <view class="benefit-item">
              <text class="benefit-icon">🎮</text>
              <text class="benefit-text">专属游戏内容</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 点亮星星按钮区域 -->
  <view wx:if="{{showLightUpButton}}" class="light-up-section">
    <text class="light-up-title">🌟 三问仪式完成 🌟</text>
    <text class="light-up-description">
      感谢你完成了今天的三问仪式，现在让我们一起点亮属于你的星星吧！
    </text>
    <button
      class="light-up-btn {{isLightingUp ? 'lighting' : ''}}"
      bindtap="lightUpStar"
      disabled="{{isLightingUp}}"
      style="z-index: 9999; position: relative;"
    >
      <text class="star-icon">⭐</text>
      <text>{{isLightingUp ? '正在点亮...' : '点亮星星'}}</text>
    </button>

    <!-- 调试信息 -->
    <view style="margin-top: 20rpx; font-size: 24rpx; color: #666;">
      调试信息: showLightUpButton={{showLightUpButton}}, isLightingUp={{isLightingUp}}
    </view>

    <!-- 测试按钮 -->
    <button
      style="margin-top: 20rpx; background: red; color: white; padding: 20rpx;"
      bindtap="testButtonClick"
    >
      测试按钮点击
    </button>
  </view>

  <!-- AnimationEngine 闪光效果覆盖层 -->
  <view class="flash-overlay" wx:if="{{flashLayer0 || flashLayer1 || flashLayer2}}">
    <view 
      class="flash-layer-0 {{flashActive0 ? 'flash-active-0' : ''}}" 
      wx:if="{{flashLayer0}}"
    ></view>
    <view 
      class="flash-layer-1 {{flashActive1 ? 'flash-active-1' : ''}}" 
      wx:if="{{flashLayer1}}"
    ></view>
    <view 
      class="flash-layer-2 {{flashActive2 ? 'flash-active-2' : ''}}" 
      wx:if="{{flashLayer2}}"
    ></view>
  </view>
</view> 