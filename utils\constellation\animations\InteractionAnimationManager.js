/**
 * 交互动画管理器
 * 负责处理用户交互相关的动画效果
 */

class InteractionAnimationManager {
    constructor() {
        this.activeInteractions = new Map()
        this.touchAnimations = new Map()
        this.hoverEffects = new Map()
        
        // 交互动画配置
        this.config = {
            starHover: {
                duration: 200,
                scale: { from: 1, to: 1.2 },
                glow: { from: 1, to: 1.5 },
                easing: 'easeOutQuart'
            },
            starTap: {
                duration: 300,
                scale: { from: 1, to: 0.9, to2: 1.1 },
                rotation: { from: 0, to: 15, to2: 0 },
                easing: 'easeOutElastic'
            },
            ripple: {
                duration: 600,
                scale: { from: 0, to: 3 },
                opacity: { from: 0.8, to: 0 },
                easing: 'easeOutQuart'
            },
            dragPreview: {
                duration: 150,
                scale: { from: 1, to: 1.3 },
                opacity: { from: 1, to: 0.8 },
                easing: 'easeOutQuart'
            },
            placement: {
                duration: 400,
                scale: { from: 1.5, to: 1 },
                bounce: 0.3,
                easing: 'easeOutBack'
            }
        }
    }

    /**
     * 初始化交互动画管理器
     */
    initialize() {
        console.log('初始化交互动画管理器...')
        this.setupGlobalListeners()
        return { success: true }
    }

    /**
     * 设置全局事件监听器
     */
    setupGlobalListeners() {
        // 这里可以设置全局的触摸和鼠标事件监听
        // 在小程序环境中，主要通过页面的事件处理函数来调用
    }

    /**
     * 创建星星悬停效果
     */
    createStarHoverEffect(starElement, options = {}) {
        const config = { ...this.config.starHover, ...options }
        const effectId = `hover_${Date.now()}_${Math.random()}`

        // 停止之前的悬停效果
        this.stopElementHoverEffects(starElement)

        const animation = this.createAnimation({
            element: starElement,
            duration: config.duration,
            easing: config.easing,
            properties: {
                scale: config.scale,
                glow: config.glow
            },
            onUpdate: (properties) => {
                this.applyHoverStyles(starElement, properties)
            },
            onComplete: () => {
                if (options.onComplete) {
                    options.onComplete()
                }
            }
        })

        this.hoverEffects.set(effectId, animation)
        return effectId
    }

    /**
     * 移除星星悬停效果
     */
    removeStarHoverEffect(starElement, options = {}) {
        const config = { ...this.config.starHover, ...options }
        
        // 反向动画回到原始状态
        const animation = this.createAnimation({
            element: starElement,
            duration: config.duration,
            easing: config.easing,
            properties: {
                scale: { from: 1.2, to: 1 },
                glow: { from: 1.5, to: 1 }
            },
            onUpdate: (properties) => {
                this.applyHoverStyles(starElement, properties)
            },
            onComplete: () => {
                this.stopElementHoverEffects(starElement)
                if (options.onComplete) {
                    options.onComplete()
                }
            }
        })

        return animation
    }

    /**
     * 创建星星点击效果
     */
    createStarTapEffect(starElement, options = {}) {
        const config = { ...this.config.starTap, ...options }
        const effectId = `tap_${Date.now()}_${Math.random()}`

        // 创建点击波纹效果
        this.createRippleEffect(starElement, {
            color: options.color || '#FFFFFF',
            size: options.rippleSize || 50
        })

        // 创建星星缩放和旋转动画
        const animation = this.createMultiStageAnimation({
            element: starElement,
            stages: [
                {
                    duration: config.duration * 0.4,
                    properties: {
                        scale: { from: 1, to: config.scale.to },
                        rotation: { from: 0, to: config.rotation.to }
                    }
                },
                {
                    duration: config.duration * 0.6,
                    properties: {
                        scale: { from: config.scale.to, to: config.scale.to2 },
                        rotation: { from: config.rotation.to, to: config.rotation.to2 }
                    }
                }
            ],
            easing: config.easing,
            onUpdate: (properties) => {
                this.applyTapStyles(starElement, properties)
            },
            onComplete: () => {
                if (options.onComplete) {
                    options.onComplete()
                }
            }
        })

        this.touchAnimations.set(effectId, animation)
        return effectId
    }

    /**
     * 创建波纹效果
     */
    createRippleEffect(targetElement, options = {}) {
        const config = { ...this.config.ripple, ...options }
        const rippleId = `ripple_${Date.now()}_${Math.random()}`

        // 创建波纹元素（在实际实现中，这需要通过页面的setData来创建）
        const rippleData = {
            id: rippleId,
            x: options.x || 50, // 百分比位置
            y: options.y || 50,
            color: options.color || '#FFFFFF',
            size: options.size || 50
        }

        const animation = this.createAnimation({
            element: null, // 波纹元素需要在页面中动态创建
            duration: config.duration,
            easing: config.easing,
            properties: {
                scale: config.scale,
                opacity: config.opacity
            },
            onUpdate: (properties) => {
                // 通过回调更新波纹样式
                if (options.onUpdate) {
                    options.onUpdate(rippleId, properties)
                }
            },
            onComplete: () => {
                // 移除波纹元素
                if (options.onComplete) {
                    options.onComplete(rippleId)
                }
            }
        })

        return { rippleId, animation }
    }

    /**
     * 创建拖拽预览效果
     */
    createDragPreviewEffect(starElement, options = {}) {
        const config = { ...this.config.dragPreview, ...options }
        const effectId = `drag_${Date.now()}_${Math.random()}`

        const animation = this.createAnimation({
            element: starElement,
            duration: config.duration,
            easing: config.easing,
            properties: {
                scale: config.scale,
                opacity: config.opacity
            },
            onUpdate: (properties) => {
                this.applyDragStyles(starElement, properties)
            },
            onComplete: () => {
                if (options.onComplete) {
                    options.onComplete()
                }
            }
        })

        this.activeInteractions.set(effectId, animation)
        return effectId
    }

    /**
     * 创建放置动画效果
     */
    createPlacementEffect(starElement, options = {}) {
        const config = { ...this.config.placement, ...options }
        const effectId = `placement_${Date.now()}_${Math.random()}`

        const animation = this.createAnimation({
            element: starElement,
            duration: config.duration,
            easing: config.easing,
            properties: {
                scale: config.scale
            },
            onUpdate: (properties) => {
                this.applyPlacementStyles(starElement, properties)
            },
            onComplete: () => {
                if (options.onComplete) {
                    options.onComplete()
                }
            }
        })

        this.activeInteractions.set(effectId, animation)
        return effectId
    }

    /**
     * 创建基础动画
     */
    createAnimation(config) {
        const animation = {
            element: config.element,
            duration: config.duration,
            easing: config.easing,
            properties: config.properties,
            onUpdate: config.onUpdate,
            onComplete: config.onComplete,
            startTime: null,
            currentProperties: {}
        }

        const animate = (timestamp) => {
            if (!animation.startTime) {
                animation.startTime = timestamp
            }

            const elapsed = timestamp - animation.startTime
            const progress = Math.min(elapsed / animation.duration, 1)
            const easedProgress = this.applyEasing(progress, animation.easing)

            // 更新属性
            for (const [property, config] of Object.entries(animation.properties)) {
                if (config.from !== undefined && config.to !== undefined) {
                    animation.currentProperties[property] = this.interpolate(
                        config.from, 
                        config.to, 
                        easedProgress
                    )
                }
            }

            // 调用更新回调
            if (animation.onUpdate) {
                animation.onUpdate(animation.currentProperties, easedProgress)
            }

            // 检查是否完成
            if (progress >= 1) {
                if (animation.onComplete) {
                    animation.onComplete(animation.currentProperties)
                }
            } else {
                animation.animationId = requestAnimationFrame(animate)
            }
        }

        animation.animationId = requestAnimationFrame(animate)
        return animation
    }

    /**
     * 创建多阶段动画
     */
    createMultiStageAnimation(config) {
        let currentStage = 0
        const stages = config.stages

        const runNextStage = () => {
            if (currentStage >= stages.length) {
                if (config.onComplete) {
                    config.onComplete()
                }
                return
            }

            const stage = stages[currentStage]
            const animation = this.createAnimation({
                element: config.element,
                duration: stage.duration,
                easing: config.easing,
                properties: stage.properties,
                onUpdate: config.onUpdate,
                onComplete: () => {
                    currentStage++
                    runNextStage()
                }
            })

            return animation
        }

        return runNextStage()
    }

    /**
     * 应用悬停样式
     */
    applyHoverStyles(element, properties) {
        if (!element) return

        const transforms = []
        
        if (properties.scale !== undefined) {
            transforms.push(`scale(${properties.scale})`)
        }

        if (transforms.length > 0) {
            element.style.transform = transforms.join(' ')
        }

        if (properties.glow !== undefined) {
            const currentBoxShadow = element.style.boxShadow || ''
            // 增强发光效果
            element.style.filter = `brightness(${properties.glow})`
        }
    }

    /**
     * 应用点击样式
     */
    applyTapStyles(element, properties) {
        if (!element) return

        const transforms = []
        
        if (properties.scale !== undefined) {
            transforms.push(`scale(${properties.scale})`)
        }

        if (properties.rotation !== undefined) {
            transforms.push(`rotate(${properties.rotation}deg)`)
        }

        if (transforms.length > 0) {
            element.style.transform = transforms.join(' ')
        }
    }

    /**
     * 应用拖拽样式
     */
    applyDragStyles(element, properties) {
        if (!element) return

        const transforms = []
        
        if (properties.scale !== undefined) {
            transforms.push(`scale(${properties.scale})`)
        }

        if (transforms.length > 0) {
            element.style.transform = transforms.join(' ')
        }

        if (properties.opacity !== undefined) {
            element.style.opacity = properties.opacity
        }

        // 添加拖拽时的阴影效果
        element.style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.3)'
        element.style.zIndex = '1000'
    }

    /**
     * 应用放置样式
     */
    applyPlacementStyles(element, properties) {
        if (!element) return

        const transforms = []
        
        if (properties.scale !== undefined) {
            transforms.push(`scale(${properties.scale})`)
        }

        if (transforms.length > 0) {
            element.style.transform = transforms.join(' ')
        }
    }

    /**
     * 线性插值
     */
    interpolate(from, to, progress) {
        return from + (to - from) * progress
    }

    /**
     * 应用缓动函数
     */
    applyEasing(progress, easing) {
        switch (easing) {
            case 'easeOutQuart':
                return 1 - Math.pow(1 - progress, 4)
            
            case 'easeOutElastic':
                const c4 = (2 * Math.PI) / 3
                return progress === 0 ? 0 : progress === 1 ? 1 :
                    Math.pow(2, -10 * progress) * Math.sin((progress * 10 - 0.75) * c4) + 1
            
            case 'easeOutBack':
                const c1 = 1.70158
                const c3 = c1 + 1
                return 1 + c3 * Math.pow(progress - 1, 3) + c1 * Math.pow(progress - 1, 2)
            
            default:
                return progress
        }
    }

    /**
     * 停止元素的悬停效果
     */
    stopElementHoverEffects(element) {
        const effectsToStop = []
        
        for (const [effectId, animation] of this.hoverEffects) {
            if (animation.element === element) {
                effectsToStop.push(effectId)
            }
        }

        effectsToStop.forEach(id => {
            const animation = this.hoverEffects.get(id)
            if (animation.animationId) {
                cancelAnimationFrame(animation.animationId)
            }
            this.hoverEffects.delete(id)
        })

        return effectsToStop.length
    }

    /**
     * 停止指定交互动画
     */
    stopInteraction(interactionId) {
        if (this.activeInteractions.has(interactionId)) {
            const animation = this.activeInteractions.get(interactionId)
            if (animation.animationId) {
                cancelAnimationFrame(animation.animationId)
            }
            this.activeInteractions.delete(interactionId)
            return true
        }
        return false
    }

    /**
     * 停止所有交互动画
     */
    stopAllInteractions() {
        const count = this.activeInteractions.size + this.touchAnimations.size + this.hoverEffects.size
        
        // 停止所有动画
        for (const [id, animation] of this.activeInteractions) {
            if (animation.animationId) {
                cancelAnimationFrame(animation.animationId)
            }
        }
        
        for (const [id, animation] of this.touchAnimations) {
            if (animation.animationId) {
                cancelAnimationFrame(animation.animationId)
            }
        }
        
        for (const [id, animation] of this.hoverEffects) {
            if (animation.animationId) {
                cancelAnimationFrame(animation.animationId)
            }
        }

        this.activeInteractions.clear()
        this.touchAnimations.clear()
        this.hoverEffects.clear()

        return count
    }

    /**
     * 获取交互状态
     */
    getInteractionStatus() {
        return {
            activeInteractions: this.activeInteractions.size,
            touchAnimations: this.touchAnimations.size,
            hoverEffects: this.hoverEffects.size,
            total: this.activeInteractions.size + this.touchAnimations.size + this.hoverEffects.size
        }
    }

    /**
     * 销毁交互动画管理器
     */
    destroy() {
        this.stopAllInteractions()
        console.log('交互动画管理器已销毁')
    }
}

// 创建全局实例
const interactionAnimationManager = new InteractionAnimationManager()

export default interactionAnimationManager
