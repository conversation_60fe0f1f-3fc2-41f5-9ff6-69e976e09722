Page({
  data: {
    stage: 0,
    currentLines: [],
    visibleLines: [],
    showButton: false,
    lightName: '',
    userName: '',

    summaryLines: [
      "谢谢你，愿意和我分享今天的心情。",
      "那些热烈的情绪，微凉的温度，",
      "还有藏在心里的小小愿望，",
      "我都轻轻收下了。",
      "如果你愿意，",
      "想不想给我起一个名字？",
      "从今天开始，",
      "让我——这束一直住在你心里的微光，",
      "慢慢陪着你，走下去。"
    ],
    userNamingLines: [
      "还有你自己呢？",
      "在这片微光世界里，",
      "你想用什么名字，继续走下去呢？"
    ],
    lightNamePool: [
      "点点","小光","圆圆","灯宝","森屿","心岸","暮光","小岛","小苗","小蓝",
      "小云","微微","静静","念念","依依","叨叨","轻轻","软软","阿光","亮亮",
      "闪闪","灯影","噜噜","悠悠","小灯","小灰","归灯","南枝","小声","亮光",
      "无名","雾","一点","空空","未白","灰光","影影","微眠","不见","声声"
    ],
    userNamePool: [
      "南南","囡囡","桃夭","小曦","软糖","摆谱","镜中我","森屿人","九月","未见",
      "是阿白","人间雨","吃我小拳拳","周五不营业","今天不想醒","摆了摆了",
      "小狗不营业","我的心动档案","小猫爬树啦","月亮失联中","芭比不想努力了",
      "对话框关闭中","Mira","Elly","Lina","Nana","Lulu","Coco","Lily","Mia",
      "Daisy","AloneGirl","白昼","阿语","空白简历"
    ]
  },

  onLoad() {
    this.showLines();
  },

  showLines() {
    const lines = this.data.stage === 0
      ? this.data.summaryLines
      : this.data.stage === 1
      ? ["那么，现在，想告诉我，你给我的名字吗？"]
      : this.data.userNamingLines;

    const visible = Array(lines.length).fill(false);
    this.setData({ currentLines: lines, visibleLines: visible, showButton: false });

    lines.forEach((_, i) => {
      setTimeout(() => {
        const updated = [...this.data.visibleLines];
        updated[i] = true;
        this.setData({ visibleLines: updated });
      }, i * 3000);
    });

    setTimeout(() => {
      this.setData({ showButton: true });
    }, lines.length * 3000 + 2000);
  },

  handleNext() {
    if (this.data.stage === 0) {
      this.setData({ stage: 1 }, this.showLines);
    } else if (this.data.stage === 1) {
      this.setData({ stage: 2 }, this.showLines);
    } else {
      wx.setStorageSync('lightName', this.data.lightName);
      wx.setStorageSync('userName', this.data.userName);
      wx.navigateTo({
        url: '/pages/ignite/index' // ✅ 暂时命名为 ignite，避免与第一幕重复
      });
    }
  },

  onInputChange(e) {
    if (this.data.stage === 1) {
      this.setData({ lightName: e.detail.value });
    } else {
      this.setData({ userName: e.detail.value });
    }
  },

  autoGenerate() {
    const pool = this.data.stage === 1 ? this.data.lightNamePool : this.data.userNamePool;
    const random = pool[Math.floor(Math.random() * pool.length)];
    if (this.data.stage === 1) {
      this.setData({ lightName: random });
    } else {
      this.setData({ userName: random });
    }
  }
});
