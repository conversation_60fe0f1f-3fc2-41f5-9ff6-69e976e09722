<view class="container">
  <image class="bg" src="cloud://cloudbase-8gji862jcfb501e7.636c-cloudbase-8gji862jcfb501e7-1365531166/images/welcomebg.png" mode="aspectFill" />
  
  <view class="content">
    <view class="title">微光</view>
    <view class="subtitle">遇见内心的光</view>

    <view class="login-area" wx:if="{{!hasUserInfo}}">
      <button 
        class="login-btn" 
        type="primary" 
        bindtap="handleUserLogin"
      >开启微光之旅</button>
      <view class="tips">登录后即可开始探索内心世界</view>
    </view>

    <view class="user-info" wx:else>
      <image class="avatar" src="{{userInfo.userInfo.avatarUrl}}" mode="aspectFill"></image>
      <view class="nickname">{{userInfo.userInfo.nickName}}</view>
      <view class="welcome-back">欢迎回来</view>
    </view>
  </view>
</view>