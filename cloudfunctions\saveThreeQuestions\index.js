const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 错误类型定义
const ERROR_TYPES = {
  VALIDATION_ERROR: 'validation_error',
  DATABASE_ERROR: 'database_error',
  NETWORK_ERROR: 'network_error',
  AI_SERVICE_ERROR: 'ai_service_error',
  SYSTEM_ERROR: 'system_error',
  TIMEOUT_ERROR: 'timeout_error'
}

// 重试配置
const RETRY_CONFIG = {
  maxRetries: 3,
  baseDelay: 1000,
  maxDelay: 10000,
  backoffFactor: 2
}

// 超时配置
const TIMEOUT_CONFIG = {
  database: 10000,    // 数据库操作超时 10秒
  aiService: 15000,   // AI服务超时 15秒
  messageSchedule: 8000 // 消息调度超时 8秒
}

// 错误处理工具函数
function createErrorResponse(errorType, message, details = {}) {
  return {
    success: false,
    error: message,
    errorType,
    timestamp: new Date().toISOString(),
    details,
    retryable: isRetryableError(errorType)
  }
}

// 判断错误是否可重试
function isRetryableError(errorType) {
  const retryableErrors = [
    ERROR_TYPES.NETWORK_ERROR,
    ERROR_TYPES.TIMEOUT_ERROR,
    ERROR_TYPES.DATABASE_ERROR
  ]
  return retryableErrors.includes(errorType)
}

// 指数退避重试函数
async function retryWithBackoff(operation, options = {}) {
  const {
    maxRetries = RETRY_CONFIG.maxRetries,
    baseDelay = RETRY_CONFIG.baseDelay,
    maxDelay = RETRY_CONFIG.maxDelay,
    backoffFactor = RETRY_CONFIG.backoffFactor,
    retryCondition = null
  } = options

  let lastError

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await operation()
    } catch (error) {
      lastError = error

      // 检查是否应该重试
      if (retryCondition && !retryCondition(error)) {
        throw error
      }

      if (attempt === maxRetries) {
        throw error
      }

      // 计算延迟时间
      const delay = Math.min(baseDelay * Math.pow(backoffFactor, attempt), maxDelay)
      console.log(`操作失败，${delay}ms后进行第${attempt + 2}次重试:`, error.message)

      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }

  throw lastError
}

// 超时包装函数
function withTimeout(promise, timeoutMs, errorMessage = '操作超时') {
  return Promise.race([
    promise,
    new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error(errorMessage))
      }, timeoutMs)
    })
  ])
}

// 安全的云函数调用
async function safeCloudFunctionCall(functionName, data, options = {}) {
  const { timeout = 10000, retryable = true } = options

  const operation = () => {
    return withTimeout(
      cloud.callFunction({ name: functionName, data }),
      timeout,
      `调用云函数 ${functionName} 超时`
    )
  }

  if (retryable) {
    return await retryWithBackoff(operation, {
      retryCondition: (error) => {
        // 网络错误或超时错误才重试
        return error.message.includes('timeout') ||
          error.message.includes('network') ||
          error.errCode === -1
      }
    })
  } else {
    return await operation()
  }
}

// 安全的数据库操作
async function safeDatabaseOperation(operation, operationName, options = {}) {
  const { timeout = TIMEOUT_CONFIG.database, retryable = true } = options

  const wrappedOperation = () => {
    return withTimeout(operation(), timeout, `数据库操作 ${operationName} 超时`)
  }

  if (retryable) {
    return await retryWithBackoff(wrappedOperation, {
      retryCondition: (error) => {
        // 数据库连接错误或超时错误才重试
        return error.message.includes('timeout') ||
          error.message.includes('connection') ||
          error.errCode === -1
      }
    })
  } else {
    return await wrappedOperation()
  }
}

// 记录错误到日志系统
async function logError(error, context, additionalData = {}) {
  try {
    const errorLog = {
      timestamp: new Date().toISOString(),
      context,
      error: {
        message: error.message,
        stack: error.stack,
        code: error.errCode || error.code
      },
      additionalData
    }

    console.error(`[错误日志] ${context}:`, errorLog)

    // 这里可以添加发送到外部日志系统的逻辑
    // 例如发送到监控系统或日志收集服务

  } catch (logError) {
    console.error('记录错误日志失败:', logError)
  }
}

// 数据验证函数
function validateQuestionAnswers(questionAnswers) {
  const errors = []

  if (!questionAnswers) {
    errors.push('questionAnswers 参数不能为空')
    return { valid: false, errors }
  }

  // 验证第一问：今日感受
  if (questionAnswers.feeling !== undefined && questionAnswers.feeling !== null) {
    if (typeof questionAnswers.feeling !== 'string') {
      errors.push('feeling 必须是字符串类型')
    } else if (questionAnswers.feeling.trim().length > 50) {
      errors.push('feeling 长度不能超过50个字符')
    }
  }

  // 验证第二问：明天留言
  if (questionAnswers.tomorrowMessage !== undefined && questionAnswers.tomorrowMessage !== null) {
    if (typeof questionAnswers.tomorrowMessage !== 'string') {
      errors.push('tomorrowMessage 必须是字符串类型')
    } else if (questionAnswers.tomorrowMessage.trim().length > 100) {
      errors.push('tomorrowMessage 长度不能超过100个字符')
    }
  }

  // 验证第三问：是否生成日记
  if (questionAnswers.wantDiary !== undefined && questionAnswers.wantDiary !== null) {
    if (typeof questionAnswers.wantDiary !== 'boolean') {
      errors.push('wantDiary 必须是布尔类型')
    }
  }

  return { valid: errors.length === 0, errors }
}

function validateDialogueContext(dialogueContext) {
  const errors = []

  if (!dialogueContext) {
    errors.push('dialogueContext 参数不能为空')
    return { valid: false, errors }
  }

  if (!dialogueContext.theme || typeof dialogueContext.theme !== 'string') {
    errors.push('dialogueContext.theme 必须是非空字符串')
  }

  if (!dialogueContext.summary || typeof dialogueContext.summary !== 'string') {
    errors.push('dialogueContext.summary 必须是非空字符串')
  }

  if (dialogueContext.content && !Array.isArray(dialogueContext.content)) {
    errors.push('dialogueContext.content 必须是数组类型')
  }

  if (dialogueContext.userLevel !== undefined &&
    (!Number.isInteger(dialogueContext.userLevel) ||
      dialogueContext.userLevel < 1 ||
      dialogueContext.userLevel > 5)) {
    errors.push('dialogueContext.userLevel 必须是1-5之间的整数')
  }

  return { valid: errors.length === 0, errors }
}

// 数据清理和标准化函数
function sanitizeAndNormalizeData(questionAnswers, dialogueContext) {
  const sanitized = {
    questionAnswers: {
      feeling: questionAnswers.feeling ? questionAnswers.feeling.trim() : '',
      tomorrowMessage: questionAnswers.tomorrowMessage ? questionAnswers.tomorrowMessage.trim() : '',
      wantDiary: Boolean(questionAnswers.wantDiary)
    },
    dialogueContext: {
      theme: dialogueContext.theme.trim(),
      summary: dialogueContext.summary.trim(),
      content: Array.isArray(dialogueContext.content) ? dialogueContext.content : [],
      userLevel: typeof dialogueContext.userLevel === 'string' ?
        parseInt(dialogueContext.userLevel, 10) || 2 :
        (dialogueContext.userLevel || 2)
    }
  }

  return sanitized
}

// 创建集合的函数
async function ensureCollectionExists(collectionName) {
  try {
    // 尝试创建集合
    await db.createCollection(collectionName)
    console.log(`集合 ${collectionName} 创建成功`)
  } catch (error) {
    // 如果集合已存在，忽略错误
    if (error.errCode !== -501011) {
      console.error(`创建集合 ${collectionName} 失败`, error)
      // 不再抛出错误，允许继续执行
      console.log(`集合 ${collectionName} 可能已存在`)
    }
  }
}

// 数据迁移函数 - 将旧格式数据转换为新格式
async function migrateOldDataFormat(recordData) {
  try {
    // 检查是否需要迁移
    if (recordData.starPosition || recordData.constellationData) {
      console.log('数据已包含星座信息，无需迁移')
      return recordData
    }

    // 为旧数据生成默认的星座位置
    const migrationResult = await generateDefaultStarPosition(recordData)

    if (migrationResult.success) {
      recordData.starPosition = migrationResult.starPosition
      recordData.migrationInfo = {
        migratedAt: new Date().toISOString(),
        migrationType: 'auto_generated_position',
        originalFormat: 'legacy'
      }
      console.log('旧数据迁移成功，生成默认星座位置')
    }

    return recordData
  } catch (error) {
    console.error('数据迁移失败:', error)
    return recordData // 返回原数据，不影响主流程
  }
}

// 为旧数据生成默认星座位置
async function generateDefaultStarPosition(recordData) {
  try {
    const { openid, createTime, emotionKeyword } = recordData

    // 查询用户现有的星星数量，用于计算位置
    const existingStars = await db.collection('three_questions_records')
      .where({
        openid: openid,
        starPosition: db.command.exists(true)
      })
      .count()

    const starCount = existingStars.total

    // 使用网格布局算法生成位置
    const position = calculateGridPosition(starCount, emotionKeyword, createTime)

    return {
      success: true,
      starPosition: {
        x: position.x,
        y: position.y,
        generatedAt: new Date().toISOString(),
        generationType: 'grid_layout',
        starIndex: starCount
      }
    }
  } catch (error) {
    console.error('生成默认星座位置失败:', error)
    return { success: false, error: error.message }
  }
}

// 计算网格位置
function calculateGridPosition(starIndex, emotionKeyword, createTime) {
  const cols = 3 // 每行3颗星星
  const row = Math.floor(starIndex / cols)
  const col = starIndex % cols

  // 基础网格位置
  const baseX = 0.15 + col * 0.3 // 15%, 45%, 75%
  const baseY = 0.2 + row * 0.15 // 从20%开始，每行间隔15%

  // 基于情感关键词和时间添加随机偏移
  const seed = hashString(emotionKeyword + createTime) % 1000
  const randomX = (seed % 100 - 50) / 1000 // ±5%的偏移
  const randomY = ((seed * 7) % 80 - 40) / 1000 // ±4%的偏移

  return {
    x: Math.max(0.05, Math.min(0.95, baseX + randomX)),
    y: Math.max(0.15, Math.min(0.85, baseY + randomY))
  }
}

// 简单的字符串哈希函数
function hashString(str) {
  let hash = 0
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // 转换为32位整数
  }
  return Math.abs(hash)
}

// 验证星座位置数据
function validateStarPosition(starPosition) {
  const errors = []

  if (!starPosition) {
    return { valid: true, errors } // 星座位置数据是可选的
  }

  if (typeof starPosition !== 'object') {
    errors.push('starPosition 必须是对象类型')
    return { valid: false, errors }
  }

  // 验证位置坐标
  if (starPosition.x !== undefined) {
    if (typeof starPosition.x !== 'number' || starPosition.x < 0 || starPosition.x > 1) {
      errors.push('starPosition.x 必须是0-1之间的数字')
    }
  }

  if (starPosition.y !== undefined) {
    if (typeof starPosition.y !== 'number' || starPosition.y < 0 || starPosition.y > 1) {
      errors.push('starPosition.y 必须是0-1之间的数字')
    }
  }

  // 验证轨道信息
  if (starPosition.anchorStarId !== undefined && typeof starPosition.anchorStarId !== 'string') {
    errors.push('starPosition.anchorStarId 必须是字符串类型')
  }

  if (starPosition.orbitRadius !== undefined) {
    if (typeof starPosition.orbitRadius !== 'number' || starPosition.orbitRadius < 0) {
      errors.push('starPosition.orbitRadius 必须是非负数')
    }
  }

  if (starPosition.orbitAngle !== undefined) {
    if (typeof starPosition.orbitAngle !== 'number') {
      errors.push('starPosition.orbitAngle 必须是数字类型')
    }
  }

  return { valid: errors.length === 0, errors }
}

// 验证星座数据
function validateConstellationData(constellationData) {
  const errors = []

  if (!constellationData) {
    return { valid: true, errors } // 星座数据是可选的
  }

  if (typeof constellationData !== 'object') {
    errors.push('constellationData 必须是对象类型')
    return { valid: false, errors }
  }

  // 验证星星数组
  if (constellationData.stars !== undefined) {
    if (!Array.isArray(constellationData.stars)) {
      errors.push('constellationData.stars 必须是数组类型')
    } else {
      // 验证每个星星的数据
      constellationData.stars.forEach((star, index) => {
        if (!star.id || typeof star.id !== 'string') {
          errors.push(`constellationData.stars[${index}].id 必须是非空字符串`)
        }

        const positionValidation = validateStarPosition(star.position)
        if (!positionValidation.valid) {
          errors.push(...positionValidation.errors.map(err => `constellationData.stars[${index}].position: ${err}`))
        }
      })
    }
  }

  // 验证元数据
  if (constellationData.metadata !== undefined) {
    if (typeof constellationData.metadata !== 'object') {
      errors.push('constellationData.metadata 必须是对象类型')
    }
  }

  return { valid: errors.length === 0, errors }
}

// 提取感受关键词
function extractFeelingKeyword(feeling) {
  if (!feeling || feeling.trim().length === 0) {
    return '今日感受'
  }

  const trimmedFeeling = feeling.trim()

  // 如果输入很短（1-3个字），直接作为关键词
  if (trimmedFeeling.length <= 3) {
    return trimmedFeeling
  }

  // 如果是短句，提取核心词汇
  if (trimmedFeeling.length <= 10) {
    // 简单的关键词提取：去除常见的修饰词，但保持顺序
    const commonWords = ['很', '非常', '特别', '比较', '有点', '感到', '觉得', '今天', '我']
    let keyword = trimmedFeeling

    // 逐个移除修饰词
    commonWords.forEach(word => {
      keyword = keyword.replace(new RegExp(word, 'g'), '')
    })

    // 清理多余的空格
    keyword = keyword.replace(/\s+/g, '').trim()

    // 如果提取后还有内容且长度合理，返回提取结果
    if (keyword.length > 0 && keyword.length <= 6) {
      return keyword
    }

    // 如果提取后为空或太长，返回原文的前几个字
    return trimmedFeeling.substring(0, Math.min(4, trimmedFeeling.length))
  }

  // 如果是长句，智能提取关键词
  const commonWords = ['很', '非常', '特别', '比较', '有点', '感到', '觉得', '今天', '我', '的', '了', '是', '在']
  let keyword = trimmedFeeling

  // 移除修饰词
  commonWords.forEach(word => {
    keyword = keyword.replace(new RegExp(word, 'g'), '')
  })

  // 清理空格并截取
  keyword = keyword.replace(/\s+/g, '').trim()

  if (keyword.length > 0) {
    return keyword.substring(0, Math.min(6, keyword.length))
  }

  // 如果提取失败，返回原文的前几个字
  return trimmedFeeling.substring(0, 6)
}

exports.main = async (event, context) => {
  const startTime = Date.now()
  let operationContext = 'saveThreeQuestions'

  try {
    // 详细记录输入参数
    console.log('收到的事件参数:', JSON.stringify(event))

    const {
      // 新的数据结构
      questionAnswers,    // 三问答案对象
      dialogueContext,    // 对话上下文

      // 兼容旧的数据结构
      theme,              // 对话主题
      summary,            // AI总结
      questions,          // 三个问题（可选）
      answers,            // 三个问题的答案（可选）
      emotionKeyword,     // 情感关键词
      tomorrowMessage,    // 明日寄语
      dialogueContent,    // 完整对话内容（可选）
      updateMode          // 新增：更新模式
    } = event

    // 验证星座数据（如果提供）
    const starPositionValidation = validateStarPosition(event.starPosition)
    const constellationValidation = validateConstellationData(event.constellationData)

    if (!starPositionValidation.valid || !constellationValidation.valid) {
      const allErrors = [...starPositionValidation.errors, ...constellationValidation.errors]
      console.error('星座数据验证失败:', allErrors)
      return {
        success: false,
        error: '星座数据验证失败',
        validationErrors: allErrors,
        errorType: 'VALIDATION_ERROR'
      }
    }

    // 处理新的数据结构
    let processedData = {}

    if (questionAnswers && dialogueContext) {
      // 验证新的数据结构
      const questionValidation = validateQuestionAnswers(questionAnswers)
      const contextValidation = validateDialogueContext(dialogueContext)

      if (!questionValidation.valid || !contextValidation.valid) {
        const allErrors = [...questionValidation.errors, ...contextValidation.errors]
        console.error('数据验证失败:', allErrors)
        return {
          success: false,
          error: '数据验证失败',
          validationErrors: allErrors,
          errorType: 'VALIDATION_ERROR'
        }
      }

      // 数据清理和标准化
      const sanitized = sanitizeAndNormalizeData(questionAnswers, dialogueContext)

      processedData = {
        theme: sanitized.dialogueContext.theme,
        summary: sanitized.dialogueContext.summary,
        dialogueContent: sanitized.dialogueContext.content,
        userLevel: sanitized.dialogueContext.userLevel,

        // 三问答案
        dailyFeeling: sanitized.questionAnswers.feeling,
        tomorrowMessage: sanitized.questionAnswers.tomorrowMessage,
        wantDiary: sanitized.questionAnswers.wantDiary,

        // 提取感受关键词作为星星关键词
        emotionKeyword: extractFeelingKeyword(sanitized.questionAnswers.feeling) || '今日感受'
      }
    } else {
      // 兼容旧的数据结构
      if (!theme || !summary) {
        console.error('缺少必要参数 (旧格式)', { theme, summary })
        return {
          success: false,
          error: '参数不完整 - 缺少 theme 或 summary',
          errorType: 'MISSING_REQUIRED_PARAMS',
          details: { theme: !!theme, summary: !!summary }
        }
      }

      processedData = {
        theme: theme.trim(),
        summary: summary.trim(),
        dialogueContent: Array.isArray(dialogueContent) ? dialogueContent : [],
        userLevel: 2,

        dailyFeeling: '',
        tomorrowMessage: tomorrowMessage ? tomorrowMessage.trim() : '',
        wantDiary: false,
        emotionKeyword: emotionKeyword || '今日感受'
      }
    }

    // 最终验证处理后的数据
    if (!processedData.theme || !processedData.summary) {
      console.error('处理后数据仍然不完整', {
        theme: processedData.theme,
        summary: processedData.summary
      })
      return {
        success: false,
        error: '处理后数据不完整',
        errorType: 'DATA_PROCESSING_ERROR',
        details: {
          theme: processedData.theme,
          summary: processedData.summary
        }
      }
    }

    const wxContext = cloud.getWXContext()
    const openid = wxContext.OPENID

    console.log('当前用户 openid:', openid)

    try {
      // 确保集合存在（不再抛出错误）
      await ensureCollectionExists('three_questions_records')
      await ensureCollectionExists('users')

      let recordResult
      let recordId = null

      // 构建完整的记录数据
      let recordData = {
        openid,
        theme: processedData.theme,
        summary: processedData.summary,
        dialogueContent: processedData.dialogueContent,
        userLevel: processedData.userLevel,

        // 三问答案
        dailyFeeling: processedData.dailyFeeling,
        tomorrowMessage: processedData.tomorrowMessage,
        wantDiary: processedData.wantDiary,

        // 星星关键词
        starKeyword: processedData.emotionKeyword,
        emotionKeyword: processedData.emotionKeyword, // 保持兼容性

        // 星座位置数据
        starPosition: event.starPosition || null, // 星星在星座中的位置信息
        constellationData: event.constellationData || null, // 完整的星座数据

        // 状态跟踪
        completed: true,
        completedTime: db.serverDate()
      }

      // 测试阶段：如果没有星座位置，生成简单的默认位置
      if (!recordData.starPosition) {
        recordData.starPosition = generateDefaultStarPosition()
      }

      // 检查是否是更新模式
      if (updateMode === 'modify') {
        console.log('处于修改模式，尝试查找并更新现有记录')

        try {
          // 尝试根据 openid 和 theme 查找现有记录
          const existingRecords = await db.collection('three_questions_records').where({
            openid: openid,
            theme: processedData.theme,
          }).orderBy('createTime', 'desc').limit(1).get()

          if (existingRecords.data.length > 0) {
            // 找到记录，更新第一个匹配项
            recordId = existingRecords.data[0]._id
            console.log(`找到现有记录，ID: ${recordId}，准备更新`)

            recordResult = await db.collection('three_questions_records').doc(recordId).update({
              data: {
                ...recordData,
                updateTime: db.serverDate()
              }
            })
            console.log('三问仪式记录更新成功:', recordResult)
          } else {
            // 未找到现有记录，则创建新记录
            console.warn('在修改模式下未找到匹配的现有记录，将创建新记录。')
            recordResult = await db.collection('three_questions_records').add({
              data: {
                ...recordData,
                createTime: db.serverDate()
              }
            })
            recordId = recordResult._id
            console.log('三问仪式记录保存成功(因未找到旧记录而新建):', recordResult)
          }
        } catch (queryError) {
          console.error('查询现有记录失败，转为创建新记录:', queryError)
          // 查询失败时，创建新记录
          recordResult = await db.collection('three_questions_records').add({
            data: {
              ...recordData,
              createTime: db.serverDate()
            }
          })
          recordId = recordResult._id
          console.log('三问仪式记录保存成功(查询失败后新建):', recordResult)
        }
      } else {
        // 默认是新增模式
        console.log('处于新增模式，创建新记录')
        recordResult = await db.collection('three_questions_records').add({
          data: {
            ...recordData,
            createTime: db.serverDate()
          }
        })
        recordId = recordResult._id
        console.log('三问仪式记录保存成功:', recordResult)
      }

      // 更新用户的对话历史
      let userUpdateResult = null
      try {
        userUpdateResult = await db.collection('users').doc(openid).update({
          data: {
            dialogueHistory: _.push({
              theme: processedData.theme,
              summary: processedData.summary,
              emotionKeyword: processedData.emotionKeyword,
              tomorrowMessage: processedData.tomorrowMessage,
              dailyFeeling: processedData.dailyFeeling,
              createTime: db.serverDate()
            })
          },
          // 如果用户文档不存在，则创建
          upsert: true
        })
        console.log('用户对话历史更新成功:', userUpdateResult)
      } catch (userUpdateError) {
        console.error('用户对话历史更新失败:', userUpdateError)
        // 不影响主流程，继续执行
        userUpdateResult = {
          success: false,
          error: userUpdateError.message
        }
      }

      // 如果用户填写了明天留言，创建定时消息
      let messageScheduled = false
      let messageScheduleResult = null

      if (processedData.tomorrowMessage && processedData.tomorrowMessage.trim().length > 0) {
        try {
          console.log('开始调度明天留言:', processedData.tomorrowMessage)

          const scheduleResult = await cloud.callFunction({
            name: 'scheduleTomorrowMessage',
            data: {
              message: processedData.tomorrowMessage,
              sourceType: 'tomorrow_message',
              sourceId: recordId
            }
          })

          if (scheduleResult.result && scheduleResult.result.success) {
            messageScheduled = true
            messageScheduleResult = scheduleResult.result
            console.log('明天留言调度成功:', scheduleResult.result)
          } else {
            console.error('明天留言调度失败:', scheduleResult.result)
          }

        } catch (error) {
          console.error('调用定时消息云函数失败:', error)
          // 不影响主流程，继续执行
        }
      }

      // 如果用户选择生成日记，调用AI日记生成服务
      let diaryGenerated = false
      let diaryContent = null
      let diaryGenerationResult = null

      if (processedData.wantDiary) {
        try {
          console.log('开始生成AI日记...')

          const diaryResult = await cloud.callFunction({
            name: 'geminiProxy',
            data: {
              type: 'generateDiary',
              dialogueContext: processedData.dialogueContent,
              userLevel: processedData.userLevel,
              dailyFeeling: processedData.dailyFeeling,
              dialogueTheme: processedData.theme,
              openid: openid
            }
          })

          if (diaryResult.result && diaryResult.result.success) {
            diaryGenerated = true
            diaryContent = diaryResult.result.diaryContent
            diaryGenerationResult = diaryResult.result
            console.log('AI日记生成成功，字数:', diaryContent.length)

            // 将生成的日记保存到记录中
            if (updateMode === 'modify') {
              await db.collection('three_questions_records').doc(recordId).update({
                data: {
                  diaryContent: diaryContent,
                  diaryGenerated: true,
                  diaryGenerationTime: db.serverDate(),
                  diaryWordCount: diaryContent.length,
                  diaryQualityScore: diaryResult.result.qualityScore || 0.7,
                  diaryFallback: diaryResult.result.fallback || false
                }
              })
            } else {
              await db.collection('three_questions_records').doc(recordId).update({
                data: {
                  diaryContent: diaryContent,
                  diaryGenerated: true,
                  diaryGenerationTime: db.serverDate(),
                  diaryWordCount: diaryContent.length,
                  diaryQualityScore: diaryResult.result.qualityScore || 0.7,
                  diaryFallback: diaryResult.result.fallback || false
                }
              })
            }

            console.log('AI日记已保存到数据库')
          } else {
            console.error('AI日记生成失败:', diaryResult.result)
            diaryGenerationResult = diaryResult.result
          }

        } catch (error) {
          console.error('调用AI日记生成服务失败:', error)
          diaryGenerationResult = {
            success: false,
            error: error.message,
            fallback: true
          }
          // 不影响主流程，继续执行
        }
      }

      // 记录三问仪式分析指标
      try {
        const analyticsData = {
          openid: openid,
          userLevel: processedData.userLevel,

          // 参与度指标
          startedAt: new Date(), // 简化处理，实际应该从前端传入
          completedAt: new Date(),
          totalTime: 0, // 简化处理，实际应该从前端传入

          // 各问题参与情况
          question1Answered: processedData.dailyFeeling && processedData.dailyFeeling.trim().length > 0,
          question1Skipped: !processedData.dailyFeeling || processedData.dailyFeeling.trim().length === 0,
          question1Time: 0, // 简化处理

          question2Answered: processedData.tomorrowMessage && processedData.tomorrowMessage.trim().length > 0,
          question2Skipped: !processedData.tomorrowMessage || processedData.tomorrowMessage.trim().length === 0,
          question2Time: 0, // 简化处理

          question3Answered: processedData.wantDiary,
          question3Skipped: !processedData.wantDiary,
          question3Time: 0, // 简化处理

          // AI日记生成相关
          requestedDiary: processedData.wantDiary,
          diaryGenerated: diaryGenerated,
          diaryGenerationTime: diaryGenerationResult?.generationTime || 0,
          diaryGenerationSuccess: diaryGenerated,
          diaryFallbackUsed: diaryGenerationResult?.fallback || false,
          diaryWordCount: diaryContent ? diaryContent.length : 0,

          // 定时消息相关
          scheduledMessage: processedData.tomorrowMessage && processedData.tomorrowMessage.trim().length > 0,
          messageScheduleSuccess: messageScheduled,

          // 完成状态
          completed: true,
          exitPoint: null, // 完成了所有步骤

          // 关联信息
          dialogueTheme: processedData.theme,
          sourceRecordId: recordId
        };

        await cloud.callFunction({
          name: 'tieringAnalytics',
          data: {
            type: 'recordThreeQuestionsMetrics',
            data: analyticsData
          }
        });

        console.log('三问仪式分析指标记录成功');
      } catch (analyticsError) {
        console.error('记录三问仪式分析指标失败:', analyticsError);
        // 不影响主流程，继续执行
      }

      return {
        success: true,
        recordId: recordId,
        starKeyword: processedData.emotionKeyword, // 返回星星关键词
        messageScheduled,
        messageScheduleResult,
        diaryGenerated,
        diaryContent,
        diaryGenerationResult,
        userUpdateResult
      }
    } catch (error) {
      // 详细记录错误信息
      console.error('操作三问仪式记录失败', error)

      // 记录错误到日志系统
      await logError(error, operationContext, {
        openid: wxContext.OPENID,
        theme,
        summary,
        emotionKeyword,
        tomorrowMessage,
        updateMode,
        executionTime: Date.now() - startTime
      })

      // 根据错误类型返回不同的响应
      if (error.message.includes('timeout')) {
        return createErrorResponse(ERROR_TYPES.TIMEOUT_ERROR, '操作超时，请重试', {
          executionTime: Date.now() - startTime
        })
      } else if (error.message.includes('network') || error.errCode === -1) {
        return createErrorResponse(ERROR_TYPES.NETWORK_ERROR, '网络连接失败，请检查网络后重试', {
          errorCode: error.errCode
        })
      } else if (error.message.includes('database') || error.message.includes('collection')) {
        return createErrorResponse(ERROR_TYPES.DATABASE_ERROR, '数据库操作失败，请稍后重试', {
          errorCode: error.errCode,
          errorMessage: error.message
        })
      } else {
        return createErrorResponse(ERROR_TYPES.SYSTEM_ERROR, '系统异常，请稍后重试', {
          errorCode: error.errCode,
          errorMessage: error.message,
          errorStack: error.stack?.substring(0, 500) // 限制堆栈长度
        })
      }
    }
  } catch (outerError) {
    // 最外层错误处理 - 防止函数完全崩溃
    console.error('云函数执行发生严重错误:', outerError)

    await logError(outerError, 'saveThreeQuestions_critical', {
      executionTime: Date.now() - startTime,
      originalContext: operationContext
    })

    return createErrorResponse(ERROR_TYPES.SYSTEM_ERROR, '系统发生严重错误，请联系技术支持', {
      errorCode: outerError.errCode || 'CRITICAL_ERROR',
      timestamp: new Date().toISOString()
    })
  }
}

/**
 * 生成默认星星位置（测试阶段简化版）
 */
function generateDefaultStarPosition() {
  try {
    console.log('生成默认星星位置...')

    // 生成简单的随机位置
    const position = {
      x: Math.random() * 0.6 + 0.2, // 20%-80% 范围，避免边缘
      y: Math.random() * 0.6 + 0.2, // 20%-80% 范围，避免边缘
      generatedAt: new Date().toISOString(),
      generationType: 'default_test_position'
    }

    console.log('默认位置生成完成:', position)
    return position

  } catch (error) {
    console.error('生成默认位置失败:', error)
    // 返回中心位置作为后备
    return {
      x: 0.5,
      y: 0.5,
      generatedAt: new Date().toISOString(),
      generationType: 'fallback_center'
    }
  }
}