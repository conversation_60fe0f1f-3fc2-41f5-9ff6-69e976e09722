page {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
  background-color: black;
}

.container {
  height: 100vh;
  width: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.volume-btn {
  position: fixed;
  top: 40rpx;
  right: 40rpx;
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.volume-icon {
  width: 30rpx;
  height: 30rpx;
  background: rgba(255, 255, 255, 0.8);
  mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M12 4L7 9H3v6h4l5 5V4zm2.5 4A4.5 4.5 0 0114 12a4.5 4.5 0 01-1.5 4v-8z'/%3E%3C/svg%3E") no-repeat 50% 50%;
  -webkit-mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M12 4L7 9H3v6h4l5 5V4zm2.5 4A4.5 4.5 0 0114 12a4.5 4.5 0 01-1.5 4v-8z'/%3E%3C/svg%3E") no-repeat 50% 50%;
}

.muted .volume-icon {
  mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M12 4L7 9H3v6h4l5 5V4zm2.5 4l5 5m0-5l-5 5'/%3E%3C/svg%3E") no-repeat 50% 50%;
  -webkit-mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M12 4L7 9H3v6h4l5 5V4zm2.5 4l5 5m0-5l-5 5'/%3E%3C/svg%3E") no-repeat 50% 50%;
}

.start-button {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 40rpx;
  padding: 20rpx 60rpx;
  color: white;
  font-size: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.start-button:active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.25);
}

.content {
  width: 100%;
  padding: 40rpx;
  box-sizing: border-box;
}

.text-line {
  color: white;
  font-size: 32rpx;
  margin-bottom: 30rpx;
  text-align: center;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
  animation: fadeIn 0.5s ease;
}

.button-group {
  margin-top: 60rpx;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
  align-items: center;
}

.glass-button {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 40rpx;
  color: white;
  font-size: 28rpx;
  padding: 20rpx 60rpx;
  min-width: 400rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.glass-button:active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.25);
}

.final-button {
  margin-top: 60rpx;
  display: flex;
  justify-content: center;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
/* ✅ 静音按钮样式 */
.mute-toggle {
  position: fixed;
  top: 20rpx;
  right: 24rpx;
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.6);
  z-index: 9999;
}
