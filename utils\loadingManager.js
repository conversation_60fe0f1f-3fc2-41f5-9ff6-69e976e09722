/**
 * 加载状态管理工具
 */

class LoadingManager {
    constructor() {
        this.loadingCount = 0;
        this.loadingTimer = null;
    }

    // 显示加载
    show(title = '加载中...', mask = true) {
        this.loadingCount++;

        // 防止频繁调用
        if (this.loadingTimer) {
            clearTimeout(this.loadingTimer);
        }

        this.loadingTimer = setTimeout(() => {
            if (this.loadingCount > 0) {
                wx.showLoading({
                    title: title,
                    mask: mask
                });
            }
        }, 100);
    }

    // 隐藏加载
    hide() {
        this.loadingCount = Math.max(0, this.loadingCount - 1);

        if (this.loadingTimer) {
            clearTimeout(this.loadingTimer);
            this.loadingTimer = null;
        }

        if (this.loadingCount === 0) {
            wx.hideLoading();
        }
    }

    // 强制隐藏所有加载
    hideAll() {
        this.loadingCount = 0;
        if (this.loadingTimer) {
            clearTimeout(this.loadingTimer);
            this.loadingTimer = null;
        }
        wx.hideLoading();
    }

    // 包装异步操作
    async wrap(asyncOperation, title = '加载中...') {
        this.show(title);
        try {
            const result = await asyncOperation();
            return result;
        } finally {
            this.hide();
        }
    }
}

// 创建全局实例
const loadingManager = new LoadingManager();

module.exports = loadingManager;