/**
 * 星座存储管理器
 * 负责星座数据的本地存储和云端同步
 */

class ConstellationStorage {
    constructor() {
        this.storageKeys = {
            constellation: 'constellation_data',
            starRecords: 'star_records',
            userPreferences: 'user_preferences',
            cacheVersion: 'cache_version',
            lastSyncTime: 'last_sync_time'
        }
        
        this.currentVersion = '1.0.0'
        this.maxCacheAge = 24 * 60 * 60 * 1000 // 24小时
        this.compressionEnabled = true
    }

    /**
     * 初始化存储管理器
     */
    initialize() {
        console.log('初始化星座存储管理器...')
        
        // 检查存储版本
        this.checkStorageVersion()
        
        // 清理过期缓存
        this.cleanupExpiredCache()
        
        return { success: true }
    }

    /**
     * 保存星座数据到本地
     */
    async saveConstellationData(constellationData) {
        try {
            // 验证数据格式
            const validationResult = this.validateConstellationData(constellationData)
            if (!validationResult.valid) {
                throw new Error(`数据验证失败: ${validationResult.errors.join(', ')}`)
            }

            // 压缩数据（如果启用）
            const dataToStore = this.compressionEnabled ? 
                this.compressData(constellationData) : constellationData

            // 添加元数据
            const storageData = {
                data: dataToStore,
                version: this.currentVersion,
                timestamp: Date.now(),
                compressed: this.compressionEnabled
            }

            // 保存到本地存储
            wx.setStorageSync(this.storageKeys.constellation, storageData)
            
            // 更新缓存版本
            wx.setStorageSync(this.storageKeys.cacheVersion, this.currentVersion)

            console.log('星座数据已保存到本地存储')
            return { success: true, size: JSON.stringify(storageData).length }

        } catch (error) {
            console.error('保存星座数据失败:', error)
            return { success: false, error: error.message }
        }
    }

    /**
     * 从本地加载星座数据
     */
    async loadConstellationData() {
        try {
            const storageData = wx.getStorageSync(this.storageKeys.constellation)
            
            if (!storageData) {
                console.log('本地没有星座数据')
                return { success: true, data: null }
            }

            // 测试阶段：简化版本检查，直接使用数据
            if (storageData.version !== this.currentVersion) {
                console.log(`数据版本不同: ${storageData.version} -> ${this.currentVersion}，测试阶段直接使用`)
            }

            // 检查数据是否过期
            const age = Date.now() - storageData.timestamp
            if (age > this.maxCacheAge) {
                console.warn('本地数据已过期')
                return { success: true, data: null, expired: true }
            }

            // 解压数据（如果需要）
            const constellationData = storageData.compressed ? 
                this.decompressData(storageData.data) : storageData.data

            console.log('从本地存储加载星座数据成功')
            return { 
                success: true, 
                data: constellationData,
                age: age,
                version: storageData.version
            }

        } catch (error) {
            console.error('加载星座数据失败:', error)
            return { success: false, error: error.message }
        }
    }

    /**
     * 保存星星记录
     */
    async saveStarRecords(starRecords) {
        try {
            const storageData = {
                records: starRecords,
                timestamp: Date.now(),
                count: starRecords.length
            }

            wx.setStorageSync(this.storageKeys.starRecords, storageData)
            
            console.log(`保存了 ${starRecords.length} 条星星记录`)
            return { success: true, count: starRecords.length }

        } catch (error) {
            console.error('保存星星记录失败:', error)
            return { success: false, error: error.message }
        }
    }

    /**
     * 加载星星记录
     */
    async loadStarRecords() {
        try {
            const storageData = wx.getStorageSync(this.storageKeys.starRecords)
            
            if (!storageData) {
                return { success: true, records: [] }
            }

            console.log(`加载了 ${storageData.count} 条星星记录`)
            return { 
                success: true, 
                records: storageData.records || [],
                timestamp: storageData.timestamp
            }

        } catch (error) {
            console.error('加载星星记录失败:', error)
            return { success: false, error: error.message }
        }
    }

    /**
     * 同步数据到云端
     */
    async syncToCloud(data, syncType = 'full') {
        try {
            console.log(`开始${syncType}同步到云端...`)

            const syncData = {
                type: syncType,
                data: data,
                timestamp: Date.now(),
                version: this.currentVersion
            }

            // 调用云函数进行同步
            const result = await wx.cloud.callFunction({
                name: 'syncConstellationData',
                data: syncData
            })

            if (result.result && result.result.success) {
                // 更新最后同步时间
                wx.setStorageSync(this.storageKeys.lastSyncTime, Date.now())
                
                console.log('云端同步成功')
                return { 
                    success: true, 
                    syncId: result.result.syncId,
                    timestamp: Date.now()
                }
            } else {
                throw new Error(result.result?.error || '云端同步失败')
            }

        } catch (error) {
            console.error('云端同步失败:', error)
            return { success: false, error: error.message }
        }
    }

    /**
     * 从云端同步数据
     */
    async syncFromCloud(lastSyncTime = null) {
        try {
            console.log('从云端同步数据...')

            const syncRequest = {
                lastSyncTime: lastSyncTime || wx.getStorageSync(this.storageKeys.lastSyncTime) || 0,
                version: this.currentVersion
            }

            const result = await wx.cloud.callFunction({
                name: 'getConstellationData',
                data: syncRequest
            })

            if (result.result && result.result.success) {
                const cloudData = result.result.data
                
                if (cloudData) {
                    // 保存云端数据到本地
                    await this.saveConstellationData(cloudData)
                    
                    // 更新同步时间
                    wx.setStorageSync(this.storageKeys.lastSyncTime, Date.now())
                }

                console.log('从云端同步数据成功')
                return { 
                    success: true, 
                    data: cloudData,
                    hasUpdates: !!cloudData
                }
            } else {
                throw new Error(result.result?.error || '从云端同步失败')
            }

        } catch (error) {
            console.error('从云端同步失败:', error)
            return { success: false, error: error.message }
        }
    }

    /**
     * 验证星座数据格式
     */
    validateConstellationData(data) {
        const errors = []

        if (!data) {
            errors.push('数据不能为空')
            return { valid: false, errors }
        }

        if (!data.stars || !Array.isArray(data.stars)) {
            errors.push('星星数据必须是数组')
        }

        if (!data.metadata) {
            errors.push('缺少元数据')
        }

        // 验证每个星星数据
        if (data.stars) {
            data.stars.forEach((star, index) => {
                if (!star.id) {
                    errors.push(`星星 ${index} 缺少ID`)
                }
                if (!star.position) {
                    errors.push(`星星 ${index} 缺少位置信息`)
                }
            })
        }

        return { valid: errors.length === 0, errors }
    }

    /**
     * 压缩数据
     */
    compressData(data) {
        try {
            // 简单的JSON压缩（移除空格）
            const jsonString = JSON.stringify(data)
            return {
                compressed: true,
                data: jsonString,
                originalSize: jsonString.length
            }
        } catch (error) {
            console.warn('数据压缩失败，使用原始数据:', error)
            return data
        }
    }

    /**
     * 解压数据
     */
    decompressData(compressedData) {
        try {
            if (compressedData.compressed) {
                return JSON.parse(compressedData.data)
            }
            return compressedData
        } catch (error) {
            console.error('数据解压失败:', error)
            return null
        }
    }

    /**
     * 检查存储版本
     */
    checkStorageVersion() {
        const storedVersion = wx.getStorageSync(this.storageKeys.cacheVersion)
        
        if (!storedVersion) {
            // 首次使用，设置当前版本
            wx.setStorageSync(this.storageKeys.cacheVersion, this.currentVersion)
            console.log('设置存储版本:', this.currentVersion)
        } else if (storedVersion !== this.currentVersion) {
            console.log(`存储版本更新: ${storedVersion} -> ${this.currentVersion}`)
            // 这里可以触发数据迁移
        }
    }

    /**
     * 简化的数据处理（测试阶段）
     */
    async processStorageData(data) {
        try {
            // 测试阶段：直接返回数据，不进行复杂迁移
            console.log('处理存储数据（测试模式）')
            return { success: true, data }
        } catch (error) {
            console.error('处理存储数据失败:', error)
            return { success: false, error: error.message }
        }
    }

    /**
     * 清理过期缓存
     */
    cleanupExpiredCache() {
        try {
            const lastSyncTime = wx.getStorageSync(this.storageKeys.lastSyncTime)
            
            if (lastSyncTime && Date.now() - lastSyncTime > this.maxCacheAge) {
                console.log('清理过期缓存')
                wx.removeStorageSync(this.storageKeys.constellation)
                wx.removeStorageSync(this.storageKeys.lastSyncTime)
            }
        } catch (error) {
            console.error('清理缓存失败:', error)
        }
    }

    /**
     * 获取存储统计信息
     */
    getStorageStats() {
        try {
            const stats = {
                constellation: this.getStorageSize(this.storageKeys.constellation),
                starRecords: this.getStorageSize(this.storageKeys.starRecords),
                userPreferences: this.getStorageSize(this.storageKeys.userPreferences),
                total: 0
            }

            stats.total = stats.constellation + stats.starRecords + stats.userPreferences

            return { success: true, stats }
        } catch (error) {
            return { success: false, error: error.message }
        }
    }

    /**
     * 获取存储项大小
     */
    getStorageSize(key) {
        try {
            const data = wx.getStorageSync(key)
            return data ? JSON.stringify(data).length : 0
        } catch (error) {
            return 0
        }
    }

    /**
     * 清空所有存储数据
     */
    clearAllData() {
        try {
            Object.values(this.storageKeys).forEach(key => {
                wx.removeStorageSync(key)
            })
            
            console.log('所有存储数据已清空')
            return { success: true }
        } catch (error) {
            console.error('清空数据失败:', error)
            return { success: false, error: error.message }
        }
    }

    /**
     * 销毁存储管理器
     */
    destroy() {
        console.log('星座存储管理器已销毁')
    }
}

// 创建全局实例
const constellationStorage = new ConstellationStorage()

export default constellationStorage
