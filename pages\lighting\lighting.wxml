<!--点灯仪式主页面-->
<view class="lighting-container {{currentStep}}" style="{{containerStyle}}">
  
  <!-- 星空背景 -->
  <view class="starry-background">
    <!-- 多层星空效果 -->
    <view class="stars stars-small"></view>
    <view class="stars stars-medium"></view>
    <view class="stars stars-large"></view>
    <!-- 深空渐变背景 -->
    <view class="deep-space"></view>
  </view>

  <!-- 步骤1: 黑夜初入 -->
  <view class="step step-welcome {{currentStep === 'welcome' ? 'active' : ''}}" wx:if="{{currentStep === 'welcome'}}">
    <view class="welcome-content">
      <view class="welcome-text animate-fade-in">
        <text class="main-text">夜深了</text>
        <text class="sub-text">远方有一点微光在等待</text>
      </view>
      <view class="continue-hint animate-float">
        <text>轻触屏幕，走向那束光</text>
      </view>
    </view>
  </view>

  <!-- 步骤2: 点亮过程 -->
  <view class="step step-lighting {{currentStep === 'lighting' ? 'active' : ''}}" wx:if="{{currentStep === 'lighting'}}">
    <view class="lighting-content">
      <!-- 灯的容器 -->
      <view class="lamp-container">
        <view class="lamp {{isLighting ? 'lighting' : ''}}" bindtap="startLighting">
          <!-- 灯体 -->
          <view class="lamp-body"></view>
          <!-- 光晕效果 -->
          <view class="lamp-glow {{isLighting ? 'glowing' : ''}}"></view>
          <!-- 光线扩散 -->
          <view class="light-rays {{isLighting ? 'expanding' : ''}}">
            <view class="ray ray-1"></view>
            <view class="ray ray-2"></view>
            <view class="ray ray-3"></view>
            <view class="ray ray-4"></view>
          </view>
        </view>
      </view>
      
      <view class="lighting-text {{isLighting ? 'show' : ''}}">
        <text class="main-text">光，被你点亮了</text>
        <text class="sub-text">宇宙中多了一颗温暖的星</text>
      </view>
    </view>
  </view>

  <!-- 步骤3: 命名灯 -->
  <view class="step step-name-lamp {{currentStep === 'nameLamp' ? 'active' : ''}}" wx:if="{{currentStep === 'nameLamp'}}">
    <view class="name-content">
      <view class="lamp-display">
        <view class="lamp glowing small">
          <view class="lamp-body"></view>
          <view class="lamp-glow glowing"></view>
        </view>
      </view>
      
      <view class="name-text">
        <text class="main-text">为你的灯起个名字吧</text>
        <text class="sub-text">它将陪伴你走过每一个夜晚</text>
      </view>
      
      <view class="input-container">
        <input 
          class="lamp-name-input" 
          placeholder="给灯起个名字..." 
          placeholder-class="input-placeholder"
          value="{{lampName}}" 
          bindinput="onLampNameInput"
          maxlength="10"
        />
        <view class="input-underline"></view>
      </view>
      
      <view class="continue-btn {{lampName ? 'active' : ''}}" bindtap="confirmLampName">
        <text>确认</text>
      </view>
    </view>
  </view>

  <!-- 步骤4: 命名自己 -->
  <view class="step step-name-self {{currentStep === 'nameSelf' ? 'active' : ''}}" wx:if="{{currentStep === 'nameSelf'}}">
    <view class="name-content">
      <view class="avatar-container">
        <view class="avatar-glow"></view>
        <view class="avatar-silhouette"></view>
      </view>
      
      <view class="name-text">
        <text class="main-text">在这里，你想被如何称呼？</text>
        <text class="sub-text">选择一个让你感到舒适的名字</text>
      </view>
      
      <view class="input-container">
        <input 
          class="self-name-input" 
          placeholder="你的名字..." 
          placeholder-class="input-placeholder"
          value="{{userName}}" 
          bindinput="onUserNameInput"
          maxlength="8"
        />
        <view class="input-underline"></view>
      </view>
      
      <view class="continue-btn {{userName ? 'active' : ''}}" bindtap="confirmUserName">
        <text>确认</text>
      </view>
    </view>
  </view>

  <!-- 步骤5: 三道意象题目 -->
  <view class="step step-questions {{currentStep === 'questions' ? 'active' : ''}}" wx:if="{{currentStep === 'questions'}}">
    <view class="questions-content">
      
      <!-- 问题标题 -->
      <view class="question-header">
        <text class="question-number">{{currentQuestionIndex + 1}}/3</text>
        <text class="question-title">{{questions[currentQuestionIndex].title}}</text>
        <text class="question-desc">{{questions[currentQuestionIndex].description}}</text>
      </view>
      
      <!-- 选项列表 -->
      <view class="options-container">
        <view 
          class="option-item {{selectedOption === index ? 'selected' : ''}}" 
          wx:for="{{questions[currentQuestionIndex].options}}" 
          wx:key="index"
          bindtap="selectOption"
          data-index="{{index}}"
        >
          <view class="option-icon">
            <view class="option-glow {{selectedOption === index ? 'active' : ''}}"></view>
          </view>
          <text class="option-text">{{item}}</text>
        </view>
      </view>
      
      <!-- 继续按钮 -->
      <view class="continue-btn {{selectedOption !== null ? 'active' : ''}}" bindtap="nextQuestion">
        <text>{{currentQuestionIndex === 2 ? '完成仪式' : '下一题'}}</text>
      </view>
      
    </view>
  </view>

  <!-- 步骤6: 仪式完成 -->
  <view class="step step-complete {{currentStep === 'complete' ? 'active' : ''}}" wx:if="{{currentStep === 'complete'}}">
    <view class="complete-content">
      <view class="celebration-animation">
        <view class="lamp glowing celebration">
          <view class="lamp-body"></view>
          <view class="lamp-glow glowing"></view>
          <!-- 庆祝粒子效果 -->
          <view class="particles">
            <view class="particle" wx:for="{{12}}" wx:key="*this" style="--delay: {{index * 0.1}}s; --rotation: {{index * 30}}deg;"></view>
          </view>
        </view>
      </view>
      
      <view class="complete-text">
        <text class="main-text">点灯仪式完成</text>
        <text class="sub-text">{{userName}}，欢迎来到微光世界</text>
        <text class="detail-text">你的{{lampName}}已经点亮，它将在这片星空中永远为你闪耀</text>
      </view>
      
      <view class="enter-btn" bindtap="enterMainApp">
        <text>进入微光</text>
      </view>
    </view>
  </view>

</view>