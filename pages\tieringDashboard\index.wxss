/* 层级系统监控仪表板样式 */
.dashboard-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 页面标题 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: white;
  border-radius: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.refresh-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.refresh-time {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.refresh-btn {
  background: #007aff;
  color: white;
  border: none;
}

/* 加载和错误状态 */
.loading,
.error,
.empty-state {
  text-align: center;
  padding: 100rpx 20rpx;
  background: white;
  border-radius: 10rpx;
  margin: 20rpx 0;
}

.error-text {
  color: #ff3b30;
  margin-bottom: 20rpx;
  display: block;
}

.retry-btn {
  background: #007aff;
  color: white;
  border: none;
}

/* 仪表板内容 */
.dashboard-content {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

/* 通用section样式 */
.section,
.overview-section {
  background: white;
  border-radius: 10rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.detail-btn {
  background: #f0f0f0;
  color: #007aff;
  border: none;
}

/* 概览卡片 */
.overview-cards {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin-top: 20rpx;
}

.overview-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
  border-left: 4rpx solid #007aff;
}

.overview-card.healthy {
  border-left-color: #34c759;
}

.overview-card.warning {
  border-left-color: #ff9500;
}

.card-title {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.card-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

/* 层级分布 */
.level-distribution {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.level-item {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.level-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.level-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.level-count {
  font-size: 26rpx;
  color: #666;
}

.level-bar {
  height: 8rpx;
  background: #e5e5e5;
  border-radius: 4rpx;
  overflow: hidden;
}

.level-progress {
  height: 100%;
  background: linear-gradient(90deg, #007aff, #5ac8fa);
  transition: width 0.3s ease;
}

/* 质量指标 */
.quality-metrics,
.performance-metrics {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.metric-item,
.perf-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}

.metric-label,
.perf-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.metric-value,
.perf-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

/* 异常检测 */
.anomaly-summary {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}

.anomaly-item {
  display: flex;
  align-items: center;
  gap: 10rpx;
  padding: 15rpx 20rpx;
  border-radius: 20rpx;
  font-size: 26rpx;
}

.anomaly-item.high {
  background: #ffebee;
  color: #d32f2f;
}

.anomaly-item.medium {
  background: #fff3e0;
  color: #f57c00;
}

.anomaly-item.low {
  background: #f3e5f5;
  color: #7b1fa2;
}

.anomaly-label {
  font-size: 24rpx;
}

.anomaly-count {
  font-weight: bold;
  font-size: 28rpx;
}

.no-anomaly,
.no-data {
  text-align: center;
  color: #666;
  font-size: 26rpx;
  padding: 40rpx;
}

/* 操作按钮 */
.actions {
  display: flex;
  gap: 20rpx;
  margin-top: 20rpx;
}

.action-btn {
  flex: 1;
  background: #007aff;
  color: white;
  border: none;
  border-radius: 10rpx;
  padding: 25rpx;
  font-size: 28rpx;
}

.action-btn:active {
  background: #0056b3;
}

/* 响应式设计 */
@media (max-width: 600rpx) {
  .overview-cards {
    grid-template-columns: 1fr;
  }

  .quality-metrics,
  .performance-metrics {
    grid-template-columns: 1fr;
  }

  .actions {
    flex-direction: column;
  }
}
