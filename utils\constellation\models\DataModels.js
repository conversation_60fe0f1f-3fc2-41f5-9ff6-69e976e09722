/**
 * Data Models for Interactive Star Constellation System
 * Defines the structure for star and constellation data
 */

/**
 * @typedef {Object} StarPosition
 * @property {number} x - X coordinate (percentage 0-1)
 * @property {number} y - Y coordinate (percentage 0-1)
 * @property {string|null} anchorStarId - ID of anchor star (for orbital stars)
 * @property {number|null} orbitRadius - Orbital radius (for orbital stars)
 * @property {number|null} orbitAngle - Position on orbit in radians (for orbital stars)
 */

/**
 * @typedef {Object} StarMetadata
 * @property {string} dialogueTheme - Theme from dialogue session
 * @property {string} coreFeeling - Answer from first question
 * @property {Date} lightingDate - When star was lit
 * @property {boolean} hasGeneratedDiary - Whether diary was created
 * @property {string} emotionKeyword - Extracted emotion
 * @property {Array} dialogueContent - Full dialogue for reference
 */

/**
 * @typedef {Object} StarVisualProperties
 * @property {number} size - Star size multiplier (0.5-2.0)
 * @property {number} brightness - Star opacity (0.0-1.0)
 * @property {string} color - Star color based on emotion (hex color)
 * @property {number} glowIntensity - Glow effect strength (0.0-1.0)
 */

/**
 * @typedef {Object} StarModel
 * @property {string} id - Unique star identifier
 * @property {string} userId - User openid
 * @property {StarPosition} position - Star position on map
 * @property {StarMetadata} metadata - Star metadata from dialogue
 * @property {StarVisualProperties} visualProperties - Visual appearance properties
 * @property {Date} createdAt - Creation timestamp
 * @property {Date} updatedAt - Last update timestamp
 */

/**
 * @typedef {Object} ConstellationBounds
 * @property {number} minX - Minimum X coordinate
 * @property {number} maxX - Maximum X coordinate
 * @property {number} minY - Minimum Y coordinate
 * @property {number} maxY - Maximum Y coordinate
 */

/**
 * @typedef {Object} ConstellationMetadata
 * @property {number} totalStars - Total number of stars
 * @property {Date} firstStarDate - When first star was placed
 * @property {Date} lastStarDate - Most recent star
 * @property {ConstellationBounds} constellationBounds - Bounding box of constellation
 */

/**
 * @typedef {Object} ConstellationSettings
 * @property {number} defaultZoomLevel - User's preferred zoom (0.5-3.0)
 * @property {Object} centerPoint - Map center point
 * @property {number} centerPoint.x - Center X coordinate
 * @property {number} centerPoint.y - Center Y coordinate
 */

/**
 * @typedef {Object} ConstellationModel
 * @property {string} userId - User openid
 * @property {StarModel[]} stars - Array of user's stars
 * @property {ConstellationMetadata} metadata - Constellation metadata
 * @property {ConstellationSettings} settings - User settings
 * @property {number} version - Data structure version
 * @property {Date} createdAt - Creation timestamp
 * @property {Date} updatedAt - Last update timestamp
 */

/**
 * Star Model Factory
 * Creates new star instances with proper validation
 */
class StarModelFactory {
    /**
     * Create a new star model
     * @param {Object} starData - Star data
     * @returns {StarModel} New star model instance
     */
    static createStar(starData) {
        const now = new Date();

        return {
            id: starData.id || this.generateStarId(),
            userId: starData.userId,
            position: {
                x: starData.position?.x || 0,
                y: starData.position?.y || 0,
                anchorStarId: starData.position?.anchorStarId || null,
                orbitRadius: starData.position?.orbitRadius || null,
                orbitAngle: starData.position?.orbitAngle || null
            },
            metadata: {
                dialogueTheme: starData.metadata?.dialogueTheme || '',
                coreFeeling: starData.metadata?.coreFeeling || '',
                lightingDate: starData.metadata?.lightingDate || now,
                hasGeneratedDiary: starData.metadata?.hasGeneratedDiary || false,
                emotionKeyword: starData.metadata?.emotionKeyword || '',
                dialogueContent: starData.metadata?.dialogueContent || []
            },
            visualProperties: {
                size: starData.visualProperties?.size || 1.0,
                brightness: starData.visualProperties?.brightness || 1.0,
                color: starData.visualProperties?.color || '#FFFFFF',
                glowIntensity: starData.visualProperties?.glowIntensity || 0.5
            },
            createdAt: starData.createdAt || now,
            updatedAt: starData.updatedAt || now
        };
    }

    /**
     * Generate unique star ID
     * @returns {string} Unique star identifier
     */
    static generateStarId() {
        return `star_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * Validate star model data
     * @param {StarModel} star - Star to validate
     * @returns {Object} Validation result
     */
    static validateStar(star) {
        const errors = [];

        if (!star.id) errors.push('Star ID is required');
        if (!star.userId) errors.push('User ID is required');
        if (typeof star.position.x !== 'number' || star.position.x < 0 || star.position.x > 1) {
            errors.push('Position X must be between 0 and 1');
        }
        if (typeof star.position.y !== 'number' || star.position.y < 0 || star.position.y > 1) {
            errors.push('Position Y must be between 0 and 1');
        }

        return {
            valid: errors.length === 0,
            errors
        };
    }
}

/**
 * Constellation Model Factory
 * Creates new constellation instances with proper validation
 */
class ConstellationModelFactory {
    /**
     * Create a new constellation model
     * @param {Object} constellationData - Constellation data
     * @returns {ConstellationModel} New constellation model instance
     */
    static createConstellation(constellationData) {
        const now = new Date();

        return {
            userId: constellationData.userId,
            stars: constellationData.stars || [],
            metadata: {
                totalStars: constellationData.metadata?.totalStars || 0,
                firstStarDate: constellationData.metadata?.firstStarDate || null,
                lastStarDate: constellationData.metadata?.lastStarDate || null,
                constellationBounds: constellationData.metadata?.constellationBounds || {
                    minX: 0, maxX: 1, minY: 0, maxY: 1
                }
            },
            settings: {
                defaultZoomLevel: constellationData.settings?.defaultZoomLevel || 1.0,
                centerPoint: constellationData.settings?.centerPoint || { x: 0.5, y: 0.5 }
            },
            version: constellationData.version || 1,
            createdAt: constellationData.createdAt || now,
            updatedAt: constellationData.updatedAt || now
        };
    }

    /**
     * Update constellation metadata based on stars
     * @param {ConstellationModel} constellation - Constellation to update
     * @returns {ConstellationModel} Updated constellation
     */
    static updateMetadata(constellation) {
        if (!constellation.stars || constellation.stars.length === 0) {
            return constellation;
        }

        // Calculate bounds
        const bounds = {
            minX: Math.min(...constellation.stars.map(s => s.position.x)),
            maxX: Math.max(...constellation.stars.map(s => s.position.x)),
            minY: Math.min(...constellation.stars.map(s => s.position.y)),
            maxY: Math.max(...constellation.stars.map(s => s.position.y))
        };

        // Find date range
        const dates = constellation.stars.map(s => new Date(s.metadata.lightingDate));
        const firstStarDate = new Date(Math.min(...dates));
        const lastStarDate = new Date(Math.max(...dates));

        constellation.metadata = {
            ...constellation.metadata,
            totalStars: constellation.stars.length,
            firstStarDate,
            lastStarDate,
            constellationBounds: bounds
        };

        constellation.updatedAt = new Date();

        return constellation;
    }
}

module.exports = {
    StarModel: StarModelFactory,
    ConstellationModel: ConstellationModelFactory
};