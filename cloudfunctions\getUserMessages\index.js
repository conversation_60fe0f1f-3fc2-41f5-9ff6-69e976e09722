const cloud = require('wx-server-sdk')

cloud.init({
    env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 标记消息为已读
async function markMessageAsRead(messageId, openid) {
    try {
        const result = await db.collection('user_messages')
            .doc(messageId)
            .update({
                data: {
                    isRead: true,
                    readTime: db.serverDate()
                }
            })

        return { success: true, messageId }

    } catch (error) {
        console.error('标记消息已读失败:', error)
        return { success: false, error: error.message }
    }
}

// 批量标记消息为已读
async function markMultipleMessagesAsRead(messageIds, openid) {
    try {
        const batch = db.batch()

        messageIds.forEach(messageId => {
            const messageRef = db.collection('user_messages').doc(messageId)
            batch.update(messageRef, {
                isRead: true,
                readTime: db.serverDate()
            })
        })

        await batch.commit()

        return { success: true, updatedCount: messageIds.length }

    } catch (error) {
        console.error('批量标记消息已读失败:', error)
        return { success: false, error: error.message }
    }
}

// 删除消息
async function deleteMessage(messageId, openid) {
    try {
        // 验证消息属于当前用户
        const message = await db.collection('user_messages')
            .doc(messageId)
            .get()

        if (!message.data || message.data.openid !== openid) {
            return { success: false, error: '消息不存在或无权限删除' }
        }

        await db.collection('user_messages').doc(messageId).remove()

        return { success: true, messageId }

    } catch (error) {
        console.error('删除消息失败:', error)
        return { success: false, error: error.message }
    }
}

// 获取消息统计信息
async function getMessageStats(openid) {
    try {
        const [totalCount, unreadCount, todayCount] = await Promise.all([
            db.collection('user_messages').where({ openid }).count(),
            db.collection('user_messages').where({ openid, isRead: false }).count(),
            db.collection('user_messages').where({
                openid,
                createTime: db.command.gte(new Date(new Date().setHours(0, 0, 0, 0)))
            }).count()
        ])

        return {
            success: true,
            stats: {
                total: totalCount.total,
                unread: unreadCount.total,
                today: todayCount.total
            }
        }

    } catch (error) {
        console.error('获取消息统计失败:', error)
        return { success: false, error: error.message }
    }
}

exports.main = async (event, context) => {
    console.log('用户消息管理请求:', JSON.stringify(event))

    const {
        action = 'list',   // 操作类型：list, markRead, markMultipleRead, delete, stats
        limit = 20,        // 每页消息数量
        skip = 0,          // 跳过的消息数量
        type = null,       // 消息类型过滤
        unreadOnly = false, // 是否只获取未读消息
        messageId,         // 消息ID（用于标记已读或删除）
        messageIds = []    // 消息ID数组（用于批量操作）
    } = event

    const wxContext = cloud.getWXContext()
    const openid = wxContext.OPENID

    console.log('当前用户 openid:', openid)

    try {
        switch (action) {
            case 'list':
                // 构建查询条件
                let query = db.collection('user_messages').where({
                    openid: openid
                })

                // 添加类型过滤
                if (type) {
                    query = query.where({
                        type: type
                    })
                }

                // 添加未读过滤
                if (unreadOnly) {
                    query = query.where({
                        isRead: false
                    })
                }

                // 执行查询
                const result = await query
                    .orderBy('createTime', 'desc')
                    .skip(skip)
                    .limit(limit)
                    .get()

                console.log(`找到 ${result.data.length} 条消息`)

                // 获取未读消息总数
                const unreadCount = await db.collection('user_messages')
                    .where({
                        openid: openid,
                        isRead: false
                    })
                    .count()

                return {
                    success: true,
                    messages: result.data,
                    unreadCount: unreadCount.total,
                    hasMore: result.data.length === limit
                }

            case 'markRead':
                if (!messageId) {
                    return { success: false, error: '缺少 messageId 参数' }
                }
                return await markMessageAsRead(messageId, openid)

            case 'markMultipleRead':
                if (!messageIds || messageIds.length === 0) {
                    return { success: false, error: '缺少 messageIds 参数' }
                }
                return await markMultipleMessagesAsRead(messageIds, openid)

            case 'delete':
                if (!messageId) {
                    return { success: false, error: '缺少 messageId 参数' }
                }
                return await deleteMessage(messageId, openid)

            case 'stats':
                return await getMessageStats(openid)

            default:
                return {
                    success: false,
                    error: '不支持的操作类型',
                    supportedActions: ['list', 'markRead', 'markMultipleRead', 'delete', 'stats']
                }
        }

    } catch (error) {
        console.error('用户消息管理失败:', error)
        return {
            success: false,
            error: error.message,
            errorCode: error.errCode
        }
    }
}