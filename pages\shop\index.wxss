.container {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.product-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.product-item {
  background: #ffffff;
  border-radius: 12rpx;
  padding: 20rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.product-image {
  width: 120rpx;
  height: 120rpx;
  margin-right: 20rpx;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.product-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.product-price {
  font-size: 36rpx;
  color: #ff6b6b;
  font-weight: bold;
}

.product-points {
  font-size: 24rpx;
  color: #666;
}

.product-type {
  font-size: 24rpx;
  color: #ff9f43;
}

.buy-btn {
  background: #4834d4;
  color: white;
  border: none;
  padding: 16rpx 32rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.buy-btn[disabled] {
  background: #a8a8a8;
  color: #ffffff;
} 