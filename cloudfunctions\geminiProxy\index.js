const cloud = require('wx-server-sdk');
const fetch = require('node-fetch'); // 需要在云函数中安装 node-fetch

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });

// 引入AI提示词配置
const aiPrompts = require('./aiPrompts.js');

// OpenRouter API 配置
const OPENROUTER_API_KEY = '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';
const OPENROUTER_API_URL = 'https://openrouter.ai/api/v1/chat/completions';
const MODEL_NAME = 'google/gemini-2.5-flash';

// 错误类型定义
const ERROR_TYPES = {
  VALIDATION_ERROR: 'validation_error',
  AI_SERVICE_ERROR: 'ai_service_error',
  NETWORK_ERROR: 'network_error',
  TIMEOUT_ERROR: 'timeout_error',
  RATE_LIMIT_ERROR: 'rate_limit_error',
  SYSTEM_ERROR: 'system_error'
};

// 重试配置
const RETRY_CONFIG = {
  maxRetries: 3,
  baseDelay: 1000,
  maxDelay: 15000,
  backoffFactor: 2
};

// 超时配置
const TIMEOUT_CONFIG = {
  aiGeneration: 15000,    // AI生成超时 15秒
  emotionAnalysis: 8000,  // 情绪分析超时 8秒
  endingMessage: 10000    // 结束语生成超时 10秒
};

// 错误处理工具函数
function createErrorResponse(errorType, message, details = {}) {
  return {
    success: false,
    error: message,
    errorType,
    timestamp: new Date().toISOString(),
    details,
    retryable: isRetryableError(errorType),
    fallback: true
  };
}

// 判断错误是否可重试
function isRetryableError(errorType) {
  const retryableErrors = [
    ERROR_TYPES.NETWORK_ERROR,
    ERROR_TYPES.TIMEOUT_ERROR,
    ERROR_TYPES.AI_SERVICE_ERROR
  ];
  return retryableErrors.includes(errorType);
}

// 指数退避重试函数
async function retryWithBackoff(operation, options = {}) {
  const {
    maxRetries = RETRY_CONFIG.maxRetries,
    baseDelay = RETRY_CONFIG.baseDelay,
    maxDelay = RETRY_CONFIG.maxDelay,
    backoffFactor = RETRY_CONFIG.backoffFactor,
    retryCondition = null
  } = options;

  let lastError;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;

      // 检查是否应该重试
      if (retryCondition && !retryCondition(error)) {
        throw error;
      }

      if (attempt === maxRetries) {
        throw error;
      }

      // 计算延迟时间
      const delay = Math.min(baseDelay * Math.pow(backoffFactor, attempt), maxDelay);
      console.log(`AI操作失败，${delay}ms后进行第${attempt + 2}次重试:`, error.message);

      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError;
}

// 超时包装函数
function withTimeout(promise, timeoutMs, errorMessage = 'AI操作超时') {
  return Promise.race([
    promise,
    new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error(errorMessage));
      }, timeoutMs);
    })
  ]);
}

// 记录错误到日志系统
async function logError(error, context, additionalData = {}) {
  try {
    const errorLog = {
      timestamp: new Date().toISOString(),
      context,
      error: {
        message: error.message,
        stack: error.stack,
        code: error.code || error.errCode
      },
      additionalData
    };

    console.error(`[AI服务错误日志] ${context}:`, errorLog);

    // 记录到分析系统
    try {
      await cloud.callFunction({
        name: 'tieringAnalytics',
        data: {
          type: 'recordAIServiceError',
          data: {
            errorType: context,
            errorMessage: error.message,
            timestamp: new Date(),
            additionalData
          }
        }
      });
    } catch (analyticsError) {
      console.error('记录AI服务错误到分析系统失败:', analyticsError);
    }

  } catch (logError) {
    console.error('记录AI服务错误日志失败:', logError);
  }
}

// 提示内容缓存机制
const promptCache = new Map();
const CACHE_EXPIRY_TIME = 30 * 60 * 1000; // 30分钟缓存过期时间

// 获取缓存的提示内容
function getCachedPrompt(level) {
  const cacheKey = `level_${level}`;
  const cached = promptCache.get(cacheKey);

  if (cached && (Date.now() - cached.timestamp) < CACHE_EXPIRY_TIME) {
    console.log('使用缓存的层级提示:', level);
    return cached.data;
  }

  return null;
}

// 设置提示内容缓存
function setCachedPrompt(level, promptData) {
  const cacheKey = `level_${level}`;
  promptCache.set(cacheKey, {
    data: promptData,
    timestamp: Date.now()
  });
  console.log('缓存层级提示:', level);
}

// 记录对话指标的辅助函数
async function recordDialogueMetrics(openid, userLevel, responseTime, userQuestion, aiResponse, hasError, errorType = null) {
  if (!openid) return; // 如果没有openid，跳过记录

  try {
    await cloud.callFunction({
      name: 'tieringAnalytics',
      data: {
        type: 'recordDialogueMetrics',
        data: {
          openid,
          userLevel,
          responseTime,
          dialogueLength: (userQuestion?.length || 0) + (aiResponse?.length || 0),
          satisfactionScore: hasError ? 1 : 4, // 简单的满意度评分，错误时为1，正常时为4
          completed: !hasError,
          hasError,
          errorType
        }
      }
    });
    console.log('对话指标记录成功');
  } catch (error) {
    console.error('记录对话指标失败:', error);
    // 不影响主流程，只记录错误
  }
}

// 记录结束语生成性能指标的辅助函数
async function recordEndingMessageMetrics(openid, userLevel, performanceMetrics, endingMessage, errorMessage = null) {
  if (!openid) return; // 如果没有openid，跳过记录

  try {
    // 计算总时间
    performanceMetrics.totalTime = Date.now() - performanceMetrics.startTime;
    performanceMetrics.success = !performanceMetrics.errorType && !errorMessage;

    // 确定A/B测试组别（基于用户openid的哈希值）
    const abTestGroup = determineABTestGroup(openid);

    await cloud.callFunction({
      name: 'tieringAnalytics',
      data: {
        type: 'recordEndingMessageMetrics',
        data: {
          openid,
          userLevel,
          totalTime: performanceMetrics.totalTime,
          emotionAnalysisTime: performanceMetrics.emotionAnalysisTime,
          aiGenerationTime: performanceMetrics.aiGenerationTime,
          success: performanceMetrics.success,
          errorType: performanceMetrics.errorType,
          fallbackUsed: performanceMetrics.fallbackUsed,
          emotionAnalysisSuccess: performanceMetrics.emotionAnalysisSuccess,
          aiGenerationSuccess: performanceMetrics.aiGenerationSuccess,
          endingMessageLength: endingMessage?.length || 0,
          errorMessage: errorMessage,
          abTestGroup: abTestGroup, // A/B测试组别
          timestamp: new Date()
        }
      }
    });
    console.log('结束语生成指标记录成功');
  } catch (error) {
    console.error('记录结束语生成指标失败:', error);
    // 不影响主流程，只记录错误
  }
}

// 确定A/B测试组别的辅助函数
function determineABTestGroup(openid) {
  if (!openid) return 'unknown';

  // 使用简单的哈希算法基于openid确定组别
  let hash = 0;
  for (let i = 0; i < openid.length; i++) {
    const char = openid.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // 转换为32位整数
  }

  // 将用户分为A、B两组，各占50%
  return Math.abs(hash) % 2 === 0 ? 'A' : 'B';
}

// 记录用户对结束语的反馈数据
async function recordEndingMessageFeedback(openid, userLevel, endingMessage, feedbackType, feedbackData = {}) {
  if (!openid) return;

  try {
    const abTestGroup = determineABTestGroup(openid);

    await cloud.callFunction({
      name: 'tieringAnalytics',
      data: {
        type: 'recordEndingMessageFeedback',
        data: {
          openid,
          userLevel,
          endingMessage: endingMessage?.substring(0, 200), // 限制长度避免存储过多数据
          feedbackType, // 'positive', 'negative', 'neutral', 'skip', 'proceed'
          feedbackData, // 额外的反馈数据
          abTestGroup,
          timestamp: new Date()
        }
      }
    });
    console.log('结束语反馈记录成功');
  } catch (error) {
    console.error('记录结束语反馈失败:', error);
  }
}

// 结束语生成模块 - 基于对话内容和情绪分析生成个性化结束语
async function generateEndingMessage(dialogueContent, dialogueSummary, openid, userLevel = 3) {
  console.log('开始生成结束语...');
  const startTime = Date.now();

  // 初始化性能监控数据
  const performanceMetrics = {
    startTime,
    emotionAnalysisTime: 0,
    aiGenerationTime: 0,
    totalTime: 0,
    success: false,
    errorType: null,
    fallbackUsed: false,
    emotionAnalysisSuccess: false,
    aiGenerationSuccess: false
  };

  try {
    // 1. 首先进行情绪分析，带超时控制
    let emotionAnalysis;
    const emotionAnalysisStart = Date.now();
    try {
      const emotionPromise = analyzeDialogueEmotion(dialogueContent);
      emotionAnalysis = await Promise.race([
        emotionPromise,
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('情绪分析超时')), 3000)
        )
      ]);
      performanceMetrics.emotionAnalysisTime = Date.now() - emotionAnalysisStart;
      performanceMetrics.emotionAnalysisSuccess = true;
      console.log('情绪分析结果:', emotionAnalysis);
    } catch (emotionError) {
      performanceMetrics.emotionAnalysisTime = Date.now() - emotionAnalysisStart;
      performanceMetrics.emotionAnalysisSuccess = false;
      performanceMetrics.errorType = 'emotion_analysis_failed';
      console.error('情绪分析失败:', emotionError);
      // 使用默认情绪分析结果
      emotionAnalysis = {
        coreEmotion: '平静',
        confidence: 0.5,
        supportingEvidence: [],
        alternativeEmotions: [],
        fallback: true
      };
      console.log('使用默认情绪分析结果:', emotionAnalysis);
    }

    // 2. 提取完整对话内容用于结束语生成
    const userMessages = dialogueContent
      .filter(msg => msg.role === 'user')
      .map(msg => msg.content)
      .join('\n');

    if (!userMessages.trim()) {
      console.log('没有用户消息，返回默认结束语');
      return generateFallbackEndingMessage('平静', '没有用户消息', null, userLevel);
    }

    // 构建完整的对话上下文，包含用户和AI的对话
    const fullDialogueContext = dialogueContent
      .map(msg => `${msg.role === 'user' ? '用户' : 'AI'}：${msg.content}`)
      .join('\n');

    console.log('完整对话上下文长度:', fullDialogueContext.length);
    console.log('对话总结:', dialogueSummary);

    // 3. 使用AI提示模板生成结束语，带错误处理
    let endingPrompt;
    try {
      endingPrompt = aiPrompts.endingMessage.buildEndingPrompt(
        fullDialogueContext, // 传递完整对话而不是只有用户消息
        emotionAnalysis.coreEmotion,
        dialogueSummary || '这次对话很有意义'
      );
      console.log('结束语生成提示长度:', endingPrompt.length);
    } catch (promptError) {
      console.error('构建结束语提示失败:', promptError);
      return generateFallbackEndingMessage(emotionAnalysis.coreEmotion, '提示构建失败', null, userLevel);
    }

    // 4. 调用AI生成结束语，带超时控制
    let generationResult;
    const aiGenerationStart = Date.now();
    try {
      const aiPromise = callOpenRouterAPI(endingPrompt, []);
      generationResult = await Promise.race([
        aiPromise,
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('AI生成超时')), 5000)
        )
      ]);
      performanceMetrics.aiGenerationTime = Date.now() - aiGenerationStart;
      performanceMetrics.aiGenerationSuccess = true;
    } catch (aiError) {
      performanceMetrics.aiGenerationTime = Date.now() - aiGenerationStart;
      performanceMetrics.aiGenerationSuccess = false;
      performanceMetrics.errorType = performanceMetrics.errorType || 'ai_generation_failed';
      performanceMetrics.fallbackUsed = true;
      console.error('AI生成结束语失败:', aiError);

      // 记录性能指标到分析系统
      await recordEndingMessageMetrics(openid, userLevel, performanceMetrics, null, aiError.message);

      return generateFallbackEndingMessage(emotionAnalysis.coreEmotion, 'AI生成失败', null, userLevel);
    }

    if (generationResult.success && generationResult.response) {
      const endingMessage = generationResult.response.trim();

      // 5. 验证结束语长度和质量
      if (!endingMessage || endingMessage.length < 5) {
        console.log('结束语内容过短或为空，使用备用方案');
        return generateFallbackEndingMessage(emotionAnalysis.coreEmotion, '生成内容无效', null, userLevel);
      }

      if (endingMessage.length > 100) {
        console.log('结束语过长，进行截取');
        const truncatedMessage = endingMessage.substring(0, 97) + '...';
        return {
          success: true,
          endingMessage: truncatedMessage,
          emotion: emotionAnalysis.coreEmotion,
          confidence: emotionAnalysis.confidence,
          originalLength: endingMessage.length,
          truncated: true,
          generationTime: Date.now() - startTime
        };
      }

      // 记录成功的性能指标
      performanceMetrics.success = true;
      await recordEndingMessageMetrics(openid, userLevel, performanceMetrics, endingMessage);

      return {
        success: true,
        endingMessage: endingMessage,
        emotion: emotionAnalysis.coreEmotion,
        confidence: emotionAnalysis.confidence,
        generationTime: Date.now() - startTime,
        fallback: false
      };
    } else {
      console.log('AI生成结束语失败，使用备用方案');
      performanceMetrics.fallbackUsed = true;
      performanceMetrics.errorType = performanceMetrics.errorType || 'ai_response_invalid';

      // 记录性能指标到分析系统
      await recordEndingMessageMetrics(openid, userLevel, performanceMetrics, null, 'AI响应无效');

      return generateFallbackEndingMessage(emotionAnalysis.coreEmotion, 'AI响应无效', null, userLevel);
    }
  } catch (error) {
    console.error('生成结束语异常:', error);

    // 记录性能指标到分析系统
    performanceMetrics.errorType = 'system_exception';
    performanceMetrics.fallbackUsed = true;
    await recordEndingMessageMetrics(openid, userLevel, performanceMetrics, null, error.message);

    // 记录错误详情用于监控
    const errorDetails = {
      error: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString(),
      openid: openid,
      userLevel: userLevel,
      dialogueLength: dialogueContent ? dialogueContent.length : 0
    };
    console.error('结束语生成错误详情:', errorDetails);

    return generateFallbackEndingMessage('平静', '系统异常', null, userLevel);
  }
}

// 备用结束语生成方案 - 增强版错误处理，使用智能选择
function generateFallbackEndingMessage(emotion, errorReason = '未知错误', dialogueTheme = null, userLevel = 3) {
  console.log('使用备用结束语方案，情绪:', emotion, '错误原因:', errorReason, '主题:', dialogueTheme, '层级:', userLevel);

  // 记录备用方案使用情况，用于监控和优化
  const fallbackUsageLog = {
    timestamp: new Date().toISOString(),
    emotion: emotion,
    errorReason: errorReason,
    dialogueTheme: dialogueTheme,
    userLevel: userLevel,
    fallbackType: 'ending_message'
  };
  console.log('备用结束语使用记录:', fallbackUsageLog);

  let selectedTemplate;
  let confidence = 0.7;
  let templateSource = 'unknown';

  try {
    // 使用AI提示配置中的智能备用选择函数
    const fallbackResult = aiPrompts.endingMessage.selectFallbackMessage(
      dialogueTheme,
      userLevel,
      errorReason
    );

    selectedTemplate = fallbackResult.message;
    templateSource = fallbackResult.source;
    confidence = 0.7;

    console.log('智能选择备用结束语结果:', fallbackResult);
  } catch (selectionError) {
    console.error('智能选择备用结束语失败:', selectionError);

    // 基于情绪的简单备用方案
    const emotionBasedFallbacks = {
      '迷茫': '迷茫也是成长的一部分，我会一直陪着你。',
      '坚定': '看见你内心的坚定，这份力量会指引你前行。',
      '疲惫': '累了就休息，我在这里守护着你的光。',
      '焦虑': '焦虑背后是你对生活的在意，这也是一种力量。',
      '释然': '看见你的释然，这是内在智慧的绽放。',
      '困惑': '困惑是思考的开始，答案就在你心中。',
      '激动': '感受到你内心的光芒，这份喜悦很珍贵。',
      '平静': '你内心的平静就是最好的力量。'
    };

    // 通用备用结束语（最后的兜底方案）
    const universalFallbacks = [
      '感谢你的分享，我会一直在这里陪伴你。',
      '每一次对话都是珍贵的，我陪你继续前行。',
      '你的勇气让我感动，我会一直守护着你的光。',
      '无论何时，我都是你内心最忠实的伙伴。',
      '这次对话很有意义，我会记在心里。'
    ];

    try {
      // 尝试根据情绪选择模板
      if (emotion && emotionBasedFallbacks[emotion]) {
        selectedTemplate = emotionBasedFallbacks[emotion];
        templateSource = 'emotion_fallback';
        confidence = 0.6;
      } else {
        // 使用通用备用
        selectedTemplate = universalFallbacks[Math.floor(Math.random() * universalFallbacks.length)];
        templateSource = 'universal_fallback';
        confidence = 0.5;
      }
    } catch (templateError) {
      console.error('选择备用模板失败:', templateError);
      // 最后的紧急兜底方案
      selectedTemplate = '感谢你的分享，我会一直在这里陪伴你。';
      templateSource = 'emergency';
      confidence = 0.3;
    }
  }

  // 验证选择的模板
  if (!selectedTemplate || selectedTemplate.trim().length < 5) {
    console.error('备用结束语无效:', selectedTemplate);
    selectedTemplate = '感谢你的分享，我会一直在这里陪伴你。';
    templateSource = 'hardcoded_emergency';
    confidence = 0.3;
  }

  return {
    success: true,
    endingMessage: selectedTemplate,
    emotion: emotion,
    confidence: confidence,
    fallback: true,
    errorReason: errorReason,
    generationTime: 0, // 备用方案无需生成时间
    templateSource: templateSource,
    dialogueTheme: dialogueTheme,
    userLevel: userLevel
  };
}

// 情绪分析模块 - 从对话内容中提取核心情绪关键词（增强版错误处理）
async function analyzeDialogueEmotion(dialogueContent) {
  console.log('开始分析对话情绪...');
  const startTime = Date.now();

  // 输入验证
  if (!dialogueContent || dialogueContent.length === 0) {
    console.log('对话内容为空，返回默认情绪');
    return {
      coreEmotion: '平静',
      confidence: 0.5,
      supportingEvidence: ['对话内容为空'],
      alternativeEmotions: [],
      fallback: true,
      errorReason: '对话内容为空'
    };
  }

  try {
    // 提取用户消息内容进行情绪分析
    let userMessages;
    try {
      userMessages = dialogueContent
        .filter(msg => msg && msg.role === 'user' && msg.content)
        .map(msg => msg.content)
        .join(' ');
    } catch (filterError) {
      console.error('提取用户消息失败:', filterError);
      return {
        coreEmotion: '平静',
        confidence: 0.3,
        supportingEvidence: ['消息提取失败'],
        alternativeEmotions: [],
        fallback: true,
        errorReason: '消息提取失败'
      };
    }

    if (!userMessages || !userMessages.trim()) {
      console.log('没有有效的用户消息，返回默认情绪');
      return {
        coreEmotion: '平静',
        confidence: 0.5,
        supportingEvidence: ['没有用户消息'],
        alternativeEmotions: [],
        fallback: true,
        errorReason: '没有用户消息'
      };
    }

    // 首先尝试关键词匹配作为快速备用方案
    const keywordResult = analyzeEmotionByKeywords(userMessages);
    console.log('关键词分析结果:', keywordResult);

    // 使用AI进行更精确的情绪分析，带超时控制
    try {
      let emotionAnalysisPrompt;
      try {
        emotionAnalysisPrompt = aiPrompts.emotionAnalysis.buildAnalysisPrompt(userMessages);
      } catch (promptError) {
        console.error('构建情绪分析提示失败:', promptError);
        // 返回关键词分析结果作为备用
        return {
          ...keywordResult,
          fallback: true,
          errorReason: '提示构建失败',
          analysisTime: Date.now() - startTime
        };
      }

      const aiPromise = callOpenRouterAPI(emotionAnalysisPrompt, []);
      const analysisResult = await Promise.race([
        aiPromise,
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('AI情绪分析超时')), 3000)
        )
      ]);

      if (analysisResult.success && analysisResult.response) {
        // 解析AI返回的情绪分析结果
        let parsedResult;
        try {
          parsedResult = parseEmotionAnalysisResult(analysisResult.response);
          console.log('AI情绪分析完成:', parsedResult);

          // 验证AI分析结果的有效性
          if (!parsedResult.coreEmotion || parsedResult.confidence < 0.3) {
            console.log('AI分析结果置信度过低，使用关键词分析结果');
            return {
              ...keywordResult,
              fallback: true,
              errorReason: 'AI分析置信度过低',
              analysisTime: Date.now() - startTime
            };
          }

          return {
            ...parsedResult,
            fallback: false,
            analysisTime: Date.now() - startTime
          };
        } catch (parseError) {
          console.error('解析AI情绪分析结果失败:', parseError);
          return {
            ...keywordResult,
            fallback: true,
            errorReason: '解析AI结果失败',
            analysisTime: Date.now() - startTime
          };
        }
      } else {
        console.log('AI情绪分析失败，使用关键词匹配备用方案');
        return {
          ...keywordResult,
          fallback: true,
          errorReason: 'AI分析失败',
          analysisTime: Date.now() - startTime
        };
      }
    } catch (aiError) {
      console.error('AI情绪分析异常:', aiError);
      return {
        ...keywordResult,
        fallback: true,
        errorReason: aiError.message || 'AI分析异常',
        analysisTime: Date.now() - startTime
      };
    }
  } catch (error) {
    console.error('情绪分析整体异常:', error);

    // 记录错误详情用于监控
    const errorDetails = {
      error: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString(),
      dialogueLength: dialogueContent ? dialogueContent.length : 0
    };
    console.error('情绪分析错误详情:', errorDetails);

    // 最后的兜底方案
    return {
      coreEmotion: '平静',
      confidence: 0.3,
      supportingEvidence: ['系统异常'],
      alternativeEmotions: [],
      fallback: true,
      errorReason: '系统异常',
      analysisTime: Date.now() - startTime
    };
  }
}

// 解析AI返回的情绪分析结果
function parseEmotionAnalysisResult(aiResponse) {
  try {
    // 尝试解析JSON格式的响应
    if (aiResponse.includes('{') && aiResponse.includes('}')) {
      const jsonMatch = aiResponse.match(/\{[^}]+\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        return {
          coreEmotion: parsed.coreEmotion || '平静',
          confidence: parsed.confidence || 0.5,
          supportingEvidence: parsed.supportingEvidence || [],
          alternativeEmotions: parsed.alternativeEmotions || []
        };
      }
    }

    // 如果不是JSON格式，尝试从文本中提取情绪关键词
    const emotionKeywords = ['迷茫', '坚定', '疲惫', '焦虑', '释然', '困惑', '清晰', '纠结', '平静', '激动'];
    let detectedEmotion = '平静';
    let maxConfidence = 0.5;

    for (const emotion of emotionKeywords) {
      if (aiResponse.includes(emotion)) {
        detectedEmotion = emotion;
        maxConfidence = 0.8;
        break;
      }
    }

    return {
      coreEmotion: detectedEmotion,
      confidence: maxConfidence,
      supportingEvidence: [aiResponse.substring(0, 100)],
      alternativeEmotions: []
    };
  } catch (error) {
    console.error('解析情绪分析结果失败:', error);
    return {
      coreEmotion: '平静',
      confidence: 0.5,
      supportingEvidence: [],
      alternativeEmotions: []
    };
  }
}

// 基于关键词的情绪分析备用方案
function analyzeEmotionByKeywords(userText) {
  console.log('使用关键词匹配进行情绪分析');

  const emotionPatterns = {
    '迷茫': ['不知道', '迷茫', '困惑', '不清楚', '不明白', '怎么办', '不确定'],
    '坚定': ['坚定', '确定', '明确', '清楚', '决定', '肯定', '一定要'],
    '疲惫': ['累', '疲惫', '疲劳', '疲倦', '没力气', '撑不住', '精疲力尽'],
    '焦虑': ['焦虑', '担心', '紧张', '害怕', '恐惧', '不安', '忧虑'],
    '释然': ['释然', '放下', '想开了', '轻松', '解脱', '舒服', '平静'],
    '困惑': ['困惑', '纠结', '矛盾', '挣扎', '两难', '不知所措'],
    '激动': ['激动', '兴奋', '开心', '高兴', '喜悦', '振奋'],
    '平静': ['平静', '冷静', '淡定', '安静', '稳定']
  };

  let bestMatch = {
    emotion: '平静',
    confidence: 0.3,
    matchCount: 0
  };

  // 计算每种情绪的匹配度
  for (const [emotion, keywords] of Object.entries(emotionPatterns)) {
    let matchCount = 0;
    const evidence = [];

    for (const keyword of keywords) {
      const regex = new RegExp(keyword, 'gi');
      const matches = userText.match(regex);
      if (matches) {
        matchCount += matches.length;
        evidence.push(`包含"${keyword}"`);
      }
    }

    if (matchCount > bestMatch.matchCount) {
      bestMatch = {
        emotion,
        confidence: Math.min(0.9, 0.5 + matchCount * 0.1),
        matchCount,
        evidence
      };
    }
  }

  return {
    coreEmotion: bestMatch.emotion,
    confidence: bestMatch.confidence,
    supportingEvidence: bestMatch.evidence || [],
    alternativeEmotions: []
  };
}

// 调用OpenRouter API的辅助函数（用于情绪分析和日记生成）
async function callOpenRouterAPI(prompt, messages, options = {}) {
  try {
    const requestBody = {
      model: MODEL_NAME,
      messages: [
        { role: 'system', content: prompt },
        ...messages
      ],
      temperature: options.temperature || 0.3, // 默认较低随机性
      max_tokens: options.maxTokens || 500
    };

    const response = await fetch(OPENROUTER_API_URL, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://weiguang.app',
        'X-Title': 'WeiguangApp'
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status}`);
    }

    const data = await response.json();

    if (data.choices && data.choices[0] && data.choices[0].message) {
      return {
        success: true,
        response: data.choices[0].message.content
      };
    } else {
      throw new Error('API响应格式异常');
    }
  } catch (error) {
    console.error('调用OpenRouter API失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// AI日记生成核心功能 - VIP专属功能
async function generateAIDiary(dialogueContent, userLevel, dailyFeeling, dialogueTheme, openid) {
  console.log('开始生成AI日记...', { userLevel, dailyFeeling, dialogueTheme, dialogueRounds: dialogueContent.length });
  const startTime = Date.now();

  // 初始化性能监控数据
  const performanceMetrics = {
    startTime,
    styleAnalysisTime: 0,
    aiGenerationTime: 0,
    qualityValidationTime: 0,
    totalTime: 0,
    success: false,
    errorType: null,
    fallbackUsed: false,
    styleAnalysisSuccess: false,
    aiGenerationSuccess: false,
    qualityValidationSuccess: false
  };

  try {
    // 1. 输入验证
    if (!dialogueContent || dialogueContent.length === 0) {
      console.log('对话内容为空，使用备用日记');
      return generateFallbackDiary(userLevel, dialogueTheme, dailyFeeling, '对话内容为空');
    }

    // 2. 用户语言风格分析
    let styleAnalysis;
    const styleAnalysisStart = Date.now();
    try {
      styleAnalysis = analyzeUserLanguageStyle(dialogueContent);
      performanceMetrics.styleAnalysisTime = Date.now() - styleAnalysisStart;
      performanceMetrics.styleAnalysisSuccess = true;
      console.log('用户语言风格分析完成:', styleAnalysis);
    } catch (styleError) {
      performanceMetrics.styleAnalysisTime = Date.now() - styleAnalysisStart;
      performanceMetrics.styleAnalysisSuccess = false;
      performanceMetrics.errorType = 'style_analysis_failed';
      console.error('语言风格分析失败:', styleError);
      // 使用默认风格分析
      styleAnalysis = {
        styleType: '年轻直接型',
        confidence: 0.5,
        keyFeatures: ['直接表达'],
        fallback: true
      };
    }

    // 3. 构建日记生成提示
    let diaryPrompt;
    try {
      diaryPrompt = aiPrompts.diaryGeneration.buildDiaryPrompt(
        dialogueContent,
        userLevel,
        dailyFeeling,
        dialogueTheme
      );
      console.log('日记生成提示构建完成，长度:', diaryPrompt.length);
    } catch (promptError) {
      console.error('构建日记生成提示失败:', promptError);
      return generateFallbackDiary(userLevel, dialogueTheme, dailyFeeling, '提示构建失败');
    }

    // 4. 调用AI生成日记，带超时控制
    let generationResult;
    const aiGenerationStart = Date.now();
    try {
      const aiPromise = callOpenRouterAPI(diaryPrompt, [], {
        temperature: 0.7, // 日记生成需要更多创造性
        maxTokens: 800   // 增加token限制以支持更长的日记
      });

      generationResult = await Promise.race([
        aiPromise,
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('AI日记生成超时')), 8000)
        )
      ]);

      performanceMetrics.aiGenerationTime = Date.now() - aiGenerationStart;
      performanceMetrics.aiGenerationSuccess = true;
    } catch (aiError) {
      performanceMetrics.aiGenerationTime = Date.now() - aiGenerationStart;
      performanceMetrics.aiGenerationSuccess = false;
      performanceMetrics.errorType = performanceMetrics.errorType || 'ai_generation_failed';
      performanceMetrics.fallbackUsed = true;
      console.error('AI日记生成失败:', aiError);

      // 记录性能指标
      await recordDiaryGenerationMetrics(openid, userLevel, performanceMetrics, null, aiError.message);

      return generateFallbackDiary(userLevel, dialogueTheme, dailyFeeling, 'AI生成失败');
    }

    if (generationResult.success && generationResult.response) {
      const rawDiary = generationResult.response.trim();

      // 5. 质量验证和优化
      let validatedDiary;
      const qualityValidationStart = Date.now();
      try {
        validatedDiary = validateAndOptimizeDiary(rawDiary, dialogueContent, userLevel, dailyFeeling);
        performanceMetrics.qualityValidationTime = Date.now() - qualityValidationStart;
        performanceMetrics.qualityValidationSuccess = true;
        console.log('日记质量验证完成:', {
          originalLength: rawDiary.length,
          finalLength: validatedDiary.diary.length,
          qualityScore: validatedDiary.qualityScore
        });
      } catch (validationError) {
        performanceMetrics.qualityValidationTime = Date.now() - qualityValidationStart;
        performanceMetrics.qualityValidationSuccess = false;
        console.error('日记质量验证失败:', validationError);
        // 使用原始日记，但进行基本清理
        validatedDiary = {
          diary: rawDiary.substring(0, 500), // 限制长度
          qualityScore: 0.5,
          issues: ['质量验证失败'],
          fallback: false
        };
      }

      // 6. 最终验证
      if (!validatedDiary.diary || validatedDiary.diary.length < 50) {
        console.log('生成的日记内容过短或为空，使用备用方案');
        return generateFallbackDiary(userLevel, dialogueTheme, dailyFeeling, '生成内容无效');
      }

      // 记录成功的性能指标
      performanceMetrics.success = true;
      performanceMetrics.totalTime = Date.now() - startTime;
      await recordDiaryGenerationMetrics(openid, userLevel, performanceMetrics, validatedDiary.diary);

      return {
        success: true,
        diaryContent: validatedDiary.diary,
        wordCount: validatedDiary.diary.length,
        qualityScore: validatedDiary.qualityScore,
        styleAnalysis: styleAnalysis,
        generationTime: performanceMetrics.totalTime,
        fallback: false,
        performanceMetrics: performanceMetrics
      };
    } else {
      console.log('AI日记生成失败，使用备用方案');
      performanceMetrics.fallbackUsed = true;
      performanceMetrics.errorType = performanceMetrics.errorType || 'ai_response_invalid';

      // 记录性能指标
      await recordDiaryGenerationMetrics(openid, userLevel, performanceMetrics, null, 'AI响应无效');

      return generateFallbackDiary(userLevel, dialogueTheme, dailyFeeling, 'AI响应无效');
    }
  } catch (error) {
    console.error('生成AI日记异常:', error);

    // 记录性能指标
    performanceMetrics.errorType = 'system_exception';
    performanceMetrics.fallbackUsed = true;
    performanceMetrics.totalTime = Date.now() - startTime;
    await recordDiaryGenerationMetrics(openid, userLevel, performanceMetrics, null, error.message);

    return generateFallbackDiary(userLevel, dialogueTheme, dailyFeeling, '系统异常');
  }
}

// 用户语言风格分析算法
function analyzeUserLanguageStyle(dialogueContent) {
  console.log('开始分析用户语言风格...');

  try {
    // 提取用户消息
    const userMessages = dialogueContent
      .filter(msg => msg && msg.role === 'user' && msg.content)
      .map(msg => msg.content)
      .join(' ');

    if (!userMessages.trim()) {
      return {
        styleType: '年轻直接型',
        confidence: 0.3,
        keyFeatures: ['无足够文本分析'],
        fallback: true
      };
    }

    // 获取风格模式配置
    const stylePatterns = aiPrompts.diaryGeneration.styleAnalysis.patterns;
    const analysisResults = {};

    // 分析每种风格的匹配度
    for (const [styleType, pattern] of Object.entries(stylePatterns)) {
      let matchScore = 0;
      let matchedFeatures = [];

      // 关键词匹配
      for (const keyword of pattern.keywords) {
        const regex = new RegExp(keyword, 'gi');
        const matches = userMessages.match(regex);
        if (matches) {
          matchScore += matches.length;
          matchedFeatures.push(`使用"${keyword}"`);
        }
      }

      // 计算匹配度（基于文本长度标准化）
      const normalizedScore = matchScore / Math.max(1, userMessages.length / 100);

      analysisResults[styleType] = {
        score: normalizedScore,
        features: matchedFeatures,
        confidence: Math.min(0.9, normalizedScore * 0.2 + 0.3)
      };
    }

    // 找出最匹配的风格
    let bestMatch = {
      styleType: '年轻直接型',
      score: 0,
      confidence: 0.5
    };

    for (const [styleType, result] of Object.entries(analysisResults)) {
      if (result.score > bestMatch.score) {
        bestMatch = {
          styleType,
          score: result.score,
          confidence: result.confidence,
          features: result.features
        };
      }
    }

    // 额外的语言特征分析
    const additionalFeatures = analyzeAdditionalLanguageFeatures(userMessages);

    return {
      styleType: bestMatch.styleType,
      confidence: bestMatch.confidence,
      keyFeatures: [...(bestMatch.features || []), ...additionalFeatures],
      analysisDetails: analysisResults,
      fallback: false
    };
  } catch (error) {
    console.error('语言风格分析失败:', error);
    return {
      styleType: '年轻直接型',
      confidence: 0.3,
      keyFeatures: ['分析失败'],
      fallback: true,
      error: error.message
    };
  }
}

// 分析额外的语言特征
function analyzeAdditionalLanguageFeatures(userText) {
  const features = [];

  // 句子长度分析
  const sentences = userText.split(/[。！？.!?]/).filter(s => s.trim().length > 0);
  const avgSentenceLength = sentences.reduce((sum, s) => sum + s.length, 0) / sentences.length;

  if (avgSentenceLength > 20) {
    features.push('偏好长句表达');
  } else if (avgSentenceLength < 10) {
    features.push('偏好短句表达');
  }

  // 标点符号使用分析
  const exclamationCount = (userText.match(/[！!]/g) || []).length;
  const questionCount = (userText.match(/[？?]/g) || []).length;

  if (exclamationCount > 2) {
    features.push('情感表达强烈');
  }
  if (questionCount > 2) {
    features.push('善于提问思考');
  }

  // 语气词分析
  const modalWords = ['吧', '呢', '啊', '哦', '嗯', '呀'];
  const modalCount = modalWords.reduce((count, word) => {
    return count + (userText.match(new RegExp(word, 'g')) || []).length;
  }, 0);

  if (modalCount > 3) {
    features.push('语气温和亲切');
  }

  return features;
}

// 日记质量验证和优化
function validateAndOptimizeDiary(rawDiary, dialogueContent, userLevel, dailyFeeling) {
  console.log('开始验证和优化日记质量...');

  try {
    let diary = rawDiary.trim();
    let qualityScore = 0;
    const issues = [];
    const fixes = [];

    // 1. 基本格式检查
    if (!diary || diary.length < 50) {
      issues.push('内容过短');
      qualityScore -= 0.3;
    }

    if (diary.length > 600) {
      diary = diary.substring(0, 597) + '...';
      fixes.push('截取过长内容');
      qualityScore -= 0.1;
    }

    // 2. 第一人称检查
    const firstPersonScore = validateFirstPerson(diary);
    qualityScore += firstPersonScore.score;
    if (firstPersonScore.issues.length > 0) {
      issues.push(...firstPersonScore.issues);
    }

    // 3. 内容相关性检查
    const relevanceScore = validateContentRelevance(diary, dialogueContent);
    qualityScore += relevanceScore.score;
    if (relevanceScore.issues.length > 0) {
      issues.push(...relevanceScore.issues);
    }

    // 4. 情感一致性检查
    const emotionScore = validateEmotionConsistency(diary, dailyFeeling);
    qualityScore += emotionScore.score;
    if (emotionScore.issues.length > 0) {
      issues.push(...emotionScore.issues);
    }

    // 5. 结构完整性检查
    const structureScore = validateDiaryStructure(diary);
    qualityScore += structureScore.score;
    if (structureScore.issues.length > 0) {
      issues.push(...structureScore.issues);
    }

    // 标准化质量分数 (0-1)
    qualityScore = Math.max(0, Math.min(1, (qualityScore + 2) / 4));

    return {
      diary: diary,
      qualityScore: qualityScore,
      issues: issues,
      fixes: fixes,
      fallback: false
    };
  } catch (error) {
    console.error('日记质量验证失败:', error);
    return {
      diary: rawDiary.substring(0, 500),
      qualityScore: 0.3,
      issues: ['验证过程失败'],
      fixes: [],
      fallback: true,
      error: error.message
    };
  }
}

// 验证第一人称使用
function validateFirstPerson(diary) {
  const issues = [];
  let score = 0;

  // 检查是否使用第一人称
  const firstPersonCount = (diary.match(/我/g) || []).length;
  const secondPersonCount = (diary.match(/你/g) || []).length;
  const thirdPersonCount = (diary.match(/他|她/g) || []).length;

  if (firstPersonCount > 0) {
    score += 0.5;
  } else {
    issues.push('缺少第一人称表达');
    score -= 0.5;
  }

  if (secondPersonCount > 0) {
    issues.push('包含第二人称表达');
    score -= 0.3;
  }

  if (thirdPersonCount > 0) {
    issues.push('包含第三人称表达');
    score -= 0.2;
  }

  return { score, issues };
}

// 验证内容相关性
function validateContentRelevance(diary, dialogueContent) {
  const issues = [];
  let score = 0;

  try {
    // 提取对话中的关键词
    const dialogueText = dialogueContent
      .filter(msg => msg.role === 'user')
      .map(msg => msg.content)
      .join(' ');

    // 简单的关键词匹配检查
    const commonWords = ['微光', '对话', '聊', '谈', '说'];
    let matchCount = 0;

    for (const word of commonWords) {
      if (diary.includes(word) && dialogueText.includes(word)) {
        matchCount++;
      }
    }

    if (matchCount > 0) {
      score += 0.3;
    } else {
      issues.push('与对话内容关联度较低');
      score -= 0.2;
    }

    // 检查是否提到对话相关内容
    if (diary.includes('微光') || diary.includes('对话') || diary.includes('聊')) {
      score += 0.2;
    }

    return { score, issues };
  } catch (error) {
    console.error('内容相关性验证失败:', error);
    return { score: 0, issues: ['相关性验证失败'] };
  }
}

// 验证情感一致性
function validateEmotionConsistency(diary, dailyFeeling) {
  const issues = [];
  let score = 0;

  if (!dailyFeeling || dailyFeeling.trim().length === 0) {
    return { score: 0, issues: [] }; // 没有感受信息，跳过验证
  }

  try {
    // 简单的情感词匹配
    const positiveWords = ['开心', '高兴', '满足', '轻松', '释然', '温暖'];
    const negativeWords = ['难过', '沮丧', '焦虑', '担心', '疲惫', '困惑'];
    const neutralWords = ['平静', '思考', '理解', '认识'];

    const feelingLower = dailyFeeling.toLowerCase();
    let expectedTone = 'neutral';

    if (positiveWords.some(word => feelingLower.includes(word))) {
      expectedTone = 'positive';
    } else if (negativeWords.some(word => feelingLower.includes(word))) {
      expectedTone = 'negative';
    }

    // 检查日记中的情感表达是否一致
    const diaryPositive = positiveWords.some(word => diary.includes(word));
    const diaryNegative = negativeWords.some(word => diary.includes(word));

    if (expectedTone === 'positive' && diaryPositive) {
      score += 0.3;
    } else if (expectedTone === 'negative' && diaryNegative) {
      score += 0.3;
    } else if (expectedTone === 'neutral') {
      score += 0.2;
    } else {
      issues.push('情感表达与今日感受不一致');
      score -= 0.1;
    }

    return { score, issues };
  } catch (error) {
    console.error('情感一致性验证失败:', error);
    return { score: 0, issues: ['情感验证失败'] };
  }
}

// 验证日记结构
function validateDiaryStructure(diary) {
  const issues = [];
  let score = 0;

  try {
    // 检查是否有明显的段落结构
    const paragraphs = diary.split('\n').filter(p => p.trim().length > 0);

    if (paragraphs.length >= 2) {
      score += 0.2;
    } else {
      issues.push('缺少段落结构');
    }

    // 检查是否包含对未来的思考
    const futureWords = ['明天', '未来', '接下来', '以后', '继续'];
    if (futureWords.some(word => diary.includes(word))) {
      score += 0.2;
    } else {
      issues.push('缺少对未来的思考');
    }

    // 检查是否有完整的表达
    if (diary.length > 100 && diary.includes('。')) {
      score += 0.1;
    }

    return { score, issues };
  } catch (error) {
    console.error('结构验证失败:', error);
    return { score: 0, issues: ['结构验证失败'] };
  }
}

// 备用日记生成方案
function generateFallbackDiary(userLevel, dialogueTheme, dailyFeeling, errorReason = '未知错误') {
  console.log('使用备用日记方案，层级:', userLevel, '主题:', dialogueTheme, '感受:', dailyFeeling, '错误原因:', errorReason);

  try {
    // 使用AI提示配置中的智能备用选择函数
    const fallbackResult = aiPrompts.diaryGeneration.selectFallbackDiary(
      userLevel,
      dialogueTheme,
      dailyFeeling,
      errorReason
    );

    console.log('智能选择备用日记结果:', fallbackResult);

    return {
      success: true,
      diaryContent: fallbackResult.diary,
      wordCount: fallbackResult.wordCount,
      qualityScore: 0.7, // 备用日记的固定质量分数
      fallback: true,
      fallbackSource: fallbackResult.source,
      errorReason: errorReason,
      generationTime: 0
    };
  } catch (selectionError) {
    console.error('智能选择备用日记失败:', selectionError);

    // 最终紧急兜底
    const emergencyDiary = "今天和微光的对话很有意义，让我对自己有了新的认识。虽然还有很多问题没有答案，但我开始相信，答案会在我继续前行的路上慢慢显现。这种相信本身就是一种力量。明天，我要带着这份力量继续我的人生旅程。";

    return {
      success: true,
      diaryContent: emergencyDiary,
      wordCount: emergencyDiary.length,
      qualityScore: 0.5,
      fallback: true,
      fallbackSource: 'emergency',
      errorReason: errorReason,
      generationTime: 0,
      error: selectionError.message
    };
  }
}

// 记录日记生成性能指标的辅助函数
async function recordDiaryGenerationMetrics(openid, userLevel, performanceMetrics, diaryContent, errorMessage = null) {
  if (!openid) return; // 如果没有openid，跳过记录

  try {
    await cloud.callFunction({
      name: 'tieringAnalytics',
      data: {
        type: 'recordDiaryGenerationMetrics',
        data: {
          openid,
          userLevel,
          totalTime: performanceMetrics.totalTime,
          styleAnalysisTime: performanceMetrics.styleAnalysisTime,
          aiGenerationTime: performanceMetrics.aiGenerationTime,
          qualityValidationTime: performanceMetrics.qualityValidationTime,
          success: performanceMetrics.success,
          errorType: performanceMetrics.errorType,
          fallbackUsed: performanceMetrics.fallbackUsed,
          styleAnalysisSuccess: performanceMetrics.styleAnalysisSuccess,
          aiGenerationSuccess: performanceMetrics.aiGenerationSuccess,
          qualityValidationSuccess: performanceMetrics.qualityValidationSuccess,
          diaryLength: diaryContent?.length || 0,
          errorMessage: errorMessage,
          timestamp: new Date()
        }
      }
    });
    console.log('日记生成指标记录成功');
  } catch (error) {
    console.error('记录日记生成指标失败:', error);
    // 不影响主流程，只记录错误
  }
}

// 模拟 AI 响应 - 基于历史记录生成更相关的回复，遵循简洁直接原则
function mockAIResponse(userQuestion, dialogueContext, aiRoleInstruction) {
  console.log('使用模拟响应，检查是否包含历史记录信息')

  // 检查角色指令中是否包含历史记录信息
  if (aiRoleInstruction && aiRoleInstruction.includes('历史对话记录')) {
    console.log('检测到历史记录信息，尝试生成相关回复')

    // 尝试从角色指令中提取历史信息
    const historyMatch = aiRoleInstruction.match(/用户：([^内心]+)/g)
    if (historyMatch && historyMatch.length > 0) {
      const userMessage = historyMatch[0].replace('用户：', '').trim()
      console.log('提取到的用户历史消息:', userMessage)

      // 基于历史消息生成简洁直接的相关回复
      const contextualResponses = [
        `那个"${userMessage.substring(0, 15)}"，现在还困扰着吗？`,
        `上次的感受，这次有什么不同？`,
        `那时候的想法，现在还是这样吗？`,
        `还是那个问题在心里打转？`
      ]
      return contextualResponses[Math.floor(Math.random() * contextualResponses.length)]
    }
  }

  // 检查是否是开场问题
  if (userQuestion.includes('开场问题') || userQuestion.includes('开始关于')) {
    const openingResponses = [
      '我是你心中的微光，最近心里在想什么？',
      '我是你心中的微光，这个话题从哪里开始？',
      '我是你心中的微光，现在最困扰的是什么？',
      '我是你心中的微光，内心深处在纠结什么？'
    ]
    return openingResponses[Math.floor(Math.random() * openingResponses.length)]
  }

  // 如果没有历史记录，使用简洁直接的通用回复
  const mockResponses = [
    '说到底，你在怕什么？',
    '问题的核心是什么？',
    '真正想要的是什么？',
    '内心的声音在说什么？',
    '这背后的原因是什么？'
  ]
  return mockResponses[Math.floor(Math.random() * mockResponses.length)]
}

// 层级特定的历史记录权重配置
const TIER_HISTORY_WEIGHTS = {
  1: { // 关闭层 - 需要极致温柔
    maxRecords: 2, // 最少历史记录，避免压力
    focusOnEmotions: true, // 重点关注情感
    includeDialogue: false, // 不包含具体对话内容
    weightRecent: 0.8 // 更重视最近的记录
  },
  2: { // 徘徊层 - 需要鼓励
    maxRecords: 3,
    focusOnEmotions: true,
    includeDialogue: true,
    weightRecent: 0.7,
    highlightPositive: true // 突出积极内容
  },
  3: { // 挣扎层 - 需要行动指导
    maxRecords: 4,
    focusOnEmotions: true,
    includeDialogue: true,
    weightRecent: 0.6,
    emphasizeGrowth: true // 强调成长轨迹
  },
  4: { // 主人翁层 - 需要深度探索
    maxRecords: 5,
    focusOnEmotions: false,
    includeDialogue: true,
    weightRecent: 0.5,
    deepAnalysis: true // 深度分析模式
  },
  5: { // 创造者层 - 需要思想碰撞
    maxRecords: 5,
    focusOnEmotions: false,
    includeDialogue: true,
    weightRecent: 0.4,
    intellectualFocus: true // 智力挑战导向
  }
};

// 构建层级感知的用户历史上下文
function buildTierAwareHistoryContext(history, userLevel = 3, isOpeningQuestion = false) {
  if (!history || history.length === 0) return ''

  const tierConfig = TIER_HISTORY_WEIGHTS[userLevel] || TIER_HISTORY_WEIGHTS[3];
  const maxRecords = Math.min(tierConfig.maxRecords, history.length);

  // 根据层级权重选择历史记录
  const selectedHistory = selectHistoryByTier(history, tierConfig, maxRecords);
  console.log('selectedHistory', selectedHistory)
  let context = '\n\n【用户历史对话记录】\n'

  // 根据层级调整上下文描述
  switch (userLevel) {
    case 1:
      context += '基于你最近的内心状态，我想温柔地陪伴你：\n\n'
      break;
    case 2:
      context += '从你过往的表达中，我看到了你内心的光芒：\n\n'
      break;
    case 3:
      context += '回顾你的成长轨迹，我看到了你内在的力量：\n\n'
      break;
    case 4:
      context += '基于你的深度思考历程，让我们继续探索：\n\n'
      break;
    case 5:
      context += '从你的思想轨迹中，我看到了创造的火花：\n\n'
      break;
    default:
      context += '基于以下用户过往的对话记录，你可以更好地了解用户的内心世界：\n\n'
  }

  selectedHistory.forEach((record, index) => {
    context += buildTierSpecificRecordContext(record, index + 1, tierConfig, userLevel);
  })

  // 根据层级和场景添加特定指导
  if (isOpeningQuestion) {
    context += buildTierSpecificOpeningGuidance(userLevel);
  } else {
    context += buildTierSpecificDialogueGuidance(userLevel);
  }

  return context;
}

// 根据层级权重选择历史记录
function selectHistoryByTier(history, tierConfig, maxRecords) {
  // 按时间排序，最新的在前
  const sortedHistory = [...history].sort((a, b) => new Date(b.createTime) - new Date(a.createTime));

  // 根据权重选择记录
  const recentWeight = tierConfig.weightRecent;
  const recentCount = Math.ceil(maxRecords * recentWeight);
  const olderCount = maxRecords - recentCount;

  const recentRecords = sortedHistory.slice(0, recentCount);
  const olderRecords = sortedHistory.slice(recentCount, recentCount + olderCount);

  return [...recentRecords, ...olderRecords];
}

// 构建层级特定的记录上下文
function buildTierSpecificRecordContext(record, index, tierConfig, userLevel) {
  let context = `${index}. 对话主题：${record.theme}\n`;

  // 根据层级配置决定包含的内容
  if (record.summary && (tierConfig.focusOnEmotions || userLevel >= 3)) {
    if (userLevel === 1) {
      context += `   内心感受：${record.summary}\n`;
    } else if (userLevel === 2 && tierConfig.highlightPositive) {
      // 为徘徊层突出积极内容
      const positiveKeywords = ['希望', '想要', '努力', '尝试', '改变', '成长'];
      const hasPositive = positiveKeywords.some(keyword => record.summary.includes(keyword));
      if (hasPositive) {
        context += `   内心光芒：${record.summary}\n`;
      } else {
        context += `   内心感悟：${record.summary}\n`;
      }
    } else {
      context += `   内心感悟：${record.summary}\n`;
    }
  }

  // 情感关键词处理
  if (record.emotionKeyword && tierConfig.focusOnEmotions) {
    context += `   情感关键词：${record.emotionKeyword}\n`;
  }

  // 三问仪式处理 - 根据层级调整深度
  if (record.questions && record.answers && record.questions.length > 0 && record.answers.length > 0) {
    if (userLevel >= 4 && tierConfig.deepAnalysis) {
      context += `   深度思考：\n`;
      for (let i = 0; i < Math.min(record.questions.length, record.answers.length); i++) {
        context += `     问：${record.questions[i]}\n`;
        context += `     答：${record.answers[i]}\n`;
      }
    } else if (userLevel === 3 && tierConfig.emphasizeGrowth) {
      // 为挣扎层强调成长相关的问答
      context += `   成长思考：\n`;
      for (let i = 0; i < Math.min(2, record.questions.length, record.answers.length); i++) {
        context += `     ${record.questions[i]} → ${record.answers[i]}\n`;
      }
    }
  }
  // 历史对话内容处理
  // if (record.dialogueContent && record.dialogueContent.length > 0) {
  //   const dialogueMessages = parseDialogueContent(record.dialogueContent);
  //   const messageLimit = getDialogueMessageLimit(userLevel);

  //   if (dialogueMessages.length > 0) {
  //     context += `   历史对话内容：\n`;
  //     const recentMessages = dialogueMessages.slice(-messageLimit);

  //     recentMessages.forEach((msg) => {
  //       if (msg.role === 'user') {
  //         context += `     用户：${msg.content}\n`;
  //       } else if (msg.role === 'ai' || msg.role === 'assistant') {
  //         context += `     内心：${msg.content}\n`;
  //       }
  //     });
  //   }
  // }

  // 其他字段根据层级选择性包含
  if (record.userConcerns && record.userConcerns.length > 0 && userLevel >= 4) {
    context += `   关注焦点：${record.userConcerns.join('; ')}\n`;
  }

  if (record.emotionalState && record.emotionalState.length > 0 && tierConfig.focusOnEmotions) {
    context += `   情感状态：${record.emotionalState.join(', ')}\n`;
  }

  if (record.tomorrowMessage && userLevel >= 3) {
    context += `   给未来的话：${record.tomorrowMessage}\n`;
  }

  context += `   记录时间：${new Date(record.createTime).toLocaleDateString()}\n\n`;

  return context;
}

// 解析对话内容的辅助函数
function parseDialogueContent(dialogueContent) {
  let dialogueMessages = [];

  if (typeof dialogueContent === 'string') {
    try {
      dialogueMessages = JSON.parse(dialogueContent);
    } catch (e) {
      console.log('解析历史对话内容失败:', e);
      dialogueMessages = [];
    }
  } else if (Array.isArray(dialogueContent)) {
    dialogueMessages = dialogueContent;
  }

  return dialogueMessages;
}

// 根据层级获取对话消息限制
function getDialogueMessageLimit(userLevel) {
  const limits = {
    1: 200, // 关闭层：最少对话内容
    2: 200, // 徘徊层：适中对话内容
    3: 200, // 挣扎层：较多对话内容
    4: 200, // 主人翁层：更多对话内容
    5: 200  // 创造者层：最多对话内容
  };
  return limits[userLevel] || 4;
}

// 构建层级特定的开场指导
function buildTierSpecificOpeningGuidance(userLevel) {
  let guidance = '\n【开场问题的层级特定要求】\n';

  switch (userLevel) {
    case 1: // 关闭层
      guidance += '1. 用最温柔的语气，避免任何可能的压力\n';
      guidance += '2. 重点关注情感支持，不要急于解决问题\n';
      guidance += '3. 基于最近的情感状态，表达无条件的理解\n';
      guidance += '4. 控制在30个字以内，语气要极其温和\n';
      break;
    case 2: // 徘徊层
      guidance += '1. 从历史中找到积极的火花，给予鼓励\n';
      guidance += '2. 温和地询问，避免给用户压力\n';
      guidance += '3. 重点关注用户想要改变的愿望\n';
      guidance += '4. 控制在40个字以内，语气要鼓励而温暖\n';
      break;
    case 3: // 挣扎层
      guidance += '1. 基于历史成长轨迹，提出行动导向的问题\n';
      guidance += '2. 将痛苦转化为行动的动力\n';
      guidance += '3. 提供具体的第一步思考方向\n';
      guidance += '4. 控制在50个字以内，语气要坚定有力\n';
      break;
    case 4: // 主人翁层
      guidance += '1. 基于深度思考历程，提出苏格拉底式问题\n';
      guidance += '2. 引导用户进行更深层的自我探索\n';
      guidance += '3. 关联历史中的深度思考内容\n';
      guidance += '4. 控制在50个字以内，语气要平等而深刻\n';
      break;
    case 5: // 创造者层
      guidance += '1. 基于思想轨迹，提出智力挑战性问题\n';
      guidance += '2. 激发创造性思维和思想碰撞\n';
      guidance += '3. 关联历史中的创新想法或深刻洞察\n';
      guidance += '4. 控制在50个字以内，语气要启发而智慧\n';
      break;
  }

  guidance += '\n【重要】必须基于历史记录中的具体内容，体现层级特定的关怀方式！\n';
  return guidance;
}

// 构建层级特定的对话指导
function buildTierSpecificDialogueGuidance(userLevel) {
  let guidance = '\n【对话过程的层级特定要求】\n';

  switch (userLevel) {
    case 1: // 关闭层
      guidance += '1. 提供无条件的情感支持和理解\n';
      guidance += '2. 避免给出任何建议或解决方案\n';
      guidance += '3. 重点在于陪伴和倾听\n';
      guidance += '4. 语气要极其温柔，避免任何评判\n';
      break;
    case 2: // 徘徊层
      guidance += '1. 发现并放大用户内心的积极火花\n';
      guidance += '2. 温和地鼓励，给予希望和信心\n';
      guidance += '3. 关注用户的小小进步和努力\n';
      guidance += '4. 语气要温暖鼓励，充满希望\n';
      break;
    case 3: // 挣扎层
      guidance += '1. 将痛苦转化为行动的能量和动力\n';
      guidance += '2. 提供具体可行的第一步指导\n';
      guidance += '3. 强调用户内在的力量和潜能\n';
      guidance += '4. 语气要坚定有力，赋予能量\n';
      break;
    case 4: // 主人翁层
      guidance += '1. 通过苏格拉底式提问引导深度自我探索\n';
      guidance += '2. 帮助用户发现自己内在的智慧\n';
      guidance += '3. 平等对话，尊重用户的主体性\n';
      guidance += '4. 语气要平等深刻，启发思考\n';
      break;
    case 5: // 创造者层
      guidance += '1. 提供智力刺激和创造性协作\n';
      guidance += '2. 进行思想实验和深度思辨\n';
      guidance += '3. 激发创新思维和突破性洞察\n';
      guidance += '4. 语气要智慧启发，充满思辨性\n';
      break;
  }

  guidance += '\n【重要】结合历史记录，体现对用户成长轨迹的深度理解！\n';
  return guidance;
}

// 兼容性函数：保持原有接口
function buildHistoryContext(history) {
  return buildTierAwareHistoryContext(history, 3, true);
}

// 提示内容长度限制配置
const PROMPT_LENGTH_LIMITS = {
  maxSystemPrompt: 80000, // 系统提示最大字符数
  maxHistoryContext: 40000, // 历史上下文最大字符数
  maxDialogueContext: 20000, // 对话上下文最大字符数
  warningThreshold: 0.8 // 警告阈值（80%时开始优化）
};

// 层级切换时的上下文平滑过渡配置
const TIER_TRANSITION_CONFIG = {
  transitionKeywords: {
    1: ['温柔', '理解', '陪伴', '倾听'],
    2: ['鼓励', '希望', '光芒', '努力'],
    3: ['力量', '行动', '改变', '成长'],
    4: ['探索', '思考', '发现', '智慧'],
    5: ['创造', '思辨', '洞察', '突破']
  },
  transitionPhrases: {
    1: '我想更温柔地陪伴你',
    2: '让我们一起寻找内心的光芒',
    3: '是时候将这份感受转化为力量了',
    4: '让我们深入探索这个问题',
    5: '这里有个有趣的思想实验'
  }
};

// 构建层级感知的角色指令
function buildTierAwareRoleInstruction(baseInstruction, dialogueContext, historyContext, userLevel, levelPrompt = null, previousLevel = null) {
  console.log('构建层级感知角色指令 - 当前层级:', userLevel, '之前层级:', previousLevel);

  let instruction = '';

  // 如果有层级提示，优先使用层级提示
  if (levelPrompt && levelPrompt.promptContent) {
    console.log('使用层级特定提示:', levelPrompt.levelName);
    instruction = levelPrompt.promptContent;

    // 如果发生了层级切换，添加平滑过渡指导
    if (previousLevel && previousLevel !== userLevel) {
      const transitionGuidance = buildTierTransitionGuidance(previousLevel, userLevel);
      instruction += `\n\n${transitionGuidance}`;
    }
  } else {
    // 回退到原有的提示系统
    instruction = buildFallbackRoleInstruction(baseInstruction, userLevel);
  }

  // 添加历史上下文（如果有）
  if (historyContext) {
    const optimizedHistoryContext = optimizePromptLength(historyContext, PROMPT_LENGTH_LIMITS.maxHistoryContext);
    instruction += `\n\n${optimizedHistoryContext}`;
  }

  // 添加当前对话上下文分析（如果有）
  if (dialogueContext && dialogueContext.length > 0) {
    const contextAnalysis = analyzeTierAwareDialogueContext(dialogueContext, userLevel);
    if (contextAnalysis) {
      const optimizedContextAnalysis = optimizePromptLength(contextAnalysis, PROMPT_LENGTH_LIMITS.maxDialogueContext);
      instruction += `\n\n${optimizedContextAnalysis}`;
    }
  }

  // 最终长度优化
  const finalInstruction = optimizePromptLength(instruction, PROMPT_LENGTH_LIMITS.maxSystemPrompt);

  console.log('最终指令长度:', finalInstruction.length, '字符', finalInstruction);
  if (finalInstruction.length > PROMPT_LENGTH_LIMITS.maxSystemPrompt * PROMPT_LENGTH_LIMITS.warningThreshold) {
    console.warn('提示长度接近限制，已进行优化');
  }
  return finalInstruction;
}

// 构建层级切换的平滑过渡指导
function buildTierTransitionGuidance(fromLevel, toLevel) {
  const fromKeywords = TIER_TRANSITION_CONFIG.transitionKeywords[fromLevel] || [];
  const toKeywords = TIER_TRANSITION_CONFIG.transitionKeywords[toLevel] || [];
  const transitionPhrase = TIER_TRANSITION_CONFIG.transitionPhrases[toLevel] || '';

  let guidance = '\n【层级切换平滑过渡指导】\n';
  guidance += `用户从 Level ${fromLevel} 切换到 Level ${toLevel}，需要平滑过渡：\n`;
  guidance += `1. 承认之前的状态：理解用户之前的${fromKeywords.join('、')}需求\n`;
  guidance += `2. 自然过渡：${transitionPhrase}\n`;
  guidance += `3. 新层级适应：现在更多关注${toKeywords.join('、')}\n`;
  guidance += `4. 保持连续性：不要突然改变语气，要渐进式调整\n`;
  guidance += `5. 情感桥接：用温暖的方式解释这种变化是成长的体现\n`;

  return guidance;
}

// 层级感知的对话上下文分析
function analyzeTierAwareDialogueContext(dialogueContext, userLevel) {
  return '';
  if (!dialogueContext || dialogueContext.length === 0) return '';

  let contextInfo = '\n【层级感知的对话上下文分析】\n';

  // 根据层级调整分析重点
  const analysisConfig = getTierAnalysisConfig(userLevel);

  // 获取最近的用户消息
  const recentUserMessages = dialogueContext
    .filter(msg => msg.role === 'user')
    .slice(-analysisConfig.userMessageCount);

  // 获取最近的AI回应
  const recentAIMessages = dialogueContext
    .filter(msg => msg.role === 'ai')
    .slice(-analysisConfig.aiMessageCount);

  if (recentUserMessages.length > 0) {
    contextInfo += `用户最近的表达（${analysisConfig.focusArea}）：\n`;
    recentUserMessages.forEach((msg) => {
      // 根据层级提取关键信息
      const keyInfo = extractTierSpecificInfo(msg.content, userLevel);
      contextInfo += `"${keyInfo}"\n`;
    });
  }

  if (recentAIMessages.length > 0) {
    contextInfo += '\n之前的内心回应：\n';
    recentAIMessages.forEach((msg) => {
      const keyInfo = extractTierSpecificInfo(msg.content, userLevel);
      contextInfo += `"${keyInfo}"\n`;
    });
  }

  // 添加层级特定的指导
  contextInfo += `\n【Level ${userLevel} 特定指导】\n`;
  contextInfo += getTierSpecificContextGuidance(userLevel);

  return contextInfo;
}

// 获取层级分析配置
function getTierAnalysisConfig(userLevel) {
  const configs = {
    1: { // 关闭层
      userMessageCount: 2,
      aiMessageCount: 1,
      focusArea: '情感状态'
    },
    2: { // 徘徊层
      userMessageCount: 3,
      aiMessageCount: 2,
      focusArea: '积极信号'
    },
    3: { // 挣扎层
      userMessageCount: 3,
      aiMessageCount: 2,
      focusArea: '行动意愿'
    },
    4: { // 主人翁层
      userMessageCount: 4,
      aiMessageCount: 2,
      focusArea: '深度思考'
    },
    5: { // 创造者层
      userMessageCount: 4,
      aiMessageCount: 3,
      focusArea: '创新想法'
    }
  };

  return configs[userLevel] || configs[3];
}

// 提取层级特定信息
function extractTierSpecificInfo(content, userLevel) {
  // 根据层级关注不同的关键词和模式
  const tierKeywords = {
    1: ['感受', '情绪', '痛苦', '难过', '累', '烦'],
    2: ['想要', '希望', '尝试', '努力', '改变', '好一点'],
    3: ['行动', '做', '开始', '改变', '突破', '力量'],
    4: ['思考', '为什么', '如何', '探索', '理解', '发现'],
    5: ['创造', '创新', '想法', '可能', '实验', '突破']
  };

  const keywords = tierKeywords[userLevel] || [];

  // 如果内容包含层级关键词，优先提取相关部分
  for (const keyword of keywords) {
    if (content.includes(keyword)) {
      // 提取包含关键词的句子或短语
      const sentences = content.split(/[。！？.!?]/);
      for (const sentence of sentences) {
        if (sentence.includes(keyword)) {
          return sentence.trim().substring(0, 100); // 限制长度
        }
      }
    }
  }

  // 如果没有找到特定关键词，返回前100个字符
  return content.substring(0, 100);
}

// 获取层级特定的上下文指导
function getTierSpecificContextGuidance(userLevel) {
  const guidances = {
    1: '- 重点关注情感支持，避免给出建议\n- 用最温柔的语气回应\n- 体现无条件的理解和陪伴',
    2: '- 寻找并放大积极信号\n- 给予温暖的鼓励\n- 帮助用户看到内心的光芒',
    3: '- 将情感转化为行动动力\n- 提供具体可行的第一步\n- 强调用户的内在力量',
    4: '- 通过提问引导深度思考\n- 帮助用户自己发现答案\n- 平等对话，尊重主体性',
    5: '- 提供智力刺激和挑战\n- 进行思想碰撞和实验\n- 激发创新思维和洞察'
  };

  return guidances[userLevel] || guidances[3];
}

// 优化提示内容长度 - 禁用截断以保持完整消息
function optimizePromptLength(content, maxLength) {
  // 直接返回原始内容，不进行截断
  // 这确保了所有消息内容都能完整传递给AI模型
  return content;
}

// 回退的角色指令构建（当没有层级提示时）
function buildFallbackRoleInstruction(baseInstruction, userLevel) {
  const config = aiPrompts.coreRole;

  let coreRole = `${config.identity}\n\n`;

  coreRole += `【微光的核心哲学】\n`;
  config.corePhilosophy.forEach(philosophy => {
    coreRole += `- ${philosophy}\n`;
  });

  coreRole += `\n【核心要求】\n`;
  config.coreRequirements.forEach(req => {
    coreRole += `- ${req}\n`;
  });

  coreRole += `\n【回应风格】\n`;
  config.responseStyle.forEach(style => {
    coreRole += `- ${style}\n`;
  });

  coreRole += `\n【严格禁止】\n`;
  config.strictlyProhibited.forEach(prohibited => {
    coreRole += `- ${prohibited}\n`;
  });

  coreRole += `\n【回应原则】\n`;
  config.responsePrinciples.forEach(principle => {
    coreRole += `- ${principle}\n`;
  });

  // 添加层级特定的补充指导
  if (userLevel) {
    coreRole += `\n【当前用户层级：Level ${userLevel}】\n`;
    coreRole += getTierSpecificContextGuidance(userLevel);
  }
  console.log('coreRole', coreRole)
  return coreRole;
}

// 兼容性函数：保持原有接口
function buildContextualRoleInstruction(baseInstruction, dialogueContext, levelPrompt = null) {
  return buildTierAwareRoleInstruction(baseInstruction, dialogueContext, null, 3, levelPrompt);
}

// 分析当前对话上下文，提取关键信息
function analyzeDialogueContext(dialogueContext) {
  if (!dialogueContext || dialogueContext.length === 0) return ''

  let contextInfo = '\n【当前对话上下文分析】\n'

  // 获取最近的用户消息，了解当前关注点
  const recentUserMessages = dialogueContext
    .filter(msg => msg.role === 'user')
    .slice(-3) // 最近3条用户消息

  // 获取最近的AI回应，了解对话走向
  const recentAIMessages = dialogueContext
    .filter(msg => msg.role === 'ai')
    .slice(-2) // 最近2条AI消息

  if (recentUserMessages.length > 0) {
    contextInfo += '用户最近的表达：\n'
    recentUserMessages.forEach((msg) => {
      contextInfo += `"${msg.content}"\n`
    })
  }

  if (recentAIMessages.length > 0) {
    contextInfo += '\n之前的内心回应：\n'
    recentAIMessages.forEach((msg) => {
      contextInfo += `"${msg.content}"\n`
    })
  }

  if (recentUserMessages.length > 0 || recentAIMessages.length > 0) {
    contextInfo += '\n【重要指示】\n'
    contextInfo += '- 必须基于以上对话内容，体现出对用户当前状态的深度理解\n'
    contextInfo += '- 在回应中自然地关联到用户刚才提到的内容或情感\n'
    contextInfo += '- 用内心独白的方式，避免重复之前已经说过的话\n'
    contextInfo += '- 展现出对话的连续性和情感的延续性\n'
    contextInfo += '- 如果是开场问题，要基于用户刚才的表达提出相关的深入问题\n'
  }

  return contextInfo
}

// 构建简化的历史上下文（用于开场问题）
function buildSimpleHistoryContext(history) {
  if (!history || history.length === 0) return ''

  let context = '\n【用户历史对话记录】\n'
  context += '基于用户最近的内心对话内容：\n\n'

  history.slice(0, 3).forEach((record, index) => {
    context += `=== 历史对话 ${index + 1} ===\n`
    context += `主题：${record.theme}\n`

    // 解析并提取 dialogueContent 中的关键对话
    if (record.dialogueContent && record.dialogueContent.length > 0) {
      context += '对话内容：\n'

      // 解析对话内容，确保正确处理数据结构
      let dialogueMessages = []

      // 检查 dialogueContent 是否是字符串（需要解析）还是已经是数组
      if (typeof record.dialogueContent === 'string') {
        try {
          dialogueMessages = JSON.parse(record.dialogueContent)
        } catch (e) {
          console.log('解析简化历史对话内容失败:', e)
          dialogueMessages = []
        }
      } else if (Array.isArray(record.dialogueContent)) {
        dialogueMessages = record.dialogueContent
      }

      // 提取用户的关键表达（最多2条）
      const userMessages = dialogueMessages
        .filter(msg => msg.role === 'user')
        .slice(0, 2)

      userMessages.forEach((msg) => {
        context += `用户：${msg.content}\n`
      })

      // 提取AI的关键回应（最多1条）
      const aiMessages = dialogueMessages
        .filter(msg => msg.role === 'ai' || msg.role === 'assistant')
        .slice(0, 1)

      aiMessages.forEach((msg) => {
        context += `内心：${msg.content}\n`
      })
    }

    context += `时间：${new Date(record.createTime).toLocaleDateString()}\n\n`
  })

  context += '请基于这些历史对话内容，提出一个与用户过往表达相关联的开场问题。\n'
  return context
}

// 构建基于历史记录的角色指令（用于开场问题）
function buildHistoryBasedRoleInstruction(baseInstruction, historyContext, levelPrompt = null) {
  // 如果有层级提示，优先使用层级提示
  if (levelPrompt && levelPrompt.promptContent) {
    console.log('开场问题使用层级特定提示:', levelPrompt.levelName);
    let instruction = levelPrompt.promptContent;

    // 添加历史上下文信息
    if (historyContext) {
      instruction += `\n\n${historyContext}`;
    }

    return instruction;
  }

  // 回退到原有的开场问题提示系统
  const config = aiPrompts.openingQuestion

  let coreRole = `${aiPrompts.coreRole.identity}\n\n`

  coreRole += `【开场问题的核心要求】\n`
  config.coreRequirements.forEach(req => {
    coreRole += `- ${req}\n`
  })

  coreRole += `\n【开场问题风格】\n`
  config.style.forEach(style => {
    coreRole += `- ${style}\n`
  })

  coreRole += `\n【严格禁止】\n`
  config.strictlyProhibited.forEach(prohibited => {
    coreRole += `- ${prohibited}\n`
  })

  coreRole += `\n【开场原则】\n`
  config.principles.forEach(principle => {
    coreRole += `- ${principle}\n`
  })

  coreRole += `\n【重要】${config.important}`

  return historyContext ? `${coreRole}\n${historyContext}` : coreRole
}

// 构建包含历史记录和当前对话上下文的角色指令（用于对话过程中）
function buildContextualRoleInstructionWithHistory(baseInstruction, dialogueContext, historyContext, levelPrompt = null) {
  // 如果有层级提示，优先使用层级提示
  if (levelPrompt && levelPrompt.promptContent) {
    console.log('对话过程使用层级特定提示:', levelPrompt.levelName);
    let instruction = levelPrompt.promptContent;

    // 添加历史背景信息
    if (historyContext) {
      instruction += `\n\n${historyContext}`;
    }

    // 添加当前对话上下文分析
    if (dialogueContext && dialogueContext.length > 0) {
      const contextAnalysis = analyzeDialogueContext(dialogueContext);
      if (contextAnalysis) {
        instruction += `\n\n${contextAnalysis}`;
      }
    }

    return instruction;
  }

  // 回退到原有的对话过程提示系统
  const config = aiPrompts.dialogueProcess

  let coreRole = `${aiPrompts.coreRole.identity}\n\n`

  coreRole += `【核心要求】\n`
  config.coreRequirements.forEach(req => {
    coreRole += `- ${req}\n`
  })

  coreRole += `\n【回应风格】\n`
  config.style.forEach(style => {
    coreRole += `- ${style}\n`
  })

  coreRole += `\n【严格禁止】\n`
  config.strictlyProhibited.forEach(prohibited => {
    coreRole += `- ${prohibited}\n`
  })

  coreRole += `\n【回应原则】\n`
  config.principles.forEach(principle => {
    coreRole += `- ${principle}\n`
  })

  let fullInstruction = coreRole

  // 添加历史背景信息
  if (historyContext) {
    fullInstruction += `\n\n${historyContext}`
  }

  // 添加当前对话上下文分析
  if (dialogueContext && dialogueContext.length > 0) {
    const contextAnalysis = analyzeDialogueContext(dialogueContext)
    if (contextAnalysis) {
      fullInstruction += `\n\n${contextAnalysis}`
    }
  }

  return fullInstruction
}

exports.main = async (event, context) => {
  const startTime = Date.now(); // 记录开始时间用于性能监控

  const {
    userQuestion,
    aiRoleInstruction = '你是用户内心深处的声音，是他们最了解自己的那个内在伙伴。',
    dialogueContext = [],
    openid,
    includeHistory = false,
    userLevel, // 新增：用户层级参数
    type, // 新增：请求类型参数
    dialogueSummary // 新增：对话总结参数（用于结束语生成）
  } = event

  // 处理AI日记生成请求
  if (type === 'generateDiary') {
    console.log('处理AI日记生成请求');

    const { dailyFeeling, dialogueTheme } = event;

    if (!dialogueContext || dialogueContext.length === 0) {
      return {
        success: false,
        error: '生成日记需要对话内容'
      };
    }

    try {
      const diaryResult = await generateAIDiary(
        dialogueContext,
        userLevel || 3,
        dailyFeeling || '',
        dialogueTheme || '内心探索',
        openid
      );

      // 记录日记生成指标
      const responseTime = Date.now() - startTime;
      await recordDialogueMetrics(
        openid,
        userLevel || 3,
        responseTime,
        'AI日记生成',
        diaryResult.diaryContent,
        !diaryResult.success,
        diaryResult.success ? null : 'diary_generation_failed'
      );

      return diaryResult;
    } catch (error) {
      console.error('AI日记生成异常:', error);

      // 记录错误指标
      const responseTime = Date.now() - startTime;
      await recordDialogueMetrics(
        openid,
        userLevel || 3,
        responseTime,
        'AI日记生成',
        null,
        true,
        'diary_generation_error'
      );

      return {
        success: false,
        error: 'AI日记生成失败，请稍后重试'
      };
    }
  }

  // 处理结束语生成请求
  if (type === 'generateEndingMessage') {
    console.log('处理结束语生成请求');

    if (!dialogueContext || dialogueContext.length === 0) {
      return {
        success: false,
        error: '生成结束语需要对话内容'
      };
    }

    try {
      const endingResult = await generateEndingMessage(
        dialogueContext,
        dialogueSummary,
        openid,
        userLevel || 3
      );

      // 记录结束语生成指标
      const responseTime = Date.now() - startTime;
      await recordDialogueMetrics(
        openid,
        userLevel || 3,
        responseTime,
        '结束语生成',
        endingResult.endingMessage,
        !endingResult.success,
        endingResult.success ? null : 'ending_generation_failed'
      );

      return endingResult;
    } catch (error) {
      console.error('结束语生成异常:', error);

      // 记录错误指标
      const responseTime = Date.now() - startTime;
      await recordDialogueMetrics(
        openid,
        userLevel || 3,
        responseTime,
        '结束语生成',
        null,
        true,
        'ending_generation_error'
      );

      return {
        success: false,
        error: '结束语生成失败，请稍后重试'
      };
    }
  }

  // 处理普通对话请求
  if (!userQuestion) {
    return { success: false, error: '缺少用户问题' }
  }

  // 用户层级检查和获取
  let finalUserLevel = userLevel;
  let levelPrompt = null;

  // 如果没有提供userLevel但有openid，尝试从用户数据中获取层级
  if (!finalUserLevel && openid) {
    console.log('未提供用户层级，尝试从用户数据中获取...')
    try {
      const userLevelResult = await cloud.callFunction({
        name: 'user',
        data: { type: 'getLevel' }
      });

      if (userLevelResult.result.success) {
        finalUserLevel = userLevelResult.result.level;
        console.log('从用户数据获取到层级:', finalUserLevel);
      } else {
        console.log('获取用户层级失败，使用默认层级3');
        finalUserLevel = 3; // 默认层级
      }
    } catch (error) {
      console.error('获取用户层级异常:', error);
      finalUserLevel = 3; // 默认层级
    }
  }

  // 如果仍然没有层级，使用默认层级3
  if (!finalUserLevel) {
    finalUserLevel = 3;
    console.log('使用默认层级:', finalUserLevel);
  }

  // 获取层级特定的AI提示（先检查缓存）
  console.log('获取层级', finalUserLevel, '的AI提示...')

  // 首先尝试从缓存获取
  levelPrompt = getCachedPrompt(finalUserLevel);

  if (!levelPrompt) {
    // 缓存中没有，从数据库获取
    try {
      const promptResult = await cloud.callFunction({
        name: 'aiPromptManager',
        data: {
          type: 'getPrompt',
          level: finalUserLevel
        }
      });

      if (promptResult.result.success && promptResult.result.data) {
        levelPrompt = promptResult.result.data;
        // 将结果存入缓存
        setCachedPrompt(finalUserLevel, levelPrompt);
        console.log('成功获取层级提示:', levelPrompt.levelName, '版本:', levelPrompt.version);
      } else {
        console.log('获取层级提示失败，将使用默认提示:', promptResult.result.error || '未知错误');
      }
    } catch (error) {
      console.error('调用aiPromptManager失败:', error);
      console.log('将使用默认提示系统');
    }
  }

  console.log('dialogueContext', dialogueContext)
  // 调试日志：查看传入的对话上下文
  console.log('=== geminiProxy 调试信息 ===')
  console.log('用户问题:', userQuestion)
  console.log('对话上下文长度:', dialogueContext.length)
  console.log('是否包含历史:', includeHistory)
  console.log('用户openid:', openid)
  console.log('最终用户层级:', finalUserLevel)
  console.log('层级提示状态:', levelPrompt ? '已获取' : '未获取')

  // 详细打印对话上下文
  if (dialogueContext && dialogueContext.length > 0) {
    console.log('=== 对话上下文详细内容 ===')
    dialogueContext.forEach((msg, index) => {
      console.log(`消息 ${index + 1}:`)
      console.log(`  角色: ${msg.role}`)
      console.log(`  内容: ${msg.content}`)
      console.log(`  时间: ${msg.timestamp || '未知'}`)
      console.log('---')
    })

    // 分析最近的用户消息
    const recentUserMessages = dialogueContext.filter(msg => msg.role === 'user').slice(-3)
    console.log('=== 最近的用户消息 ===')
    recentUserMessages.forEach((msg, index) => {
      console.log(`用户消息 ${index + 1}: ${msg.content}`)
    })

    // 分析最近的AI消息
    const recentAIMessages = dialogueContext.filter(msg => msg.role === 'ai').slice(-2)
    console.log('=== 最近的AI消息 ===')
    recentAIMessages.forEach((msg, index) => {
      console.log(`AI消息 ${index + 1}: ${msg.content}`)
    })
  } else {
    console.log('对话上下文为空')
  }

  // 判断是否是开场问题（基于用户问题内容和空的对话上下文）
  const isOpeningQuestion = userQuestion.includes('开场问题') || userQuestion.includes('开始关于') || dialogueContext.length === 0

  let enhancedRoleInstruction

  // 如果需要历史记录且有openid，获取用户历史（无论是否为开场问题）
  if (includeHistory && openid) {
    console.log('获取用户历史记录作为对话背景...')
    try {
      const historyResult = await cloud.callFunction({
        name: 'getUserHistory',
        data: { openid, limit: 5 } // 限制为最近5条记录
      })

      // console.log('获取历史记录结果:', historyResult.result)

      if (historyResult.result.success && historyResult.result.history && historyResult.result.history.length > 0) {
        console.log('成功获取历史记录数量:', historyResult.result.history.length)

        // 详细打印历史记录内容以便调试
        historyResult.result.history.forEach((record, index) => {
          console.log(`历史记录 ${index + 1}:`)
          console.log(`  主题: ${record.theme}`)
          console.log(`  总结: ${record.summary?.substring(0, 50)}...`)
          console.log(`  对话内容长度: ${record.dialogueContent?.length || 0}`)
          console.log(`  创建时间: ${record.createTime}`)
        })

        // 使用层级感知的历史上下文构建函数
        const historyContext = buildTierAwareHistoryContext(historyResult.result.history, finalUserLevel, isOpeningQuestion)
        console.log('构建的层级感知历史上下文长度:', historyContext.length)
        // console.log('历史上下文预览:', historyContext.substring(0, 200) + '...')

        // 使用层级感知的角色指令构建
        enhancedRoleInstruction = buildTierAwareRoleInstruction(
          aiRoleInstruction,
          dialogueContext,
          historyContext,
          finalUserLevel,
          levelPrompt
        )
        console.log('使用层级感知的角色指令构建（包含历史记录）')
      } else {
        console.log('没有历史记录或获取失败，使用基础指令')
        console.log('historyResult.result.success:', historyResult.result.success)
        console.log('historyResult.result.history:', historyResult.result.history)
        enhancedRoleInstruction = buildTierAwareRoleInstruction(
          aiRoleInstruction,
          dialogueContext,
          null,
          finalUserLevel,
          levelPrompt
        )
      }
    } catch (error) {
      console.error('获取历史记录失败:', error)
      enhancedRoleInstruction = buildTierAwareRoleInstruction(
        aiRoleInstruction,
        dialogueContext,
        null,
        finalUserLevel,
        levelPrompt
      )
    }
  } else {
    // 不需要历史记录，使用当前对话上下文
    console.log('不需要历史记录，使用层级感知的对话上下文构建角色指令')
    enhancedRoleInstruction = buildTierAwareRoleInstruction(
      aiRoleInstruction,
      dialogueContext,
      null,
      finalUserLevel,
      levelPrompt
    )
  }

  console.log('增强后的角色指令长度:', enhancedRoleInstruction.length)
  console.log('使用的提示系统:', levelPrompt ? '层级提示系统' : '默认提示系统')
  if (levelPrompt) {
    console.log('层级提示详情:', {
      level: levelPrompt.level,
      levelName: levelPrompt.levelName,
      version: levelPrompt.version,
      contentLength: levelPrompt.promptContent?.length || 0
    });
  }
  // console.log('=== 调试信息结束 ===')



  // console.log('使用真实API调用，角色指令长度:', enhancedRoleInstruction.length)
  // console.log('API Key 状态:', OPENROUTER_API_KEY ? '已配置' : '未配置')
  // console.log('请求参数:', { userQuestion, openid, includeHistory })

  try {
    // 构建消息数组，包括对话上下文和当前问题
    // 构建初步的消息数组
    let messages = [
      { role: 'system', content: enhancedRoleInstruction },
      ...dialogueContext.map(msg => ({
        role: msg.role === 'user' ? 'user' : 'assistant',
        content: msg.content
      })),
      { role: 'user', content: userQuestion }
    ];

    // 移除重复的消息项（基于 role 和 content 全等去重）
    messages = messages.filter((msg, index, self) =>
      index === self.findIndex(m =>
        m.role === msg.role && m.content === msg.content
      )
    );
    // // 记录消息统计信息而不是完整内容，避免截断
    // console.log('API请求统计:', {
    //   model: MODEL_NAME,
    //   messagesCount: messages.length,
    //   totalLength: JSON.stringify(messages).length,
    //   temperature: 0.7,
    //   max_tokens: 1024
    // });

    // // 详细记录每条消息的长度统计
    // messages.forEach((msg, index) => {
    //   console.log(`消息${index + 1} [${msg.role}]: ${msg.content.length}字符`);
    // });

    const requestBody = {
      model: MODEL_NAME,
      messages: messages,
      temperature: 0.7,
      max_tokens: 1024
    };
    console.log('requestBody', requestBody)

    // 确保请求体完整性
    const requestBodyString = JSON.stringify(requestBody);
    console.log('请求体总长度:', requestBodyString.length, '字符');

    const response = await fetch(OPENROUTER_API_URL, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(requestBodyString, 'utf8').toString()
      },
      body: requestBodyString
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('OpenRouter API request failed:', response.status, errorText);
      return {
        success: false,
        error: `AI服务错误: ${response.status} - ${errorText}`,
        status: response.status
      };
    }

    const data = await response.json();

    // 检查是否有 choices 数组，并获取第一个消息的内容
    if (data.choices && data.choices.length > 0 && data.choices[0].message) {
      const aiResponse = data.choices[0].message.content;

      // 记录对话指标
      const responseTime = Date.now() - startTime;
      await recordDialogueMetrics(openid, finalUserLevel, responseTime, userQuestion, aiResponse, false);

      return {
        success: true,
        response: aiResponse,
        tokens: data.usage?.total_tokens || 0,
        appliedLevel: finalUserLevel, // 实际应用的层级
        promptVersion: levelPrompt ? levelPrompt.version : 'fallback', // 使用的提示版本
        levelName: levelPrompt ? levelPrompt.levelName : '默认提示'
      };
    } else {
      console.error('OpenRouter API response missing expected data:', data);

      // 记录错误指标
      const responseTime = Date.now() - startTime;
      await recordDialogueMetrics(openid, finalUserLevel, responseTime, userQuestion, null, true, 'invalid_response_format');

      return {
        success: false,
        error: 'AI服务返回数据格式不正确。'
      };
    }

  } catch (error) {
    console.error('调用 OpenRouter API 异常:', error);

    // 记录错误指标
    const responseTime = Date.now() - startTime;
    let errorType = 'unknown_error';

    if (error.response) {
      errorType = 'api_error';
    } else if (error.request) {
      errorType = 'timeout_error';
    }

    await recordDialogueMetrics(openid, finalUserLevel, responseTime, userQuestion, null, true, errorType);

    // 处理不同类型的错误
    if (error.response) {
      return {
        success: false,
        error: `API错误：${error.response.data?.error?.message || '未知错误'}`,
        status: error.response.status
      }
    } else if (error.request) {
      return {
        success: false,
        error: 'API请求超时，请检查网络连接'
      }
    } else {
      return {
        success: false,
        error: '未知错误，请稍后重试'
      }
    }
  }
}; 