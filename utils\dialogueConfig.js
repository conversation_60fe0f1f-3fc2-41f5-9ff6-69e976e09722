// 对话配置文件
const dialogueConfig = {
    maxDialogueRound: 5,
    pointsCost: 6,
    typingSpeed: 50,
    dialogueSettings: {
        enableTypingEffect: true,
        autoScrollThreshold: 2,
        summaryMaxLength: 100
    },
    endingMessage: {
        enabled: true,
        maxGenerationTime: 5000,
        fallbackEnabled: true,
        emotionAnalysisTimeout: 2000,
        aiGenerationTimeout: 3000,
        wordCountMin: 50,
        wordCountMax: 70
    },
    emotionKeywords: [
        "迷茫",
        "坚定",
        "疲惫",
        "焦虑",
        "释然",
        "困惑",
        "清晰",
        "纠结",
        "平静",
        "激动"
    ],
    messages: {
        pointsInsufficient: "光点不足，是否前往光点铺子充值？",
        dialogueFinishing: "对话即将结束，正在生成总结",
        analysisComplete: "分析完成，开始对话",
        endingGeneration: "正在生成结束语...",
        endingGenerationFailed: "结束语生成失败，使用默认内容"
    }
}

module.exports = dialogueConfig