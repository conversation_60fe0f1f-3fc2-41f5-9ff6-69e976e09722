/* 系统管理页面样式 */
.system-admin {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 页面标题 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding: 20rpx 0;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.refresh-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  background-color: #007aff;
  color: white;
  border-radius: 50%;
  font-size: 24rpx;
}

.refresh-btn:disabled {
  background-color: #ccc;
}

.last-update {
  font-size: 24rpx;
  color: #666;
}

/* 标签页导航 */
.tab-navigation {
  display: flex;
  background-color: white;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.tab-item {
  flex: 1;
  padding: 24rpx;
  text-align: center;
  font-size: 28rpx;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tab-item.active {
  background-color: #007aff;
  color: white;
  font-weight: bold;
}

/* 标签页内容 */
.tab-content {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20rpx); }
  to { opacity: 1; transform: translateY(0); }
}

/* 卡片样式 */
.status-card,
.alerts-card,
.performance-card,
.deployment-card,
.feature-flags-card,
.testing-card,
.performance-modes,
.system-operations {
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

/* 系统健康状态 */
.health-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx 24rpx;
  border-radius: 16rpx;
  min-width: 120rpx;
}

.health-indicator.healthy {
  background-color: #34c759;
  color: white;
}

.health-indicator.warning {
  background-color: #ff9500;
  color: white;
}

.health-indicator.critical {
  background-color: #ff3b30;
  color: white;
}

.health-score {
  font-size: 32rpx;
  font-weight: bold;
}

.health-status {
  font-size: 20rpx;
  text-transform: uppercase;
}

.health-metrics {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.metric-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.metric-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.metric-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

/* 告警列表 */
.alerts-list {
  max-height: 400rpx;
  overflow-y: auto;
}

.alert-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20rpx;
  margin-bottom: 16rpx;
  border-radius: 12rpx;
  border-left: 6rpx solid;
}

.alert-item.warning {
  background-color: #fff3cd;
  border-left-color: #ff9500;
}

.alert-item.error {
  background-color: #f8d7da;
  border-left-color: #ff3b30;
}

.alert-content {
  flex: 1;
}

.alert-type {
  font-size: 24rpx;
  font-weight: bold;
  color: #666;
  display: block;
  margin-bottom: 8rpx;
}

.alert-message {
  font-size: 26rpx;
  color: #333;
  line-height: 1.4;
}

.alert-time {
  font-size: 22rpx;
  color: #999;
  white-space: nowrap;
}

/* 快速操作 */
.quick-actions {
  display: flex;
  gap: 20rpx;
  margin-top: 30rpx;
}

.action-btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: bold;
  border: none;
}

.action-btn.primary {
  background-color: #007aff;
  color: white;
}

.action-btn.secondary {
  background-color: #8e8e93;
  color: white;
}

/* 性能指标 */
.performance-metrics {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.metric-group {
  border: 2rpx solid #e5e5ea;
  border-radius: 12rpx;
  padding: 20rpx;
}

.group-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #007aff;
  margin-bottom: 20rpx;
  display: block;
}

.run-benchmark-btn {
  padding: 12rpx 24rpx;
  background-color: #34c759;
  color: white;
  border-radius: 8rpx;
  font-size: 24rpx;
}

/* 性能模式 */
.mode-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.mode-btn {
  padding: 24rpx;
  background-color: #f8f9fa;
  color: #333;
  border: 2rpx solid #e5e5ea;
  border-radius: 12rpx;
  font-size: 26rpx;
  transition: all 0.3s ease;
}

.mode-btn:active {
  background-color: #007aff;
  color: white;
  border-color: #007aff;
}

/* 部署状态 */
.deployment-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: white;
}

.deployment-status.deploying {
  background-color: #ff9500;
}

.deployment-status.idle {
  background-color: #8e8e93;
}

.deployment-info {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #e5e5ea;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 26rpx;
  color: #666;
}

.info-value {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
}

/* 功能开关 */
.feature-flags-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.feature-flag-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.flag-name {
  font-size: 26rpx;
  color: #333;
}

.flag-switch {
  transform: scale(0.8);
}

/* 测试结果 */
.test-summary {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.summary-item.success {
  background-color: #d4edda;
  color: #155724;
}

.summary-item.failed {
  background-color: #f8d7da;
  color: #721c24;
}

.summary-label {
  font-size: 24rpx;
  margin-bottom: 8rpx;
}

.summary-value {
  font-size: 32rpx;
  font-weight: bold;
}

.run-tests-btn {
  padding: 12rpx 24rpx;
  background-color: #007aff;
  color: white;
  border-radius: 8rpx;
  font-size: 24rpx;
}

/* 测试进度条 */
.test-progress {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.progress-bar {
  flex: 1;
  height: 12rpx;
  background-color: #e5e5ea;
  border-radius: 6rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #34c759;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 24rpx;
  color: #666;
  min-width: 80rpx;
  text-align: right;
}

/* 系统操作 */
.operation-buttons {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.operation-btn {
  padding: 24rpx;
  background-color: #f8f9fa;
  color: #333;
  border: 2rpx solid #e5e5ea;
  border-radius: 12rpx;
  font-size: 26rpx;
  text-align: center;
}

.operation-btn:active {
  background-color: #e5e5ea;
}

/* 加载遮罩 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  color: white;
  font-size: 28rpx;
}
