/* 微光星迹页面样式 */

.star-sky-container {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(180deg, #0a0a23 0%, #1a1a3a 50%, #2a2a4a 100%);
  position: relative;
  overflow: hidden;
}

/* 星空背景装饰 */
.star-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.bg-star {
  position: absolute;
  width: 2px;
  height: 2px;
  background: #ffffff;
  border-radius: 50%;
  animation: twinkle 3s infinite;
}

.bg-star-1 {
  top: 10%;
  left: 20%;
  animation-delay: 0s;
}
.bg-star-2 {
  top: 25%;
  left: 80%;
  animation-delay: 0.5s;
}
.bg-star-3 {
  top: 40%;
  left: 15%;
  animation-delay: 1s;
}
.bg-star-4 {
  top: 60%;
  left: 70%;
  animation-delay: 1.5s;
}
.bg-star-5 {
  top: 75%;
  left: 30%;
  animation-delay: 2s;
}
.bg-star-6 {
  top: 20%;
  left: 50%;
  animation-delay: 2.5s;
}
.bg-star-7 {
  top: 85%;
  left: 60%;
  animation-delay: 3s;
}
.bg-star-8 {
  top: 50%;
  left: 90%;
  animation-delay: 3.5s;
}

@keyframes twinkle {
  0%,
  100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

/* 页面头部 */
.page-header {
  text-align: center;
  padding: 60rpx 40rpx 40rpx;
  position: relative;
  z-index: 10;
}

.page-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 16rpx;
  text-shadow: 0 0 20rpx rgba(255, 255, 255, 0.5);
}

.page-subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.7);
  font-style: italic;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  position: relative;
  z-index: 10;
}

.loading-star {
  width: 60rpx;
  height: 60rpx;
  background: radial-gradient(circle, #ffd700 0%, #ffa500 100%);
  border-radius: 50%;
  animation: pulse 2s infinite;
  margin-bottom: 40rpx;
  box-shadow: 0 0 40rpx rgba(255, 215, 0, 0.6);
}

.loading-text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 28rpx;
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* 星光记录容器 */
.star-records-container {
  position: relative;
  width: 100%;
  height: calc(100vh - 200rpx);
  min-height: 600rpx;
  z-index: 10;
  overflow: hidden; /* 防止内容溢出 */
}

/* 地图视口 - 处理触摸事件和裁剪 */
.star-map-viewport {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  touch-action: none; /* 禁用默认触摸行为 */
}

/* 地图内容 - 可缩放和平移的容器 */
.star-map-content {
  position: relative;
  width: 100%;
  height: 100%;
  transition: transform 0.1s ease-out;
  will-change: transform; /* 优化变换性能 */
}

/* 对话星星 */
.dialogue-star {
  position: absolute;
  cursor: pointer;
  animation: starGlow 4s infinite;

  /* 动画增强属性 */
  transform-origin: center center;
  will-change: transform, opacity, box-shadow;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.star-body {
  position: relative;
  width: 40rpx;
  height: 40rpx;
}

.star-core {
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, #ffd700 0%, #ffa500 70%, #ff8c00 100%);
  border-radius: 50%;
  position: relative;
  z-index: 2;
  box-shadow: 0 0 20rpx rgba(255, 215, 0, 0.8);
}

.star-glow {
  position: absolute;
  top: -10rpx;
  left: -10rpx;
  width: 60rpx;
  height: 60rpx;
  background: radial-gradient(
    circle,
    rgba(255, 215, 0, 0.3) 0%,
    transparent 70%
  );
  border-radius: 50%;
  z-index: 1;
}

.star-info {
  position: absolute;
  top: 50rpx;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  padding: 16rpx 20rpx;
  border-radius: 12rpx;
  min-width: 120rpx;
  text-align: center;
  opacity: 0;
  transition: opacity 0.3s;
  pointer-events: none;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.1);
}

.dialogue-star:hover .star-info {
  opacity: 1;
}

.star-theme {
  display: block;
  color: #ffffff;
  font-size: 24rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.star-time {
  display: block;
  color: rgba(255, 255, 255, 0.7);
  font-size: 20rpx;
  margin-bottom: 8rpx;
}

.star-emotion {
  display: block;
  font-size: 20rpx;
  font-weight: bold;
}

@keyframes starGlow {
  0%,
  100% {
    filter: brightness(1);
  }
  50% {
    filter: brightness(1.3);
  }
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 500rpx;
  position: relative;
  z-index: 10;
}

.empty-star {
  width: 80rpx;
  height: 80rpx;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.3) 0%,
    transparent 70%
  );
  border-radius: 50%;
  margin-bottom: 40rpx;
  border: 2rpx dashed rgba(255, 255, 255, 0.3);
}

.empty-title {
  color: #ffffff;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.empty-subtitle {
  color: rgba(255, 255, 255, 0.6);
  font-size: 28rpx;
  margin-bottom: 40rpx;
}

.start-dialogue-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  border: none;
  border-radius: 50rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
  font-weight: bold;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
}

/* 详情弹窗 */
.detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(10rpx);
}

.detail-content {
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(255, 255, 255, 0.9) 100%
  );
  border-radius: 24rpx;
  padding: 40rpx;
  overflow-y: auto;
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32rpx;
  padding-bottom: 24rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.detail-title-area {
  flex: 1;
}

.detail-theme {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8rpx;
}

.detail-time {
  display: block;
  font-size: 24rpx;
  color: #666666;
}

.close-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #999999;
  cursor: pointer;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.05);
}

.detail-section {
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 16rpx;
  padding-left: 16rpx;
  border-left: 4rpx solid #667eea;
}

.section-content {
  background: rgba(255, 255, 255, 0.5);
  padding: 24rpx;
  border-radius: 16rpx;
  line-height: 1.6;
  color: #444444;
  font-size: 26rpx;
}

.summary-content {
  background: linear-gradient(
    135deg,
    rgba(102, 126, 234, 0.1) 0%,
    rgba(118, 75, 162, 0.1) 100%
  );
}

.questions-container {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 16rpx;
  overflow: hidden;
}

.question-item {
  padding: 24rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.question-item:last-child {
  border-bottom: none;
}

.question-text {
  font-size: 26rpx;
  font-weight: bold;
  color: #667eea;
  margin-bottom: 12rpx;
}

.answer-text {
  font-size: 26rpx;
  color: #444444;
  line-height: 1.6;
  padding-left: 20rpx;
  border-left: 3rpx solid rgba(102, 126, 234, 0.3);
}

.tomorrow-message {
  background: linear-gradient(
    135deg,
    rgba(255, 193, 7, 0.1) 0%,
    rgba(255, 152, 0, 0.1) 100%
  );
  font-style: italic;
}

.emotion-tag {
  display: inline-block;
  padding: 12rpx 24rpx;
  border-radius: 50rpx;
  font-size: 24rpx;
  font-weight: bold;
  text-align: center;
}

/* 新的星星信息卡片系统样式 */
.info-card-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(10rpx);
  animation: fadeIn 0.3s ease-out;
}

.info-card-content {
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(255, 255, 255, 0.9) 100%
  );
  border-radius: 24rpx;
  padding: 32rpx;
  overflow-y: auto;
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
  animation: slideUp 0.3s ease-out;
}

.info-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.card-title-area {
  flex: 1;
}

.card-theme {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 8rpx;
  line-height: 1.3;
}

.card-date {
  display: block;
  font-size: 24rpx;
  color: #666666;
  font-weight: 500;
}

.card-close-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #999999;
  cursor: pointer;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.card-close-btn:hover {
  background: rgba(0, 0, 0, 0.1);
  color: #666666;
}

.card-emotion-section {
  margin-bottom: 20rpx;
}

.card-emotion-tag {
  display: inline-block;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: bold;
  text-align: center;
  border: 1rpx solid currentColor;
}

.card-content-section {
  margin-bottom: 24rpx;
}

.card-content-preview {
  display: block;
  font-size: 26rpx;
  color: #555555;
  line-height: 1.6;
  background: rgba(0, 0, 0, 0.03);
  padding: 16rpx 20rpx;
  border-radius: 12rpx;
  border-left: 3rpx solid #667eea;
}

.card-actions {
  display: flex;
  gap: 16rpx;
  margin-bottom: 20rpx;
}

.card-action-btn {
  flex: 1;
  padding: 20rpx 24rpx;
  border-radius: 16rpx;
  font-size: 26rpx;
  font-weight: bold;
  border: none;
  transition: all 0.2s ease;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.diary-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
}

.diary-btn:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 16rpx rgba(102, 126, 234, 0.3);
}

.expand-btn {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: #ffffff;
}

.expand-btn:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 16rpx rgba(240, 147, 251, 0.3);
}

.card-expanded-content {
  border-top: 1rpx solid rgba(0, 0, 0, 0.1);
  padding-top: 24rpx;
  animation: expandIn 0.3s ease-out;
}

.card-detail-section {
  margin-bottom: 24rpx;
}

.card-section-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 12rpx;
  padding-left: 12rpx;
  border-left: 3rpx solid #667eea;
}

.card-section-content {
  background: rgba(255, 255, 255, 0.6);
  padding: 16rpx 20rpx;
  border-radius: 12rpx;
  line-height: 1.6;
  color: #444444;
  font-size: 24rpx;
}

.card-questions-container {
  background: rgba(255, 255, 255, 0.4);
  border-radius: 12rpx;
  overflow: hidden;
}

.card-question-item {
  padding: 16rpx 20rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.card-question-item:last-child {
  border-bottom: none;
}

.card-question-text {
  font-size: 24rpx;
  font-weight: bold;
  color: #667eea;
  margin-bottom: 8rpx;
}

.card-answer-text {
  font-size: 24rpx;
  color: #444444;
  line-height: 1.6;
  padding-left: 16rpx;
  border-left: 2rpx solid rgba(102, 126, 234, 0.3);
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30rpx) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes expandIn {
  from {
    opacity: 0;
    max-height: 0;
  }
  to {
    opacity: 1;
    max-height: 1000rpx;
  }
}

/* 地图导航控制样式 */
.map-controls {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  z-index: 100;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

/* 缩放控制组 */
.zoom-controls {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 16rpx;
  padding: 12rpx;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.1);
}

.zoom-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  border: none;
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  margin: 4rpx 0;
}

.zoom-in-btn {
  background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
  color: #ffffff;
  box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.3);
}

.zoom-in-btn:not(.disabled):active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(76, 175, 80, 0.4);
}

.zoom-out-btn {
  background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
  color: #ffffff;
  box-shadow: 0 4rpx 12rpx rgba(255, 152, 0, 0.3);
}

.zoom-out-btn:not(.disabled):active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(255, 152, 0, 0.4);
}

.zoom-btn.disabled {
  background: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.4);
  box-shadow: none;
  cursor: not-allowed;
}

.zoom-level {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  margin: 8rpx 0;
  min-width: 60rpx;
  font-weight: bold;
}

/* 其他控制按钮组 */
.other-controls {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.control-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
  background: rgba(0, 0, 0, 0.7);
  border: none;
  border-radius: 16rpx;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.1);
  transition: all 0.2s ease;
  padding: 8rpx;
}

.control-btn:active {
  transform: scale(0.95);
  background: rgba(0, 0, 0, 0.8);
}

.fit-all-btn {
  background: linear-gradient(
    135deg,
    rgba(102, 126, 234, 0.8) 0%,
    rgba(118, 75, 162, 0.8) 100%
  );
}

.reset-btn {
  background: linear-gradient(
    135deg,
    rgba(255, 193, 7, 0.8) 0%,
    rgba(255, 152, 0, 0.8) 100%
  );
}

.btn-icon {
  font-size: 28rpx;
  color: #ffffff;
  margin-bottom: 4rpx;
  font-weight: bold;
}

.btn-text {
  font-size: 18rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

/* 导航状态指示器 */
.navigation-indicator {
  position: absolute;
  bottom: 20rpx;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.6);
  color: rgba(255, 255, 255, 0.8);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.1);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 50;
}

.navigation-indicator.active {
  opacity: 1;
}

/* 响应式调整 */
@media (max-width: 750rpx) {
  .map-controls {
    top: 16rpx;
    right: 16rpx;
    gap: 12rpx;
  }

  .zoom-btn {
    width: 50rpx;
    height: 50rpx;
    font-size: 28rpx;
  }

  .control-btn {
    width: 70rpx;
    height: 70rpx;
  }

  .btn-icon {
    font-size: 24rpx;
  }

  .btn-text {
    font-size: 16rpx;
  }
}

/* 触摸反馈优化 */
.zoom-btn:not(.disabled):hover,
.control-btn:hover {
  filter: brightness(1.1);
}

/* 无障碍支持 */
.zoom-btn[aria-disabled="true"],
.control-btn[aria-disabled="true"] {
  opacity: 0.5;
  pointer-events: none;
}

/* 性能优化指示器样式 */
.performance-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 200;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 16rpx;
  padding: 24rpx 32rpx;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.1);
}

.loading-progress {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.progress-bar {
  width: 200rpx;
  height: 8rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.progress-text {
  color: rgba(255, 255, 255, 0.9);
  font-size: 24rpx;
  font-weight: 500;
}

/* 性能指标显示 */
.performance-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 16rpx;
}

.metrics-btn {
  background: linear-gradient(
    135deg,
    rgba(255, 193, 7, 0.8) 0%,
    rgba(255, 152, 0, 0.8) 100%
  );
}

.metrics-summary {
  background: rgba(0, 0, 0, 0.6);
  border-radius: 8rpx;
  padding: 4rpx 8rpx;
  backdrop-filter: blur(5rpx);
}

.metric-item {
  font-size: 16rpx;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

/* 性能优化动画 */
@keyframes performanceIndicator {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.9);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

.performance-indicator {
  animation: performanceIndicator 0.3s ease-out;
}

/* 星星渐进加载动画 */
.dialogue-star.loading {
  opacity: 0;
  transform: scale(0.5);
  animation: starLoadIn 0.5s ease-out forwards;
}

@keyframes starLoadIn {
  0% {
    opacity: 0;
    transform: scale(0.5);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 性能优化状态指示 */
.star-map-content.optimized {
  will-change: transform;
}

.star-map-content.culling-active .dialogue-star {
  transition: opacity 0.2s ease;
}

.dialogue-star.culled {
  opacity: 0;
  pointer-events: none;
}

/* LOD 级别样式 */
.dialogue-star[data-lod="high"] .star-core {
  box-shadow: 0 0 20rpx rgba(255, 215, 0, 0.8);
}

.dialogue-star[data-lod="medium"] .star-core {
  box-shadow: 0 0 15rpx rgba(255, 215, 0, 0.6);
}

.dialogue-star[data-lod="low"] .star-core {
  box-shadow: 0 0 10rpx rgba(255, 215, 0, 0.4);
}

.dialogue-star[data-lod="low"] .star-info {
  display: none; /* 低LOD时隐藏信息提示 */
}

/* 触摸优化指示器 */
.star-map-viewport.touch-optimized {
  touch-action: none;
  user-select: none;
}

.star-map-viewport.gesture-detected::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(102, 126, 234, 0.05);
  pointer-events: none;
  z-index: 1;
  transition: background 0.2s ease;
}

/* 性能模式指示器 */
.performance-mode-indicator {
  position: absolute;
  bottom: 80rpx;
  left: 20rpx;
  background: rgba(0, 0, 0, 0.7);
  color: rgba(255, 255, 255, 0.8);
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.1);
  z-index: 50;
}

/* 响应式性能优化 */
@media (max-width: 750rpx) {
  .performance-indicator {
    padding: 20rpx 24rpx;
  }

  .progress-bar {
    width: 160rpx;
    height: 6rpx;
  }

  .progress-text {
    font-size: 22rpx;
  }

  .metrics-summary {
    padding: 3rpx 6rpx;
  }

  .metric-item {
    font-size: 14rpx;
  }
}

/* ========== 新增动画和视觉效果样式 ========== */

/* 星星交互动画 */
.dialogue-star.tap-animation {
  animation: starTapEffect 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes starTapEffect {
  0% {
    transform: scale(1) rotate(0deg);
  }
  40% {
    transform: scale(0.9) rotate(15deg);
  }
  100% {
    transform: scale(1.1) rotate(0deg);
  }
}

/* 星星出现动画 */
.dialogue-star.appear-animation {
  animation: starAppearEffect 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes starAppearEffect {
  0% {
    opacity: 0;
    transform: scale(0) rotate(0deg);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2) rotate(180deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(360deg);
  }
}

/* 星星放置动画 */
.dialogue-star.placement-animation {
  animation: starPlacementEffect 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes starPlacementEffect {
  0% {
    transform: scale(1.5);
    opacity: 0.8;
  }
  60% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 星星悬停效果 */
.dialogue-star.hover-effect {
  transform: scale(1.2);
  filter: brightness(1.5);
  box-shadow: 0 0 30rpx rgba(255, 215, 0, 1);
}

/* 星星发光脉冲效果 */
.dialogue-star.pulse-effect {
  animation: starPulseEffect 1s ease-in-out infinite;
}

@keyframes starPulseEffect {
  0%,
  100% {
    transform: scale(1);
    box-shadow: 0 0 20rpx rgba(255, 215, 0, 0.8);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 0 40rpx rgba(255, 215, 0, 1);
  }
}

/* 波纹效果 */
.ripple-effect {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
  pointer-events: none;
  animation: rippleAnimation 0.6s ease-out;
}

@keyframes rippleAnimation {
  0% {
    transform: scale(0);
    opacity: 0.8;
  }
  100% {
    transform: scale(3);
    opacity: 0;
  }
}

/* 粒子效果 */
.particle-effect {
  position: absolute;
  width: 6rpx;
  height: 6rpx;
  background: #ffffff;
  border-radius: 50%;
  pointer-events: none;
  animation: particleAnimation 1s ease-out forwards;
}

@keyframes particleAnimation {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(0.5);
  }
}

/* 情感颜色主题 */
.dialogue-star[data-emotion="开心"] .star-core {
  background: radial-gradient(circle, #ffd700 0%, #ffa500 70%, #ff8c00 100%);
  box-shadow: 0 0 20rpx rgba(255, 215, 0, 0.8);
}

.dialogue-star[data-emotion="快乐"] .star-core {
  background: radial-gradient(circle, #ff69b4 0%, #ff1493 70%, #dc143c 100%);
  box-shadow: 0 0 20rpx rgba(255, 105, 180, 0.8);
}

.dialogue-star[data-emotion="平静"] .star-core {
  background: radial-gradient(circle, #87ceeb 0%, #4682b4 70%, #1e90ff 100%);
  box-shadow: 0 0 20rpx rgba(135, 206, 235, 0.8);
}

.dialogue-star[data-emotion="思考"] .star-core {
  background: radial-gradient(circle, #9370db 0%, #8a2be2 70%, #4b0082 100%);
  box-shadow: 0 0 20rpx rgba(147, 112, 219, 0.8);
}

.dialogue-star[data-emotion="难过"] .star-core {
  background: radial-gradient(circle, #4682b4 0%, #2f4f4f 70%, #191970 100%);
  box-shadow: 0 0 20rpx rgba(70, 130, 180, 0.6);
}

/* 动画性能优化 */
.dialogue-star.will-animate {
  will-change: transform, opacity, box-shadow;
}

.dialogue-star.animation-complete {
  will-change: auto;
}

/* ========== 星星放置模式样式 ========== */

/* 放置模式容器 */
.placement-mode-container {
  position: relative;
  width: 100%;
  height: calc(100vh - 200rpx);
  min-height: 600rpx;
  z-index: 10;
  background: linear-gradient(
    135deg,
    rgba(102, 126, 234, 0.05) 0%,
    rgba(118, 75, 162, 0.05) 100%
  );
  border: 2rpx dashed rgba(255, 255, 255, 0.3);
  border-radius: 24rpx;
  margin: 20rpx;
}

/* 放置指引 */
.placement-instructions {
  position: absolute;
  top: 40rpx;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  z-index: 100;
  background: rgba(0, 0, 0, 0.8);
  padding: 24rpx 32rpx;
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.3);
}

.instruction-icon {
  font-size: 48rpx;
  margin-bottom: 16rpx;
  display: block;
  animation: iconPulse 2s infinite;
}

@keyframes iconPulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
}

.instruction-text {
  display: block;
  color: #ffffff;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 12rpx;
  line-height: 1.4;
}

.instruction-subtitle {
  display: block;
  color: rgba(255, 255, 255, 0.7);
  font-size: 24rpx;
  line-height: 1.4;
}

/* 放置区域 */
.placement-area {
  position: relative;
  width: 100%;
  height: 100%;
  cursor: crosshair;
  overflow: hidden;
}

/* 现有星星（半透明显示） */
.existing-star {
  position: absolute;
  pointer-events: none;
  z-index: 10;
}

.existing-star .star-body {
  position: relative;
  width: 40rpx;
  height: 40rpx;
}

.existing-star .star-core {
  width: 100%;
  height: 100%;
  background: radial-gradient(
    circle,
    rgba(255, 215, 0, 0.3) 0%,
    rgba(255, 140, 0, 0.3) 100%
  );
  border-radius: 50%;
  border: 2rpx dashed rgba(255, 215, 0, 0.5);
}

/* 新星星预览 */
.new-star-preview {
  position: absolute;
  z-index: 50;
  transform: translate(-50%, -50%);
  pointer-events: none;
  animation: newStarPreview 2s infinite;
}

@keyframes newStarPreview {
  0%,
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.8;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 1;
  }
}

.new-star-body {
  position: relative;
  width: 50rpx;
  height: 50rpx;
}

.new-star-body .star-core {
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, #ffd700 0%, #ffa500 70%, #ff8c00 100%);
  border-radius: 50%;
  box-shadow: 0 0 30rpx rgba(255, 215, 0, 1);
  border: 3rpx solid rgba(255, 255, 255, 0.8);
}

.new-star-body .star-glow {
  position: absolute;
  top: -15rpx;
  left: -15rpx;
  width: 80rpx;
  height: 80rpx;
  background: radial-gradient(
    circle,
    rgba(255, 215, 0, 0.4) 0%,
    transparent 70%
  );
  border-radius: 50%;
  z-index: -1;
}

/* 轨道引导线 */
.orbit-guide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 20;
}

.orbit-circle {
  position: absolute;
  width: 200rpx;
  height: 200rpx;
  border: 2rpx dashed rgba(255, 215, 0, 0.6);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: orbitGuide 3s infinite;
}

@keyframes orbitGuide {
  0%,
  100% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.1);
  }
}

/* 放置模式操作按钮 */
.placement-actions {
  position: absolute;
  bottom: 40rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 24rpx;
  z-index: 100;
}

.placement-btn {
  padding: 24rpx 48rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  font-weight: bold;
  border: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10rpx);
}

.cancel-btn {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(255, 255, 255, 0.1) 100%
  );
  color: rgba(255, 255, 255, 0.9);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.cancel-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.3);
}

.confirm-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
}

.confirm-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.4);
}

.confirm-btn:disabled {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 100%
  );
  color: rgba(255, 255, 255, 0.4);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 放置模式触摸反馈 */
.placement-area.touching {
  background: linear-gradient(
    135deg,
    rgba(102, 126, 234, 0.1) 0%,
    rgba(118, 75, 162, 0.1) 100%
  );
}

/* 放置位置验证提示 */
.placement-validation {
  position: absolute;
  bottom: 120rpx;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: #ffffff;
  padding: 16rpx 24rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  z-index: 90;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.placement-validation.show {
  opacity: 1;
}

.placement-validation.valid {
  border-color: rgba(76, 175, 80, 0.5);
  background: rgba(76, 175, 80, 0.2);
}

.placement-validation.invalid {
  border-color: rgba(244, 67, 54, 0.5);
  background: rgba(244, 67, 54, 0.2);
}

/* 首颗星特殊样式 */
.placement-mode-container.first-star .placement-area {
  border-color: rgba(255, 215, 0, 0.6);
  background: linear-gradient(
    135deg,
    rgba(255, 215, 0, 0.05) 0%,
    rgba(255, 140, 0, 0.05) 100%
  );
}

.placement-mode-container.first-star .instruction-text {
  color: #ffd700;
}

.placement-mode-container.first-star .orbit-guide {
  display: none;
}

/* 轨道星特殊样式 */
.placement-mode-container.orbit-star .placement-area {
  border-color: rgba(102, 126, 234, 0.6);
}

.placement-mode-container.orbit-star .instruction-text {
  color: #667eea;
}

/* 放置模式进入动画 */
.placement-mode-container {
  animation: placementModeEnter 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes placementModeEnter {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 放置模式退出动画 */
.placement-mode-container.exiting {
  animation: placementModeExit 0.3s ease-in forwards;
}

@keyframes placementModeExit {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(0.95);
  }
}

/* 响应式调整 */
@media (max-width: 750rpx) {
  .placement-instructions {
    padding: 20rpx 24rpx;
    top: 30rpx;
  }

  .instruction-icon {
    font-size: 40rpx;
    margin-bottom: 12rpx;
  }

  .instruction-text {
    font-size: 28rpx;
    margin-bottom: 8rpx;
  }

  .instruction-subtitle {
    font-size: 22rpx;
  }

  .placement-btn {
    padding: 20rpx 36rpx;
    font-size: 26rpx;
  }

  .placement-actions {
    bottom: 30rpx;
    gap: 20rpx;
  }

  .new-star-body {
    width: 45rpx;
    height: 45rpx;
  }

  .orbit-circle {
    width: 160rpx;
    height: 160rpx;
  }
}

/* 无障碍支持 */
.placement-area[aria-label] {
  outline: none;
}

.placement-area:focus {
  border-color: rgba(255, 215, 0, 1);
  box-shadow: 0 0 0 4rpx rgba(255, 215, 0, 0.3);
}

/* 触摸优化 */
.placement-area {
  touch-action: none;
  user-select: none;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .placement-mode-container {
    border-color: #ffffff;
    background: rgba(0, 0, 0, 0.1);
  }

  .instruction-text {
    color: #ffffff;
  }

  .existing-star .star-core {
    border-color: #ffffff;
  }

  .orbit-circle {
    border-color: #ffffff;
  }
}

/* ========== 星星动画效果 ========== */

/* 星星出现动画 */
.star-appear-animation {
  animation: starAppearEffect 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes starAppearEffect {
  0% {
    opacity: 0;
    transform: scale(0) rotate(0deg);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2) rotate(180deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(360deg);
  }
}

/* 星星放置动画 */
.star-placement-animation {
  animation: starPlacementEffect 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes starPlacementEffect {
  0% {
    transform: scale(1.5);
    opacity: 0.8;
  }
  60% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 星星悬停动画 */
.star-hover-animation {
  animation: starHoverEffect 0.3s ease-out;
  transform: scale(1.2);
  filter: brightness(1.5);
  box-shadow: 0 0 30rpx rgba(255, 215, 0, 1);
}

@keyframes starHoverEffect {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.2);
  }
}

/* 星星拖拽动画 */
.star-drag-animation {
  transition: all 0.1s ease-out;
}

/* 轨道路径动画 */
.orbit-path {
  position: absolute;
  border: 2rpx dashed rgba(255, 215, 0, 0.6);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
  z-index: 30;
}

.orbit-path-animation {
  animation: orbitPathShow 0.5s ease-out;
}

@keyframes orbitPathShow {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

/* 放置确认脉冲效果 */
.placement-pulse-effect {
  position: absolute;
  width: 80rpx;
  height: 80rpx;
  transform: translate(-50%, -50%);
  pointer-events: none;
  z-index: 40;
  animation: placementPulse 1s ease-out;
}

.placement-pulse-effect::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
    circle,
    rgba(255, 215, 0, 0.6) 0%,
    rgba(255, 215, 0, 0.3) 50%,
    transparent 100%
  );
  border-radius: 50%;
  animation: pulseRing 1s ease-out;
}

@keyframes placementPulse {
  0% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(0.5);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(2);
  }
}

@keyframes pulseRing {
  0% {
    transform: scale(0.5);
    opacity: 1;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

/* 星星触摸反馈动画 */
.dialogue-star:active {
  animation: starTouchFeedback 0.2s ease-out;
}

@keyframes starTouchFeedback {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.95);
  }
  100% {
    transform: scale(1);
  }
}

/* 新星星预览增强动画 */
.new-star-preview.dragging {
  animation: newStarDrag 0.1s ease-out infinite alternate;
}

@keyframes newStarDrag {
  0% {
    transform: translate(-50%, -50%) scale(1) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) scale(1.05) rotate(2deg);
  }
}

/* 轨道引导线增强动画 */
.orbit-circle.active {
  animation: orbitGuideActive 2s ease-in-out infinite;
  border-color: rgba(255, 215, 0, 1);
  box-shadow: 0 0 20rpx rgba(255, 215, 0, 0.5);
}

@keyframes orbitGuideActive {
  0%,
  100% {
    opacity: 0.8;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.05);
  }
}

/* 星星连接线样式 */
.star-connection-line {
  position: absolute;
  height: 2rpx;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 215, 0, 0.4) 20%,
    rgba(255, 215, 0, 0.8) 50%,
    rgba(255, 215, 0, 0.4) 80%,
    transparent 100%
  );
  transform-origin: center center;
  pointer-events: none;
  z-index: 15;
  border-radius: 1rpx;
  box-shadow: 0 0 4rpx rgba(255, 215, 0, 0.3);
}

/* 连线出现动画 */
.connection-appear-animation {
  animation: connectionAppear 0.8s ease-out;
}

@keyframes connectionAppear {
  0% {
    opacity: 0;
    transform: translateX(-50%) translateY(-50%) rotate(var(--rotation))
      scaleX(0);
  }
  50% {
    opacity: 0.5;
    transform: translateX(-50%) translateY(-50%) rotate(var(--rotation))
      scaleX(0.7);
  }
  100% {
    opacity: 1;
    transform: translateX(-50%) translateY(-50%) rotate(var(--rotation))
      scaleX(1);
  }
}

/* 连线闪烁效果 */
.star-connection-line::after {
  content: "";
  position: absolute;
  top: -1rpx;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.6) 50%,
    transparent 100%
  );
  border-radius: 2rpx;
  animation: connectionGlow 3s ease-in-out infinite;
}

@keyframes connectionGlow {
  0%,
  100% {
    opacity: 0;
    transform: translateX(-100%);
  }
  50% {
    opacity: 1;
    transform: translateX(100%);
  }
}

/* 性能优化 - 动画元素 */
.dialogue-star.will-animate,
.new-star-preview.will-animate,
.orbit-path.will-animate,
.placement-pulse-effect.will-animate {
  will-change: transform, opacity, box-shadow;
}

.dialogue-star.animation-complete,
.new-star-preview.animation-complete,
.orbit-path.animation-complete,
.placement-pulse-effect.animation-complete {
  will-change: auto;
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .instruction-icon,
  .new-star-preview,
  .orbit-circle {
    animation: none;
  }

  .placement-mode-container {
    animation: none;
  }

  .placement-btn {
    transition: none;
  }

  .star-appear-animation,
  .star-placement-animation,
  .star-hover-animation,
  .orbit-path-animation,
  .placement-pulse-effect {
    animation: none;
  }

  .dialogue-star:active {
    animation: none;
  }
}
