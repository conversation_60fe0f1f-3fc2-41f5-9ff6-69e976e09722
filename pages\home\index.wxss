.container {
    position: relative;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
  }
  
  /* 背景图固定 */
  .background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    object-fit: cover;
    z-index: -1;
  }
  
  /* ✅ 顶部主标：更大 + 更亮 + 强化发光呼吸 */
  .main-logo {
    position: absolute;
    top: 13%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 560rpx;
    height: 560rpx;
    z-index: 10;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .weiguang-circle {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: contain;
    animation: elegantPulse 4.5s ease-in-out infinite;
    opacity: 0.97;
    filter: drop-shadow(0 0 48rpx rgba(255, 255, 210, 0.28))
      drop-shadow(0 0 80rpx rgba(255, 255, 240, 0.2))
      drop-shadow(0 0 120rpx rgba(255, 255, 255, 0.12));
  }
  
  @keyframes elegantPulse {
    0%,
    100% {
      transform: scale(1);
      opacity: 0.94;
    }
    50% {
      transform: scale(1.08);
      opacity: 1;
    }
  }
  
  .light-name {
    position: absolute;
    top: 20%;
    left: 50%;
    transform: translateX(-50%);
    font-size: 28rpx;
    color: #fdf6ec;
    opacity: 0.88;
    text-align: center;
    z-index: 15;
  }
  
  /* 主体结构整体上提 */
  .content {
    position: absolute;
    top: 500rpx;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 180rpx;
  }
  
  /* 通用图标结构 */
  .icon-block {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  
  .icon-wrapper {
    width: 180rpx;
    height: 180rpx;
  }
  
  .icon-glow {
    width: 100%;
    height: 100%;
  }
  
  /* 底部左右图标排列 */
  .bottom-icons {
    display: flex;
    justify-content: center;
    gap: 120rpx;
  }
  
  /* 图标按钮样式 */
  .icon-button {
    margin-top: 16rpx;
    padding: 12rpx 32rpx;
    border: 1px solid rgba(255, 255, 255, 0.28);
    border-radius: 24rpx;
    background: rgba(255, 255, 255, 0.04);
    color: #fdf6ec;
    font-size: 24rpx;
    text-align: center;
    backdrop-filter: blur(8rpx);
    line-height: 36rpx;
  }
  
  /* --- 以下是为签到入口新增的样式 --- */
  .checkin-entry-icon {
    position: fixed;
    /* 与帮助按钮保持同一垂直位置 */
    top: 120rpx; 
    /* 放在帮助按钮的左侧，并留出 20rpx 的间距 */
    right: 140rpx; /* 40rpx(帮助按钮right) + 80rpx(帮助按钮width) + 20rpx(间距) */
    width: 80rpx;
    height: 80rpx;
    /* 视觉风格与帮助按钮保持完全一致 */
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10rpx);
    z-index: 20;
    transition: all 0.3s ease;
    /* 单独调整 Emoji 的样式 */
    font-size: 40rpx;
    color: #fff;
  }

  .checkin-entry-icon:active {
    transform: scale(0.95);
    background: rgba(255, 255, 255, 0.2);
  }

  /* 帮助按钮样式 */
  .help-button {
    position: fixed;
    top: 120rpx;
    right: 40rpx;
    width: 80rpx;
    height: 80rpx;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10rpx);
    z-index: 20;
    transition: all 0.3s ease;
  }
  
  .help-button:active {
    transform: scale(0.95);
    background: rgba(255, 255, 255, 0.2);
  }
  
  .help-icon {
    font-size: 36rpx;
    line-height: 1;
  }
