/**
 * Star Lighting Manager
 * Handles the transition from "三问" completion to star map
 */

const { ANIMATION_CONFIG } = require('./config/ConstellationConfig');

/**
 * Manages star lighting effects and navigation
 */
class StarLightingManager {
    constructor() {
        this.isLighting = false;
        this.lightingCallbacks = [];
    }

    /**
     * Trigger flash effect after three questions completion
     * @param {Object} starData - Star data from dialogue session
     * @returns {Promise<Object>} Result of lighting effect
     */
    async triggerLightingEffect(starData) {
        if (this.isLighting) {
            return { success: false, error: 'Lighting effect already in progress' };
        }

        try {
            this.isLighting = true;

            // Validate star data
            if (!this.validateStarData(starData)) {
                throw new Error('Invalid star data provided');
            }

            // Generate star metadata from dialogue session
            const starMetadata = this.generateStarMetadata(starData);

            // Trigger flash animation
            await this.playFlashAnimation();

            // Prepare for navigation
            const navigationData = {
                starData: starMetadata,
                isNewStar: true,
                timestamp: Date.now()
            };

            this.isLighting = false;

            return {
                success: true,
                navigationData,
                starMetadata
            };

        } catch (error) {
            this.isLighting = false;
            console.error('Star lighting effect failed:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Navigate to star map with new star information
     * @param {Object} starData - Star metadata
     * @returns {Promise<Object>} Navigation result
     */
    async navigateToStarMap(starData) {
        try {
            // Prepare navigation parameters
            const navigationParams = {
                newStar: true,
                starData: JSON.stringify(starData),
                timestamp: Date.now()
            };

            // Navigate to star map page
            wx.navigateTo({
                url: `/pages/starTrack/index?${this.buildQueryString(navigationParams)}`
            });

            return { success: true };

        } catch (error) {
            console.error('Navigation to star map failed:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Generate star metadata from dialogue session
     * @param {Object} dialogueData - Dialogue session data
     * @returns {Object} Star metadata
     */
    generateStarMetadata(dialogueData) {
        const now = new Date();

        return {
            dialogueTheme: dialogueData.theme || '未知主题',
            coreFeeling: this.extractCoreFeeling(dialogueData),
            lightingDate: now,
            hasGeneratedDiary: dialogueData.hasGeneratedDiary || false,
            emotionKeyword: this.extractEmotionKeyword(dialogueData),
            dialogueContent: dialogueData.dialogueContent || [],
            questions: dialogueData.questions || [],
            answers: dialogueData.answers || []
        };
    }

    /**
     * Extract core feeling from first question answer
     * @param {Object} dialogueData - Dialogue data
     * @returns {string} Core feeling
     */
    extractCoreFeeling(dialogueData) {
        if (dialogueData.answers && dialogueData.answers.length > 0) {
            return dialogueData.answers[0] || '未知感受';
        }
        return '未知感受';
    }

    /**
     * Extract emotion keyword for star coloring
     * @param {Object} dialogueData - Dialogue data
     * @returns {string} Emotion keyword
     */
    extractEmotionKeyword(dialogueData) {
        // Simple emotion extraction logic
        const coreFeeling = this.extractCoreFeeling(dialogueData);

        // Map feelings to emotion keywords
        const emotionMap = {
            '开心': '开心',
            '快乐': '快乐',
            '高兴': '开心',
            '平静': '平静',
            '安静': '安静',
            '宁静': '宁静',
            '困惑': '困惑',
            '迷茫': '迷茫',
            '纠结': '纠结',
            '焦虑': '焦虑',
            '担心': '担心',
            '紧张': '紧张',
            '感动': '感动',
            '温暖': '温暖',
            '治愈': '治愈'
        };

        // Find matching emotion
        for (const [feeling, emotion] of Object.entries(emotionMap)) {
            if (coreFeeling.includes(feeling)) {
                return emotion;
            }
        }

        return 'DEFAULT';
    }

    /**
     * Play flash animation effect
     * @returns {Promise<void>} Animation completion promise
     */
    async playFlashAnimation() {
        return new Promise((resolve) => {
            // Create flash effect elements
            const flashOverlay = this.createFlashOverlay();

            // Add to page
            const page = getCurrentPages()[getCurrentPages().length - 1];
            if (page && page.selectComponent) {
                // For component-based pages
                const container = page.selectComponent('.page-container') || page;
                if (container) {
                    container.appendChild(flashOverlay);
                }
            }

            // Animate flash effect
            setTimeout(() => {
                flashOverlay.style.opacity = '1';

                setTimeout(() => {
                    flashOverlay.style.opacity = '0';

                    setTimeout(() => {
                        if (flashOverlay.parentNode) {
                            flashOverlay.parentNode.removeChild(flashOverlay);
                        }
                        resolve();
                    }, 200);
                }, ANIMATION_CONFIG.LIGHTING_FLASH_DURATION);
            }, 50);
        });
    }

    /**
     * Create flash overlay element
     * @returns {HTMLElement} Flash overlay element
     */
    createFlashOverlay() {
        const overlay = document.createElement('div');
        overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: radial-gradient(circle, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.3) 100%);
      opacity: 0;
      transition: opacity 0.2s ease;
      pointer-events: none;
      z-index: 9999;
    `;
        return overlay;
    }

    /**
     * Validate star data before processing
     * @param {Object} starData - Star data to validate
     * @returns {boolean} Validation result
     */
    validateStarData(starData) {
        if (!starData || typeof starData !== 'object') {
            return false;
        }

        // Check required fields
        const requiredFields = ['theme', 'answers'];
        for (const field of requiredFields) {
            if (!(field in starData)) {
                console.warn(`Missing required field: ${field}`);
                return false;
            }
        }

        return true;
    }

    /**
     * Build query string from parameters
     * @param {Object} params - Parameters object
     * @returns {string} Query string
     */
    buildQueryString(params) {
        return Object.entries(params)
            .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
            .join('&');
    }

    /**
     * Add callback for lighting completion
     * @param {Function} callback - Callback function
     */
    onLightingComplete(callback) {
        if (typeof callback === 'function') {
            this.lightingCallbacks.push(callback);
        }
    }

    /**
     * Remove lighting completion callback
     * @param {Function} callback - Callback function to remove
     */
    offLightingComplete(callback) {
        const index = this.lightingCallbacks.indexOf(callback);
        if (index > -1) {
            this.lightingCallbacks.splice(index, 1);
        }
    }

    /**
     * Trigger all lighting completion callbacks
     * @param {Object} data - Callback data
     */
    triggerLightingCallbacks(data) {
        this.lightingCallbacks.forEach(callback => {
            try {
                callback(data);
            } catch (error) {
                console.error('Lighting callback error:', error);
            }
        });
    }
}

module.exports = {
    StarLightingManager
};