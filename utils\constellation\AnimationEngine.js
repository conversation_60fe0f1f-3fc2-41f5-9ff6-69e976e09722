/**
 * Animation Engine
 * Manages visual effects and animations for the star constellation system
 * Handles the "哐哐哐" flashing effect and other star-related animations
 */

const { ANIMATION_CONFIG, VISUAL_CONFIG } = require('./config/ConstellationConfig');

/**
 * Animation Engine for managing visual effects
 */
class AnimationEngine {
    constructor() {
        this.activeAnimations = new Map();
        this.animationId = 0;
        this.soundEnabled = true;
        this.isInitialized = false;

        this.init();
    }

    /**
     * Initialize the animation engine
     */
    init() {
        try {
            // Check device capabilities
            this.checkDeviceCapabilities();

            // Initialize sound system
            this.initSoundSystem();

            this.isInitialized = true;
            console.log('AnimationEngine initialized successfully');
        } catch (error) {
            console.error('AnimationEngine initialization failed:', error);
            this.isInitialized = false;
        }
    }

    /**
     * Check device capabilities for animations
     */
    checkDeviceCapabilities() {
        try {
            // Check if running in WeChat Mini Program environment
            if (typeof wx !== 'undefined') {
                wx.getSystemInfo({
                    success: (res) => {
                        this.deviceInfo = {
                            platform: res.platform,
                            pixelRatio: res.pixelRatio,
                            windowWidth: res.windowWidth,
                            windowHeight: res.windowHeight,
                            supportsVibration: res.platform === 'ios' || res.platform === 'android'
                        };
                        console.log('Device capabilities detected:', this.deviceInfo);
                    },
                    fail: (error) => {
                        console.warn('Failed to get device info:', error);
                        this.deviceInfo = { supportsVibration: false };
                    }
                });
            }
        } catch (error) {
            console.warn('Device capability check failed:', error);
            this.deviceInfo = { supportsVibration: false };
        }
    }

    /**
     * Initialize sound system
     */
    initSoundSystem() {
        try {
            if (typeof wx !== 'undefined' && wx.createInnerAudioContext) {
                this.audioContext = wx.createInnerAudioContext();
                this.audioContext.autoplay = false;
                this.audioContext.loop = false;

                // Set up audio event handlers
                this.audioContext.onError((error) => {
                    console.warn('Audio context error:', error);
                    this.soundEnabled = false;
                });

                console.log('Sound system initialized');
            } else {
                console.log('Sound system not available');
                this.soundEnabled = false;
            }
        } catch (error) {
            console.warn('Sound system initialization failed:', error);
            this.soundEnabled = false;
        }
    }

    /**
     * Play the "哐哐哐" lighting effect with multiple flash layers
     * @param {Object} options - Animation options
     * @param {number} options.duration - Total animation duration (ms)
     * @param {number} options.flashCount - Number of flashes
     * @param {boolean} options.withSound - Whether to play sound effects
     * @param {boolean} options.withVibration - Whether to trigger vibration
     * @returns {Promise<void>} Animation completion promise
     */
    async playLightingEffect(options = {}) {
        const config = {
            duration: options.duration || ANIMATION_CONFIG.LIGHTING_FLASH_DURATION,
            flashCount: options.flashCount || 3,
            withSound: options.withSound !== false && this.soundEnabled,
            withVibration: options.withVibration !== false,
            intensity: options.intensity || 1.0,
            ...options
        };

        console.log('Starting lighting effect with config:', config);

        return new Promise((resolve, reject) => {
            try {
                const animationId = this.generateAnimationId();

                // Create animation state
                const animationState = {
                    id: animationId,
                    type: 'lighting',
                    startTime: Date.now(),
                    config: config,
                    resolve: resolve,
                    reject: reject,
                    currentFlash: 0,
                    flashElements: []
                };

                this.activeAnimations.set(animationId, animationState);

                // Start the lighting sequence
                this.executeLightingSequence(animationState);

            } catch (error) {
                console.error('Failed to start lighting effect:', error);
                reject(error);
            }
        });
    }

    /**
     * Execute the lighting animation sequence
     * @param {Object} animationState - Animation state object
     */
    executeLightingSequence(animationState) {
        const { config } = animationState;
        const flashInterval = config.duration / config.flashCount;

        // Create flash overlay layers
        this.createFlashLayers(animationState);

        // Execute flash sequence
        const executeFlash = () => {
            if (animationState.currentFlash >= config.flashCount) {
                // Animation complete
                this.completeLightingAnimation(animationState);
                return;
            }

            // Trigger single flash
            this.triggerSingleFlash(animationState, animationState.currentFlash);

            // Play sound effect
            if (config.withSound) {
                this.playFlashSound(animationState.currentFlash);
            }

            // Trigger vibration
            if (config.withVibration) {
                this.triggerVibration('flash');
            }

            animationState.currentFlash++;

            // Schedule next flash
            setTimeout(executeFlash, flashInterval);
        };

        // Start flash sequence
        executeFlash();
    }

    /**
     * Create multiple flash overlay layers for enhanced effect
     * @param {Object} animationState - Animation state object
     */
    createFlashLayers(animationState) {
        const layerCount = 3; // Multiple layers for depth effect

        for (let i = 0; i < layerCount; i++) {
            const layer = this.createFlashLayer(i, layerCount);
            animationState.flashElements.push(layer);

            // Add to page if in WeChat Mini Program environment
            if (typeof getCurrentPages === 'function') {
                try {
                    const currentPage = getCurrentPages()[getCurrentPages().length - 1];
                    if (currentPage && currentPage.setData) {
                        // For Mini Program, we'll use setData to control CSS classes
                        const flashData = {};
                        flashData[`flashLayer${i}`] = true;
                        currentPage.setData(flashData);
                    }
                } catch (error) {
                    console.warn('Failed to add flash layer to page:', error);
                }
            }
        }
    }

    /**
     * Create a single flash layer
     * @param {number} layerIndex - Layer index
     * @param {number} totalLayers - Total number of layers
     * @returns {Object} Flash layer configuration
     */
    createFlashLayer(layerIndex, totalLayers) {
        const opacity = 0.9 - (layerIndex * 0.2); // Decreasing opacity for depth
        const scale = 1.0 + (layerIndex * 0.1); // Slightly different scales

        return {
            id: `flash-layer-${layerIndex}`,
            opacity: opacity,
            scale: scale,
            duration: ANIMATION_CONFIG.LIGHTING_FLASH_DURATION / 3,
            delay: layerIndex * 50, // Staggered timing
            gradient: this.generateFlashGradient(layerIndex, totalLayers)
        };
    }

    /**
     * Generate gradient for flash layer
     * @param {number} layerIndex - Layer index
     * @param {number} totalLayers - Total number of layers
     * @returns {string} CSS gradient string
     */
    generateFlashGradient(layerIndex, totalLayers) {
        const centerIntensity = 0.9 - (layerIndex * 0.2);
        const edgeIntensity = centerIntensity * 0.3;

        return `radial-gradient(circle, rgba(255,255,255,${centerIntensity}) 0%, rgba(255,255,255,${edgeIntensity}) 70%, rgba(255,255,255,0) 100%)`;
    }

    /**
     * Trigger a single flash in the sequence
     * @param {Object} animationState - Animation state object
     * @param {number} flashIndex - Current flash index
     */
    triggerSingleFlash(animationState, flashIndex) {
        const { config } = animationState;
        const intensity = config.intensity * (1.0 + flashIndex * 0.1); // Increasing intensity

        // Animate each flash layer
        animationState.flashElements.forEach((layer, layerIndex) => {
            setTimeout(() => {
                this.animateFlashLayer(layer, intensity, flashIndex);
            }, layer.delay);
        });

        console.log(`Flash ${flashIndex + 1}/${config.flashCount} triggered with intensity ${intensity.toFixed(2)}`);
    }

    /**
     * Animate a single flash layer
     * @param {Object} layer - Flash layer configuration
     * @param {number} intensity - Flash intensity
     * @param {number} flashIndex - Current flash index
     */
    animateFlashLayer(layer, intensity, flashIndex) {
        if (typeof getCurrentPages === 'function') {
            try {
                const currentPage = getCurrentPages()[getCurrentPages().length - 1];
                if (currentPage && currentPage.setData) {
                    // Trigger flash animation via CSS class
                    const flashData = {};
                    flashData[`flashActive${layer.id.split('-')[2]}`] = true;
                    flashData[`flashIntensity${layer.id.split('-')[2]}`] = intensity;

                    currentPage.setData(flashData);

                    // Remove flash class after animation
                    setTimeout(() => {
                        const resetData = {};
                        resetData[`flashActive${layer.id.split('-')[2]}`] = false;
                        currentPage.setData(resetData);
                    }, layer.duration);
                }
            } catch (error) {
                console.warn('Failed to animate flash layer:', error);
            }
        }
    }

    /**
     * Play flash sound effect
     * @param {number} flashIndex - Current flash index
     */
    playFlashSound(flashIndex) {
        if (!this.soundEnabled || !this.audioContext) {
            return;
        }

        try {
            // Different sound frequencies for each flash
            const frequencies = [800, 1000, 1200]; // Hz
            const frequency = frequencies[flashIndex % frequencies.length];

            // For WeChat Mini Program, we would use predefined audio files
            // Here we simulate the sound trigger
            console.log(`Playing flash sound ${flashIndex + 1} at ${frequency}Hz`);

            // In a real implementation, you would load and play audio files:
            // this.audioContext.src = `/sounds/flash_${flashIndex + 1}.mp3`;
            // this.audioContext.play();

        } catch (error) {
            console.warn('Failed to play flash sound:', error);
        }
    }

    /**
     * Trigger haptic vibration feedback
     * @param {string} type - Vibration type ('flash', 'success', 'error')
     */
    triggerVibration(type = 'flash') {
        if (!this.deviceInfo?.supportsVibration) {
            return;
        }

        try {
            if (typeof wx !== 'undefined' && wx.vibrateShort) {
                const vibrationMap = {
                    'flash': 'heavy',
                    'success': 'medium',
                    'error': 'heavy'
                };

                wx.vibrateShort({
                    type: vibrationMap[type] || 'medium'
                });
            }
        } catch (error) {
            console.warn('Vibration failed:', error);
        }
    }

    /**
     * Complete the lighting animation
     * @param {Object} animationState - Animation state object
     */
    completeLightingAnimation(animationState) {
        console.log('Lighting animation completed');

        // Clean up flash elements
        this.cleanupFlashElements(animationState);

        // Final vibration for completion
        if (animationState.config.withVibration) {
            setTimeout(() => {
                this.triggerVibration('success');
            }, 200);
        }

        // Remove from active animations
        this.activeAnimations.delete(animationState.id);

        // Resolve the promise
        animationState.resolve();
    }

    /**
     * Clean up flash elements
     * @param {Object} animationState - Animation state object
     */
    cleanupFlashElements(animationState) {
        if (typeof getCurrentPages === 'function') {
            try {
                const currentPage = getCurrentPages()[getCurrentPages().length - 1];
                if (currentPage && currentPage.setData) {
                    // Reset all flash-related data
                    const resetData = {};
                    for (let i = 0; i < animationState.flashElements.length; i++) {
                        resetData[`flashLayer${i}`] = false;
                        resetData[`flashActive${i}`] = false;
                        resetData[`flashIntensity${i}`] = 0;
                    }
                    currentPage.setData(resetData);
                }
            } catch (error) {
                console.warn('Failed to cleanup flash elements:', error);
            }
        }
    }

    /**
     * Animate star appearance
     * @param {Object} starElement - Star element or identifier
     * @param {number} delay - Animation delay (ms)
     * @returns {Promise<void>} Animation completion promise
     */
    async animateStarAppearance(starElement, delay = 0) {
        return new Promise((resolve) => {
            setTimeout(() => {
                console.log('Animating star appearance with delay:', delay);

                if (typeof getCurrentPages === 'function') {
                    try {
                        const currentPage = getCurrentPages()[getCurrentPages().length - 1];
                        if (currentPage && currentPage.setData) {
                            currentPage.setData({
                                starAppearing: true,
                                starAppearanceDelay: delay
                            });

                            setTimeout(() => {
                                currentPage.setData({ starAppearing: false });
                                resolve();
                            }, ANIMATION_CONFIG.STAR_APPEARANCE_DURATION);
                        } else {
                            resolve();
                        }
                    } catch (error) {
                        console.warn('Star appearance animation failed:', error);
                        resolve();
                    }
                } else {
                    resolve();
                }
            }, delay);
        });
    }

    /**
     * Show orbital path visualization
     * @param {Object} centerPoint - Center point {x, y}
     * @param {number} radius - Orbital radius
     * @param {number} duration - Display duration (ms)
     * @returns {Promise<void>} Animation completion promise
     */
    async showOrbitPath(centerPoint, radius, duration = ANIMATION_CONFIG.ORBIT_PATH_DURATION) {
        return new Promise((resolve) => {
            console.log('Showing orbit path:', { centerPoint, radius, duration });

            if (typeof getCurrentPages === 'function') {
                try {
                    const currentPage = getCurrentPages()[getCurrentPages().length - 1];
                    if (currentPage && currentPage.setData) {
                        currentPage.setData({
                            orbitPathVisible: true,
                            orbitCenter: centerPoint,
                            orbitRadius: radius
                        });

                        setTimeout(() => {
                            currentPage.setData({ orbitPathVisible: false });
                            resolve();
                        }, duration);
                    } else {
                        resolve();
                    }
                } catch (error) {
                    console.warn('Orbit path animation failed:', error);
                    resolve();
                }
            } else {
                resolve();
            }
        });
    }

    /**
     * Animate star glow effect
     * @param {Object} starElement - Star element or identifier
     * @param {number} intensity - Glow intensity (0-1)
     * @returns {Promise<void>} Animation completion promise
     */
    async animateStarGlow(starElement, intensity = VISUAL_CONFIG.GLOW_INTENSITY.DEFAULT) {
        return new Promise((resolve) => {
            console.log('Animating star glow with intensity:', intensity);

            if (typeof getCurrentPages === 'function') {
                try {
                    const currentPage = getCurrentPages()[getCurrentPages().length - 1];
                    if (currentPage && currentPage.setData) {
                        currentPage.setData({
                            starGlowing: true,
                            glowIntensity: intensity
                        });

                        setTimeout(() => {
                            currentPage.setData({ starGlowing: false });
                            resolve();
                        }, ANIMATION_CONFIG.GLOW_CYCLE_DURATION);
                    } else {
                        resolve();
                    }
                } catch (error) {
                    console.warn('Star glow animation failed:', error);
                    resolve();
                }
            } else {
                resolve();
            }
        });
    }

    /**
     * Smooth transition to placement mode
     * @param {Object} starData - Star data for the new star
     * @returns {Promise<void>} Transition completion promise
     */
    async transitionToPlacementMode(starData) {
        return new Promise((resolve) => {
            console.log('Transitioning to placement mode for star:', starData);

            if (typeof getCurrentPages === 'function') {
                try {
                    const currentPage = getCurrentPages()[getCurrentPages().length - 1];
                    if (currentPage && currentPage.setData) {
                        currentPage.setData({
                            placementMode: true,
                            newStarData: starData,
                            transitionActive: true
                        });

                        setTimeout(() => {
                            currentPage.setData({ transitionActive: false });
                            resolve();
                        }, ANIMATION_CONFIG.PLACEMENT_CONFIRM_DURATION);
                    } else {
                        resolve();
                    }
                } catch (error) {
                    console.warn('Placement mode transition failed:', error);
                    resolve();
                }
            } else {
                resolve();
            }
        });
    }

    /**
     * Generate unique animation ID
     * @returns {string} Unique animation ID
     */
    generateAnimationId() {
        return `anim_${++this.animationId}_${Date.now()}`;
    }

    /**
     * Cancel an active animation
     * @param {string} animationId - Animation ID to cancel
     */
    cancelAnimation(animationId) {
        const animation = this.activeAnimations.get(animationId);
        if (animation) {
            console.log('Cancelling animation:', animationId);

            // Clean up animation
            if (animation.type === 'lighting') {
                this.cleanupFlashElements(animation);
            }

            // Reject the promise
            animation.reject(new Error('Animation cancelled'));

            // Remove from active animations
            this.activeAnimations.delete(animationId);
        }
    }

    /**
     * Cancel all active animations
     */
    cancelAllAnimations() {
        console.log('Cancelling all active animations');

        for (const [animationId] of this.activeAnimations) {
            this.cancelAnimation(animationId);
        }
    }

    /**
     * Get animation engine status
     * @returns {Object} Status information
     */
    getStatus() {
        return {
            isInitialized: this.isInitialized,
            activeAnimations: this.activeAnimations.size,
            soundEnabled: this.soundEnabled,
            deviceInfo: this.deviceInfo
        };
    }

    /**
     * Cleanup resources
     */
    destroy() {
        console.log('Destroying AnimationEngine');

        // Cancel all animations
        this.cancelAllAnimations();

        // Cleanup audio context
        if (this.audioContext) {
            try {
                this.audioContext.destroy();
            } catch (error) {
                console.warn('Failed to destroy audio context:', error);
            }
        }

        // Reset state
        this.isInitialized = false;
        this.activeAnimations.clear();
    }
}

module.exports = AnimationEngine;