const app = getApp();

// 故事数据现在通过云函数获取

// 使用全局后台音频管理器
let backgroundAudioManager; Page({

    data: {
        menuButtonInfo: {},
        story: null,
        navVisible: true,
        isPlaying: false,
        progress: 0,
        progressStyle: 'width: 0%',
        currentTime: '00:00',
        duration: '00:00',
        timerId: null,
        showTimerSheet: false,
        // 用于章节切换
        currentCharId: '',
        currentChapId: 1,
        isFirstChapter: true,
        isLastChapter: false
    },

    onLoad: function (options) {
        try {
            const charId = options.charId;
            const chapId = parseInt(options.chapId);

            if (!charId || !chapId) {
                this.showErrorAndBack('参数错误...');
                return;
            }

            this.setData({
                menuButtonInfo: app.globalData.menuButtonInfo,
                currentCharId: charId,
                currentChapId: chapId,
                isFirstChapter: chapId === 1,
                isLastChapter: chapId === 10 // 假设总共10章
            });

            // 设置后台音频管理
            this.setupBackgroundAudio();
            this.loadStory(charId, chapId);

        } catch (e) {
            console.error("加载故事页面失败:", e);
            this.showErrorAndBack('页面加载失败');
        }
    },

    // 设置后台音频管理
    setupBackgroundAudio: function () {
        // 获取全局后台音频管理器
        backgroundAudioManager = app.globalData.backgroundAudioManager;

        if (!backgroundAudioManager) {
            console.error('后台音频管理器未初始化，尝试重新创建');
            backgroundAudioManager = wx.getBackgroundAudioManager();
            app.globalData.backgroundAudioManager = backgroundAudioManager;
        }

        console.log('后台音频管理器已获取');
        console.log('当前后台音频状态:', {
            paused: backgroundAudioManager.paused,
            src: backgroundAudioManager.src,
            title: backgroundAudioManager.title
        });

        // 只绑定一次事件监听器
        this.bindAudioEvents();

        // 启动定时器来监控音频播放状态，确保息屏状态下也能自动跳转
        this.startAudioMonitor();

        // 监听小程序切换到后台
        wx.onAppHide(() => {
            console.log('小程序切换到后台，音频应该继续播放');
            console.log('当前播放状态:', !backgroundAudioManager.paused);
            console.log('音频源:', backgroundAudioManager.src);
        });

        // 监听小程序切换到前台
        wx.onAppShow(() => {
            console.log('小程序切换到前台');
            console.log('当前播放状态:', !backgroundAudioManager.paused);
        });
    },

    // 启动音频监控定时器，确保息屏状态下也能自动跳转
    startAudioMonitor: function () {
        // 清除之前的监控定时器
        if (this.audioMonitorTimer) {
            clearInterval(this.audioMonitorTimer);
        }

        const self = this;
        let lastCurrentTime = 0;
        let stuckCount = 0;

        this.audioMonitorTimer = setInterval(() => {
            if (!backgroundAudioManager || !backgroundAudioManager.src) {
                return;
            }

            const currentTime = backgroundAudioManager.currentTime;
            const duration = backgroundAudioManager.duration;
            const paused = backgroundAudioManager.paused;

            // 检查音频是否播放结束
            if (duration > 0 && currentTime >= duration - 1 && !paused) {
                console.log('定时器检测到音频播放结束，触发自动跳转');
                self.checkAndPlayNextChapter();
                return;
            }

            // 检查音频是否卡住（播放时间没有变化）
            if (!paused && currentTime === lastCurrentTime) {
                stuckCount++;
                if (stuckCount >= 3) { // 连续3次检测到卡住
                    console.log('检测到音频播放卡住，可能已结束');
                    self.checkAndPlayNextChapter();
                    stuckCount = 0;
                }
            } else {
                stuckCount = 0;
            }

            lastCurrentTime = currentTime;
        }, 2000); // 每2秒检查一次
    },

    // 页面隐藏时保持音频播放
    onHide: function () {
        console.log('页面隐藏，音频继续播放');
    },

    // 页面显示时同步音频状态
    onShow: function () {
        console.log('页面显示，同步音频状态');
        // 同步播放状态
        if (backgroundAudioManager && !backgroundAudioManager.paused) {
            this.setData({ isPlaying: true });
        }
    },

    // 加载故事
    loadStory: function (charId, chapId) {
        wx.showLoading({
            title: '加载中...'
        });

        wx.cloud.callFunction({
            name: 'getStoryData',
            data: {
                type: 'getChapterContent',
                charId: charId,
                chapId: chapId
            }
        }).then(res => {
            wx.hideLoading();
            if (res.result && res.result.success) {
                const storyData = res.result.data;
                storyData.paragraphs = storyData.content.split('\n\n');
                this.setData({ story: storyData });
                this.setupAudio(charId, chapId);
            } else if (res.result && res.result.needPurchase) {
                // 需要购买章节
                this.showPurchaseDialog(charId, chapId, res.result.cost);
            } else {
                this.showErrorAndBack(res.result?.error || '故事加载失败');
                console.error('获取章节内容失败:', res.result);
            }
        }).catch(err => {
            wx.hideLoading();
            this.showErrorAndBack('网络错误');
            console.error('调用云函数失败:', err);
        });
    },

    // 显示购买对话框
    showPurchaseDialog: function (charId, chapId, cost) {
        wx.showModal({
            title: '章节未解锁',
            content: `需要消耗 ${cost} 个光点解锁此章节，是否继续？`,
            success: (res) => {
                if (res.confirm) {
                    this.purchaseChapter(charId, chapId, cost);
                } else {
                    wx.navigateBack();
                }
            }
        });
    },

    // 购买章节
    purchaseChapter: function (charId, chapId, cost) {
        wx.showLoading({ title: '购买中...' });

        wx.cloud.callFunction({
            name: 'userPurchase',
            data: {
                action: 'purchaseChapter',
                charId: charId,
                chapId: chapId,
                cost: cost
            }
        }).then(res => {
            wx.hideLoading();
            if (res.result.success) {
                wx.showToast({
                    title: '解锁成功！',
                    icon: 'success'
                });

                // 刷新光点余额显示（如果页面有光点组件）
                this.refreshUserPoints();

                // 重新加载故事内容
                setTimeout(() => {
                    this.loadStory(charId, chapId);
                }, 1000);
            } else {
                wx.showModal({
                    title: '购买失败',
                    content: res.result.error || '未知错误',
                    showCancel: false,
                    success: () => {
                        wx.navigateBack();
                    }
                });
            }
        }).catch(err => {
            wx.hideLoading();
            wx.showModal({
                title: '网络错误',
                content: '请检查网络连接后重试',
                showCancel: false,
                success: () => {
                    wx.navigateBack();
                }
            });
            console.error('购买章节失败:', err);
        });
    },

    // 刷新用户光点余额
    refreshUserPoints: function () {
        const userPointsComponent = this.selectComponent('.nav-points');
        if (userPointsComponent) {
            userPointsComponent.refreshPoints();
        }
    },

    showErrorAndBack: function (title) {
        wx.showToast({ title: title, icon: 'error', duration: 2000 });
        setTimeout(() => wx.navigateBack(), 2000);
    },

    setupAudio: function (charId, chapId) {
        if (!backgroundAudioManager) {
            console.error('后台音频管理器未初始化');
            return;
        }

        // 停止当前播放
        try {
            backgroundAudioManager.stop();
        } catch (e) {
            console.log('停止音频时出错，可能没有正在播放的音频');
        }

        this.setData({ isPlaying: false, progress: 0, currentTime: '00:00' });

        // 使用故事数据中的音频URL
        if (this.data.story && this.data.story.audioUrl) {
            console.log('设置音频源:', this.data.story.audioUrl);

            try {
                // 设置完整的音频信息，包括播放列表信息
                backgroundAudioManager.title = this.data.story.title || '微光故事';
                backgroundAudioManager.epname = this.data.story.title || '微光故事';
                backgroundAudioManager.singer = '微光';
                backgroundAudioManager.coverImgUrl = 'cloud://cloudbase-8gji862jcfb501e7.636c-cloudbase-8gji862jcfb501e7-1365531166/images/IconWeiguang.png';

                // 设置播放列表信息，这对后台播放很重要
                backgroundAudioManager.webUrl = `https://weiguang.app/story/${charId}/${chapId}`;

                // 预先获取下一章节信息，为自动播放做准备
                this.prepareNextChapter(charId, chapId);

                // 设置音频源
                backgroundAudioManager.src = this.data.story.audioUrl;

                console.log('后台音频设置完成，title:', backgroundAudioManager.title);
                console.log('音频源:', backgroundAudioManager.src);
            } catch (error) {
                console.error('设置后台音频时出错:', error);
                wx.showToast({
                    title: '音频设置失败',
                    icon: 'none'
                });
                return;
            }
        } else {
            console.warn('未找到音频URL');
            return;
        }

        // 不在这里绑定事件监听器，避免重复绑定
    },

    // 预先准备下一章节信息
    prepareNextChapter: function (charId, chapId) {
        const nextChapId = chapId + 1;

        // 检查下一章节权限
        wx.cloud.callFunction({
            name: 'userPurchase',
            data: {
                action: 'checkChapterAccess',
                charId: charId,
                chapId: nextChapId
            }
        }).then(res => {
            if (res.result && res.result.success && res.result.hasAccess) {
                console.log('下一章节可访问，准备自动播放信息');

                // 获取下一章节的音频信息
                wx.cloud.callFunction({
                    name: 'getStoryData',
                    data: {
                        type: 'getChapterContent',
                        charId: charId,
                        chapId: nextChapId
                    }
                }).then(nextRes => {
                    if (nextRes.result && nextRes.result.success) {
                        const nextStory = nextRes.result.data;

                        // 存储下一章节信息，供后台播放使用
                        this.nextChapterInfo = {
                            charId: charId,
                            chapId: nextChapId,
                            title: nextStory.title,
                            audioUrl: nextStory.audioUrl
                        };

                        console.log('下一章节信息已准备:', this.nextChapterInfo);
                    }
                });
            } else {
                console.log('下一章节不可访问或需要购买');
                this.nextChapterInfo = null;
            }
        }).catch(err => {
            console.error('检查下一章节权限失败:', err);
            this.nextChapterInfo = null;
        });
    },

    // 绑定音频事件监听器
    bindAudioEvents: function () {
        if (!backgroundAudioManager) return;

        const self = this;

        // 监听后台音频播放事件
        backgroundAudioManager.onPlay(() => {
            console.log('音频开始播放');
            if (self.data) {
                self.setData({ isPlaying: true });
            }
        });

        backgroundAudioManager.onPause(() => {
            console.log('音频暂停');
            if (self.data) {
                self.setData({ isPlaying: false });
            }
        });

        backgroundAudioManager.onStop(() => {
            console.log('音频停止');
            if (self.data) {
                self.setData({ isPlaying: false, progress: 0, currentTime: '00:00', progressStyle: 'width: 0%' });
            }
        });

        // 使用后台音频管理器的内置功能来处理自动播放
        backgroundAudioManager.onEnded(() => {
            console.log('音频播放结束');
            if (self.data) {
                self.setData({ isPlaying: false, progress: 0, currentTime: '00:00', progressStyle: 'width: 0%' });
                if (self.data.timerId) {
                    clearTimeout(self.data.timerId);
                    self.setData({ timerId: null });
                }

                // 如果有预准备的下一章节信息，直接播放
                if (self.nextChapterInfo) {
                    console.log('使用预准备的下一章节信息直接播放');
                    self.playNextChapterDirectly();
                } else {
                    // 备用方案：传统的页面跳转方式
                    console.log('使用传统方式检查下一章节');
                    self.checkAndPlayNextChapter();
                }
            }
        });

        backgroundAudioManager.onTimeUpdate(() => {
            if (backgroundAudioManager.duration > 0 && self.data) {
                const progress = (backgroundAudioManager.currentTime / backgroundAudioManager.duration) * 100;
                self.setData({
                    progress: progress,
                    currentTime: self.formatTime(backgroundAudioManager.currentTime),
                    duration: self.formatTime(backgroundAudioManager.duration),
                    progressStyle: `width: ${progress}%`
                });
            }
        });

        backgroundAudioManager.onError((res) => {
            console.error('音频播放错误:', res);
            wx.showToast({
                title: '音频播放失败',
                icon: 'none'
            });
        });

        backgroundAudioManager.onCanplay(() => {
            console.log('音频可以播放');
        });

        backgroundAudioManager.onWaiting(() => {
            console.log('音频缓冲中');
        });

        // 添加关键的后台播放事件监听
        backgroundAudioManager.onNext(() => {
            console.log('用户点击下一首');
        });

        backgroundAudioManager.onPrev(() => {
            console.log('用户点击上一首');
        });
    },

    formatTime: function (sec) {
        if (isNaN(sec)) return '00:00';
        const minute = Math.floor(sec / 60);
        const second = Math.floor(sec % 60);
        return `${minute < 10 ? '0' + minute : minute}:${second < 10 ? '0' + second : second}`;
    },

    togglePlay: function () {
        if (!backgroundAudioManager) {
            console.error('后台音频管理器未初始化');
            return;
        }

        console.log('切换播放状态，当前状态:', this.data.isPlaying);
        console.log('后台音频管理器状态:', {
            paused: backgroundAudioManager.paused,
            src: backgroundAudioManager.src,
            title: backgroundAudioManager.title
        });

        try {
            if (this.data.isPlaying) {
                backgroundAudioManager.pause();
                console.log('暂停播放');
            } else {
                // 确保音频源已设置
                if (!backgroundAudioManager.src) {
                    console.error('音频源未设置');
                    wx.showToast({
                        title: '音频未准备好',
                        icon: 'none'
                    });
                    return;
                }

                backgroundAudioManager.play();
                console.log('开始播放');
            }
        } catch (error) {
            console.error('播放控制出错:', error);
            wx.showToast({
                title: '播放控制失败',
                icon: 'none'
            });
        }
        // 播放状态会通过事件监听器自动更新，不需要手动设置
    },

    toggleNav: function () {
        this.setData({ navVisible: !this.data.navVisible });
    },

    noop: function () { },

    toggleTimerSheet: function () {
        this.setData({ showTimerSheet: !this.data.showTimerSheet });
    },

    setTimer: function (e) {
        const type = e.currentTarget.dataset.type;
        if (this.data.timerId) {
            clearTimeout(this.data.timerId);
        }
        let duration = 0;
        let toastTitle = '';

        if (type === '15min') {
            duration = 15 * 60 * 1000;
            toastTitle = '已开启15分钟后停止';
        } else if (type === '30min') {
            duration = 30 * 60 * 1000;
            toastTitle = '已开启30分钟后停止';
        } else if (type === 'chapter') {
            toastTitle = '本章结束后停止';
            this.setData({ timerId: 'chapter_end' });
            this.toggleTimerSheet();
            wx.showToast({ title: toastTitle, icon: 'none' });
            return;
        } else {
            this.setData({ timerId: null });
            toastTitle = '已关闭定时';
            this.toggleTimerSheet();
            wx.showToast({ title: toastTitle, icon: 'none' });
            return;
        }

        const timer = setTimeout(() => {
            backgroundAudioManager.pause();
            this.setData({ isPlaying: false });
        }, duration);

        this.setData({ timerId: timer });
        this.toggleTimerSheet();
        wx.showToast({ title: toastTitle, icon: 'none' });
    },

    onUnload: function () {
        // 页面卸载时不要停止后台音频播放，让它继续在后台播放
        console.log('页面卸载，但保持音频后台播放');
        if (this.data.timerId && this.data.timerId !== 'chapter_end') {
            clearTimeout(this.data.timerId);
        }

        // 清理音频监控定时器
        if (this.audioMonitorTimer) {
            clearInterval(this.audioMonitorTimer);
            this.audioMonitorTimer = null;
        }
    },

    navigateBack: function () {
        wx.navigateBack();
    },

    // 章节切换
    prevChapter: function () {
        if (this.data.isFirstChapter) return;
        const prevChapId = this.data.currentChapId - 1;
        wx.redirectTo({
            url: `/pages/journal/reader/index?charId=${this.data.currentCharId}&chapId=${prevChapId}`
        });
    },

    nextChapter: function () {
        if (this.data.isLastChapter) return;
        const nextChapId = this.data.currentChapId + 1;
        wx.redirectTo({
            url: `/pages/journal/reader/index?charId=${this.data.currentCharId}&chapId=${nextChapId}`
        });
    },

    // 检查并自动播放下一章节 - 使用强制跳转确保息屏状态下也能工作
    checkAndPlayNextChapter: function () {
        console.log('检查是否可以自动播放下一章节');

        // 如果已经是最后一章，不自动跳转
        if (this.data.isLastChapter) {
            console.log('已经是最后一章，不自动跳转');
            wx.showToast({
                title: '故事播放完毕',
                icon: 'success'
            });
            return;
        }

        const nextChapId = this.data.currentChapId + 1;
        const charId = this.data.currentCharId;

        console.log(`检查下一章节权限: ${charId} - 第${nextChapId}章`);

        // 调用云函数检查下一章节的访问权限
        wx.cloud.callFunction({
            name: 'userPurchase',
            data: {
                action: 'checkChapterAccess',
                charId: charId,
                chapId: nextChapId
            }
        }).then(res => {
            if (res.result && res.result.success) {
                const hasAccess = res.result.hasAccess;
                const reason = res.result.reason;

                console.log(`下一章节访问权限检查结果: hasAccess=${hasAccess}, reason=${reason}`);

                if (hasAccess) {
                    // 有访问权限，立即跳转（不显示提示，避免息屏状态下的UI问题）
                    console.log(`立即跳转到下一章节: 第${nextChapId}章`);

                    // 使用 wx.reLaunch 并且不使用延时，确保在息屏状态下也能立即执行
                    try {
                        wx.reLaunch({
                            url: `/pages/journal/reader/index?charId=${charId}&chapId=${nextChapId}`,
                            success: () => {
                                console.log('页面跳转成功');
                            },
                            fail: (error) => {
                                console.error('页面跳转失败:', error);
                                // 如果 reLaunch 失败，尝试使用 navigateTo
                                wx.navigateTo({
                                    url: `/pages/journal/reader/index?charId=${charId}&chapId=${nextChapId}`
                                });
                            }
                        });
                    } catch (error) {
                        console.error('跳转页面时出错:', error);
                    }
                } else {
                    // 没有访问权限，只在亮屏状态下显示提示
                    if (reason === 'locked') {
                        // 检查小程序是否在前台
                        const pages = getCurrentPages();
                        if (pages && pages.length > 0) {
                            wx.showModal({
                                title: '下一章节未解锁',
                                content: `第${nextChapId}章需要${res.result.cost || 2}个光点解锁，是否现在购买？`,
                                success: (modalRes) => {
                                    if (modalRes.confirm) {
                                        // 用户选择购买，跳转到章节列表页面
                                        wx.navigateTo({
                                            url: `/pages/journal/chapter/index?id=${charId}`
                                        });
                                    }
                                }
                            });
                        } else {
                            console.log('小程序在后台，跳过购买提示');
                        }
                    } else {
                        console.log('下一章节无法访问，原因:', reason);
                    }
                }
            } else {
                console.error('检查章节访问权限失败:', res.result);
            }
        }).catch(err => {
            console.error('检查章节访问权限出错:', err);
        });
    },

    // 进度条点击事件
    onProgressTap: function (e) {
        if (!backgroundAudioManager || !backgroundAudioManager.duration) {
            return;
        }

        const query = wx.createSelectorQuery();
        query.select('.progress-track').boundingClientRect();
        query.exec((res) => {
            if (res[0]) {
                const rect = res[0];
                const clickX = e.detail.x;
                const trackLeft = rect.left;
                const trackWidth = rect.width;
                const clickPosition = (clickX - trackLeft) / trackWidth;
                const newTime = clickPosition * backgroundAudioManager.duration;

                console.log('进度条点击:', {
                    clickX,
                    trackLeft,
                    trackWidth,
                    clickPosition,
                    newTime
                });

                this.seekToTime(newTime);
            }
        });
    },

    // 进度条拖动开始
    onProgressStart: function (e) {
        this.setData({
            isDragging: true
        });
        console.log('开始拖动进度条');
    },

    // 进度条拖动中
    onProgressMove: function (e) {
        if (!this.data.isDragging || !backgroundAudioManager || !backgroundAudioManager.duration) {
            return;
        }

        const query = wx.createSelectorQuery();
        query.select('.progress-track').boundingClientRect();
        query.exec((res) => {
            if (res[0]) {
                const rect = res[0];
                const touchX = e.touches[0].clientX;
                const trackLeft = rect.left;
                const trackWidth = rect.width;
                let dragPosition = (touchX - trackLeft) / trackWidth;

                // 限制拖动范围在0-1之间
                dragPosition = Math.max(0, Math.min(1, dragPosition));

                const newProgress = dragPosition * 100;
                const newTime = dragPosition * backgroundAudioManager.duration;

                // 更新UI显示，但不立即跳转播放位置
                this.setData({
                    progress: newProgress,
                    progressStyle: `width: ${newProgress}%`,
                    currentTime: this.formatTime(newTime)
                });
            }
        });
    },

    // 进度条拖动结束
    onProgressEnd: function (e) {
        if (!this.data.isDragging) {
            return;
        }

        this.setData({
            isDragging: false
        });

        if (!backgroundAudioManager || !backgroundAudioManager.duration) {
            return;
        }

        const query = wx.createSelectorQuery();
        query.select('.progress-track').boundingClientRect();
        query.exec((res) => {
            if (res[0]) {
                const rect = res[0];
                const touchX = e.changedTouches[0].clientX;
                const trackLeft = rect.left;
                const trackWidth = rect.width;
                let dragPosition = (touchX - trackLeft) / trackWidth;

                // 限制拖动范围在0-1之间
                dragPosition = Math.max(0, Math.min(1, dragPosition));

                const newTime = dragPosition * backgroundAudioManager.duration;

                console.log('拖动结束，跳转到:', newTime);
                this.seekToTime(newTime);
            }
        });
    },

    // 跳转到指定时间
    seekToTime: function (time) {
        if (!backgroundAudioManager) {
            return;
        }

        try {
            backgroundAudioManager.seek(time);
            console.log('音频跳转到:', time);
        } catch (error) {
            console.error('音频跳转失败:', error);
            wx.showToast({
                title: '跳转失败',
                icon: 'none'
            });
        }
    },

    // 直接播放下一章节（不跳转页面，适用于息屏状态）
    playNextChapterDirectly: function () {
        if (!this.nextChapterInfo || !backgroundAudioManager) {
            console.log('没有下一章节信息或音频管理器未初始化');
            return;
        }

        console.log('直接播放下一章节:', this.nextChapterInfo);

        try {
            // 更新页面数据
            this.setData({
                currentChapId: this.nextChapterInfo.chapId,
                isFirstChapter: this.nextChapterInfo.chapId === 1,
                isLastChapter: this.nextChapterInfo.chapId >= 10
            });

            // 直接在后台音频管理器中切换音频源
            backgroundAudioManager.title = this.nextChapterInfo.title;
            backgroundAudioManager.epname = this.nextChapterInfo.title;
            backgroundAudioManager.singer = '微光';
            backgroundAudioManager.coverImgUrl = 'cloud://cloudbase-8gji862jcfb501e7.636c-cloudbase-8gji862jcfb501e7-1365531166/images/IconWeiguang.png';
            backgroundAudioManager.webUrl = `https://weiguang.app/story/${this.nextChapterInfo.charId}/${this.nextChapterInfo.chapId}`;

            // 设置新的音频源，这会自动开始播放
            backgroundAudioManager.src = this.nextChapterInfo.audioUrl;

            console.log('已切换到下一章节音频:', this.nextChapterInfo.title);

            // 预先准备再下一章节的信息
            this.prepareNextChapter(this.nextChapterInfo.charId, this.nextChapterInfo.chapId);

            // 获取新章节的完整内容（用于页面显示）
            this.loadStoryContent(this.nextChapterInfo.charId, this.nextChapterInfo.chapId);

        } catch (error) {
            console.error('直接播放下一章节失败:', error);
            // 如果直接播放失败，回退到页面跳转方式
            this.checkAndPlayNextChapter();
        }
    },

    // 加载故事内容（不包含音频设置）
    loadStoryContent: function (charId, chapId) {
        wx.cloud.callFunction({
            name: 'getStoryData',
            data: {
                type: 'getChapterContent',
                charId: charId,
                chapId: chapId
            }
        }).then(res => {
            if (res.result && res.result.success) {
                const storyData = res.result.data;
                storyData.paragraphs = storyData.content.split('\n\n');
                this.setData({
                    story: storyData,
                    currentCharId: charId,
                    currentChapId: chapId
                });
                console.log('章节内容已更新:', storyData.title);
            } else {
                console.error('获取章节内容失败:', res.result);
            }
        }).catch(err => {
            console.error('调用云函数失败:', err);
        });
    }
});