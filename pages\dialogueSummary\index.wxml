<view class="dialogue-summary-container">
  <view class="header">
    <text class="theme">{{dialogueTheme}} - 对话总结</text>
  </view>

  <view class="summary-section">
    <text class="section-title">对话总结</text>
    <text class="summary-text">{{aiSummary}}</text>
  </view>

  <view class="three-questions-section">
    <text class="section-title">三问仪式</text>
    <block wx:for="{{threeQuestions}}" wx:key="index">
      <view class="question-answer-item">
        <text class="question">{{item}}</text>
        <text class="answer">{{threeAnswers[index]}}</text>
      </view>
    </block>
  </view>

  <view class="emotion-section">
    <text class="section-title">情感关键词</text>
    <text class="emotion-word">{{emotionWord}}</text>
  </view>

  <view class="tomorrow-message-section">
    <text class="section-title">明日寄语</text>
    <text class="tomorrow-message">{{tomorrowMessage}}</text>
  </view>

  <view class="action-buttons">
    <button class="home-btn" bindtap="backToHome">返回首页</button>
  </view>
</view> 