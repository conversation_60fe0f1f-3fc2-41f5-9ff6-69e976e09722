page {
  background-color: #000;
}

.container {
  width: 100%;
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
}

/* 背景层 */
.background-image,
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}
.background-image {
  opacity: 0.5;
}
.overlay {
  background-color: rgba(0, 0, 0, 0.4);
}

/* 角色展示层 */
.character-bg-image {
  position: fixed;
  right: -150rpx;
  top: 180rpx; /* 【重要修改】下移图片，避免被遮挡 */
  width: 600rpx;
  height: 600rpx;
  opacity: 0.3; /* 【重要修改】稍微调亮一点，让角色更清晰 */
  z-index: 2;
  transition: transform 0.1s linear;
}

/* 内容层 */
.content-wrapper {
  position: relative;
  z-index: 3;
  width: 100%;
}

/* 自定义导航栏 */
.custom-nav {
  width: 100%;
  display: flex;
  align-items: center;
  position: sticky;
  top: 0;
  /* 【重要修改】移除背景和模糊效果，让它变透明 */
  background-color: transparent;
}
.nav-back {
  position: absolute;
  left: 20rpx;
  bottom: 0;
  height: 100%;
  width: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 22rpx;
  height: 22rpx;
  border: solid #e0e0e0;
  border-width: 0 0 4rpx 4rpx;
  transform: rotate(45deg);
}

/* 角色信息区 */
.character-info-container {
  padding: 40rpx 50rpx;
  color: #f0e6d2;
}
.character-name {
  font-size: 64rpx;
  font-weight: bold;
  letter-spacing: 4rpx;
}
.character-tagline {
  font-size: 30rpx;
  font-weight: 300;
  opacity: 0.8;
  margin-top: 10rpx;
}
.divider {
  width: 80rpx;
  height: 3rpx;
  background-color: rgba(255, 215, 0, 0.5);
  margin: 30rpx 0;
}
.character-description {
  font-size: 26rpx;
  line-height: 1.6;
  opacity: 0.7;
  background-color: rgba(0, 0, 0, 0.3);
  padding: 20rpx;
  border-radius: 12rpx;
  border-left: 3rpx solid rgba(255, 215, 0, 0.5);
}

/* 章节列表 */
.chapter-list {
  padding: 20rpx 40rpx;
  background-color: rgba(10, 10, 10, 0.4);
  border-top-left-radius: 40rpx;
  border-top-right-radius: 40rpx;
}
.chapter-card {
  display: flex;
  align-items: center;
  padding: 40rpx 20rpx;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  transition: background-color 0.2s;
}
.chapter-card:active {
  background-color: rgba(255, 255, 255, 0.05);
}
.chapter-index {
  font-size: 40rpx;
  font-weight: bold;
  color: rgba(255, 215, 0, 0.6);
  width: 80rpx;
}
.chapter-title {
  flex: 1;
  font-size: 32rpx;
  color: #eaeaea;
}
.chapter-title.locked {
  color: #888;
}
.status-tag {
  display: flex;
  align-items: center;
}
.tag-free {
  font-size: 24rpx;
  color: #ffd700;
  border: 1px solid rgba(255, 215, 0, 0.5);
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
}
.tag-unlocked {
  font-size: 24rpx;
  color: #4caf50;
  border: 1px solid rgba(76, 175, 80, 0.5);
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  background-color: rgba(76, 175, 80, 0.1);
}
.tag-locked {
  display: flex;
  align-items: center;
  color: #888;
  font-size: 28rpx;
}

/* 用CSS画的光点图标 */
.light-dot-icon {
  width: 30rpx;
  height: 30rpx;
  background-color: #ffd700;
  border-radius: 50%;
  margin-right: 10rpx;
  box-shadow: 0 0 10rpx rgba(255, 215, 0, 0.7);
}
.light-dot-icon.inline {
  display: inline-block;
  vertical-align: middle;
  margin: 0 8rpx;
}

/* 解锁弹窗样式 */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 99;
}
.unlock-modal {
  position: fixed;
  bottom: -100%; /* 初始位置在屏幕外 */
  left: 0;
  width: 100%;
  background-color: #2c2c2c;
  border-top-left-radius: 40rpx;
  border-top-right-radius: 40rpx;
  padding: 40rpx;
  box-sizing: border-box;
  z-index: 100;
  transition: bottom 0.3s ease-out;
}
.unlock-modal.show {
  bottom: 0; /* 显示时滑入 */
}
.modal-title {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  color: #eaeaea;
  margin-bottom: 30rpx;
}
.modal-content {
  font-size: 30rpx;
  color: #bbb;
  line-height: 1.6;
  text-align: center;
  margin-bottom: 40rpx;
}
.modal-content .highlight {
  color: #ffd700;
  font-weight: bold;
}
.modal-actions {
  display: flex;
  justify-content: space-between;
}
.modal-actions button {
  width: 48%;
  border-radius: 16rpx;
  font-size: 32rpx;
}
.btn-cancel {
  background-color: #555;
  color: #fff;
}
.btn-confirm {
  background-color: #ffd700;
  color: #333;
}
/* 导航栏光点组件样式 */
.nav-points {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
}
