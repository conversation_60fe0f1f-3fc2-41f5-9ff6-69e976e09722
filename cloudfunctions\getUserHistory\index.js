const cloud = require('wx-server-sdk')

cloud.init({
    env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
    const { openid, limit = 10 } = event

    if (!openid) {
        return {
            success: false,
            error: '缺少用户标识'
        }
    }

    try {
        // 获取用户最近的对话记录
        const result = await db.collection('three_questions_records')
            .where({
                openid: openid
            })
            .orderBy('createTime', 'desc')
            .limit(limit)
            .get()

        // 格式化历史记录，提取关键信息
        const formattedHistory = result.data.map(record => {
            return {
                theme: record.theme || '未知主题',
                summary: record.summary || '',
                questions: record.questions || [],
                answers: record.answers || [],
                tomorrowMessage: record.tomorrowMessage || '',
                dialogueContent: record.dialogueContent || [], // 添加完整的对话内容
                emotionKeyword: record.emotionKeyword || '', // 添加情感关键词
                createTime: record.createTime || record.updateTime, // 兼容更新时间
                // 提取用户的核心关注点和情感状态
                userConcerns: extractUserConcerns(record),
                emotionalState: extractEmotionalState(record),
                // 星座位置数据
                starPosition: record.starPosition || null, // 星星在星座中的位置信息
                constellationData: record.constellationData || null // 完整的星座数据
            }
        })

        return {
            success: true,
            history: formattedHistory,
            totalRecords: result.data.length
        }

    } catch (error) {
        console.error('获取用户历史记录错误:', error)
        return {
            success: false,
            error: error.message
        }
    }
}

// 提取用户关注点的辅助函数
function extractUserConcerns(record) {
    const concerns = []

    // 从主题中提取关注点
    if (record.theme) {
        concerns.push(`主题关注: ${record.theme}`)
    }

    // 从问答中提取关键词
    if (record.answers && record.answers.length > 0) {
        const keyWords = record.answers.join(' ').match(/工作|情感|家庭|成长|压力|焦虑|迷茫|目标|梦想|关系/g)
        if (keyWords) {
            concerns.push(`关键词: ${[...new Set(keyWords)].join(', ')}`)
        }
    }

    return concerns
}

// 提取情感状态的辅助函数
function extractEmotionalState(record) {
    const states = []

    // 从总结中分析情感倾向
    if (record.summary) {
        const positiveWords = record.summary.match(/积极|乐观|希望|成长|进步|坚强|勇敢|自信/g)
        const negativeWords = record.summary.match(/困惑|迷茫|焦虑|压力|困难|挫折|失落|疲惫/g)

        if (positiveWords && positiveWords.length > negativeWords?.length) {
            states.push('积极向上')
        } else if (negativeWords && negativeWords.length > 0) {
            states.push('需要支持')
        }
    }

    return states
}