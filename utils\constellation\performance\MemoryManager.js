/**
 * 内存管理器
 * 负责管理星座系统的内存使用和垃圾回收
 */

class MemoryManager {
    constructor() {
        this.memoryPools = {
            stars: new Map(),
            animations: new Map(),
            textures: new Map(),
            geometries: new Map()
        }

        this.cacheConfig = {
            maxStarCache: 200,
            maxAnimationCache: 50,
            maxTextureCache: 20,
            cacheTimeout: 5 * 60 * 1000 // 5分钟
        }

        this.memoryStats = {
            totalAllocated: 0,
            totalFreed: 0,
            currentUsage: 0,
            peakUsage: 0,
            gcCount: 0
        }

        this.cleanupInterval = null
        this.isMonitoring = false
    }

    /**
     * 初始化内存管理器
     */
    initialize() {
        console.log('初始化内存管理器...')
        
        // 启动定期清理
        this.startPeriodicCleanup()
        
        // 监听内存警告
        this.setupMemoryWarningListener()
        
        return { success: true }
    }

    /**
     * 启动定期清理
     */
    startPeriodicCleanup() {
        if (this.cleanupInterval) return

        // 每30秒执行一次清理
        this.cleanupInterval = setInterval(() => {
            this.performGarbageCollection()
        }, 30000)

        console.log('定期内存清理已启动')
    }

    /**
     * 停止定期清理
     */
    stopPeriodicCleanup() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval)
            this.cleanupInterval = null
        }
        console.log('定期内存清理已停止')
    }

    /**
     * 设置内存警告监听器
     */
    setupMemoryWarningListener() {
        try {
            wx.onMemoryWarning && wx.onMemoryWarning(() => {
                console.warn('收到内存警告，执行紧急清理')
                this.performEmergencyCleanup()
            })
        } catch (error) {
            console.error('设置内存警告监听器失败:', error)
        }
    }

    /**
     * 分配星星对象
     */
    allocateStar(starData) {
        const starId = starData.id || `star_${Date.now()}_${Math.random()}`
        
        // 检查是否可以从缓存中复用
        const cachedStar = this.getFromCache('stars', starId)
        if (cachedStar) {
            console.log('从缓存中复用星星对象:', starId)
            this.updateStarData(cachedStar, starData)
            return cachedStar
        }

        // 创建新的星星对象
        const star = this.createStarObject(starData)
        
        // 添加到内存池
        this.memoryPools.stars.set(starId, {
            object: star,
            createdAt: Date.now(),
            lastUsed: Date.now(),
            refCount: 1
        })

        this.updateMemoryStats('allocate', this.estimateStarSize(star))
        
        console.log('分配新星星对象:', starId)
        return star
    }

    /**
     * 创建星星对象
     */
    createStarObject(starData) {
        return {
            id: starData.id,
            theme: starData.theme,
            emotionKeyword: starData.emotionKeyword,
            position: starData.position || { x: 0, y: 0 },
            style: starData.style || {},
            animations: [],
            effects: [],
            metadata: {
                createdAt: Date.now(),
                version: '1.0'
            }
        }
    }

    /**
     * 更新星星数据
     */
    updateStarData(star, newData) {
        Object.assign(star, newData)
        star.metadata.updatedAt = Date.now()
        
        // 更新最后使用时间
        const poolEntry = this.memoryPools.stars.get(star.id)
        if (poolEntry) {
            poolEntry.lastUsed = Date.now()
        }
    }

    /**
     * 释放星星对象
     */
    releaseStar(starId) {
        const poolEntry = this.memoryPools.stars.get(starId)
        if (!poolEntry) return false

        poolEntry.refCount--
        
        if (poolEntry.refCount <= 0) {
            // 清理星星相关的动画和效果
            this.cleanupStarResources(poolEntry.object)
            
            // 从内存池中移除
            this.memoryPools.stars.delete(starId)
            
            this.updateMemoryStats('free', this.estimateStarSize(poolEntry.object))
            
            console.log('释放星星对象:', starId)
            return true
        }

        return false
    }

    /**
     * 清理星星相关资源
     */
    cleanupStarResources(star) {
        // 停止所有动画
        if (star.animations) {
            star.animations.forEach(animation => {
                if (animation.stop) {
                    animation.stop()
                }
            })
            star.animations = []
        }

        // 清理视觉效果
        if (star.effects) {
            star.effects.forEach(effect => {
                if (effect.destroy) {
                    effect.destroy()
                }
            })
            star.effects = []
        }
    }

    /**
     * 分配动画对象
     */
    allocateAnimation(animationData) {
        const animationId = animationData.id || `anim_${Date.now()}_${Math.random()}`
        
        const animation = {
            id: animationId,
            type: animationData.type,
            duration: animationData.duration,
            startTime: Date.now(),
            properties: animationData.properties || {},
            callbacks: animationData.callbacks || {}
        }

        this.memoryPools.animations.set(animationId, {
            object: animation,
            createdAt: Date.now(),
            lastUsed: Date.now(),
            refCount: 1
        })

        this.updateMemoryStats('allocate', this.estimateAnimationSize(animation))
        
        console.log('分配动画对象:', animationId)
        return animation
    }

    /**
     * 释放动画对象
     */
    releaseAnimation(animationId) {
        const poolEntry = this.memoryPools.animations.get(animationId)
        if (!poolEntry) return false

        poolEntry.refCount--
        
        if (poolEntry.refCount <= 0) {
            this.memoryPools.animations.delete(animationId)
            this.updateMemoryStats('free', this.estimateAnimationSize(poolEntry.object))
            
            console.log('释放动画对象:', animationId)
            return true
        }

        return false
    }

    /**
     * 从缓存中获取对象
     */
    getFromCache(poolName, objectId) {
        const pool = this.memoryPools[poolName]
        if (!pool) return null

        const entry = pool.get(objectId)
        if (!entry) return null

        // 检查是否过期
        const now = Date.now()
        if (now - entry.lastUsed > this.cacheConfig.cacheTimeout) {
            pool.delete(objectId)
            return null
        }

        entry.lastUsed = now
        entry.refCount++
        
        return entry.object
    }

    /**
     * 执行垃圾回收
     */
    performGarbageCollection() {
        console.log('执行垃圾回收...')
        
        const now = Date.now()
        let cleanedCount = 0

        // 清理过期的星星对象
        for (const [starId, entry] of this.memoryPools.stars) {
            if (entry.refCount <= 0 && now - entry.lastUsed > this.cacheConfig.cacheTimeout) {
                this.cleanupStarResources(entry.object)
                this.memoryPools.stars.delete(starId)
                this.updateMemoryStats('free', this.estimateStarSize(entry.object))
                cleanedCount++
            }
        }

        // 清理过期的动画对象
        for (const [animId, entry] of this.memoryPools.animations) {
            if (entry.refCount <= 0 && now - entry.lastUsed > this.cacheConfig.cacheTimeout) {
                this.memoryPools.animations.delete(animId)
                this.updateMemoryStats('free', this.estimateAnimationSize(entry.object))
                cleanedCount++
            }
        }

        // 清理纹理缓存
        this.cleanupTextureCache()

        this.memoryStats.gcCount++
        
        if (cleanedCount > 0) {
            console.log(`垃圾回收完成，清理了 ${cleanedCount} 个对象`)
        }
    }

    /**
     * 执行紧急清理
     */
    performEmergencyCleanup() {
        console.log('执行紧急内存清理...')
        
        // 清理所有未使用的对象
        let cleanedCount = 0

        // 强制清理星星对象
        for (const [starId, entry] of this.memoryPools.stars) {
            if (entry.refCount <= 0) {
                this.cleanupStarResources(entry.object)
                this.memoryPools.stars.delete(starId)
                cleanedCount++
            }
        }

        // 强制清理动画对象
        for (const [animId, entry] of this.memoryPools.animations) {
            if (entry.refCount <= 0) {
                this.memoryPools.animations.delete(animId)
                cleanedCount++
            }
        }

        // 清空所有缓存
        this.memoryPools.textures.clear()
        this.memoryPools.geometries.clear()

        console.log(`紧急清理完成，清理了 ${cleanedCount} 个对象`)
    }

    /**
     * 清理纹理缓存
     */
    cleanupTextureCache() {
        const texturePool = this.memoryPools.textures
        const maxSize = this.cacheConfig.maxTextureCache

        if (texturePool.size <= maxSize) return

        // 按最后使用时间排序，删除最旧的纹理
        const entries = Array.from(texturePool.entries())
        entries.sort((a, b) => a[1].lastUsed - b[1].lastUsed)

        const toDelete = entries.slice(0, entries.length - maxSize)
        toDelete.forEach(([textureId]) => {
            texturePool.delete(textureId)
        })

        console.log(`清理了 ${toDelete.length} 个纹理缓存`)
    }

    /**
     * 估算星星对象大小
     */
    estimateStarSize(star) {
        // 简化的大小估算
        return JSON.stringify(star).length * 2 // 假设每个字符2字节
    }

    /**
     * 估算动画对象大小
     */
    estimateAnimationSize(animation) {
        return JSON.stringify(animation).length * 2
    }

    /**
     * 更新内存统计
     */
    updateMemoryStats(operation, size) {
        if (operation === 'allocate') {
            this.memoryStats.totalAllocated += size
            this.memoryStats.currentUsage += size
            
            if (this.memoryStats.currentUsage > this.memoryStats.peakUsage) {
                this.memoryStats.peakUsage = this.memoryStats.currentUsage
            }
        } else if (operation === 'free') {
            this.memoryStats.totalFreed += size
            this.memoryStats.currentUsage = Math.max(0, this.memoryStats.currentUsage - size)
        }
    }

    /**
     * 获取内存统计信息
     */
    getMemoryStats() {
        return {
            ...this.memoryStats,
            poolSizes: {
                stars: this.memoryPools.stars.size,
                animations: this.memoryPools.animations.size,
                textures: this.memoryPools.textures.size,
                geometries: this.memoryPools.geometries.size
            },
            cacheHitRate: this.calculateCacheHitRate()
        }
    }

    /**
     * 计算缓存命中率
     */
    calculateCacheHitRate() {
        // 简化的缓存命中率计算
        const totalRequests = this.memoryStats.totalAllocated / 1000 // 假设平均对象大小
        const cacheHits = Math.max(0, totalRequests - this.memoryPools.stars.size)
        
        return totalRequests > 0 ? (cacheHits / totalRequests * 100).toFixed(2) + '%' : '0%'
    }

    /**
     * 设置缓存配置
     */
    setCacheConfig(config) {
        Object.assign(this.cacheConfig, config)
        console.log('缓存配置已更新:', this.cacheConfig)
    }

    /**
     * 强制垃圾回收
     */
    forceGarbageCollection() {
        this.performGarbageCollection()
        
        // 如果支持，触发系统垃圾回收
        if (global.gc) {
            global.gc()
        }
    }

    /**
     * 销毁内存管理器
     */
    destroy() {
        this.stopPeriodicCleanup()
        this.performEmergencyCleanup()
        console.log('内存管理器已销毁')
    }
}

// 创建全局实例
const memoryManager = new MemoryManager()

export default memoryManager
