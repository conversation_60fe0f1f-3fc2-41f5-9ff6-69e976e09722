const cloud = require('wx-server-sdk');

cloud.init({
    env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

exports.main = async (event, context) => {
    const { type } = event;

    try {
        switch (type) {
            case 'getUserLevelDistribution':
                return await getUserLevelDistribution();
            case 'getDialogueQualityMetrics':
                return await getDialogueQualityMetrics(event.level, event.startDate, event.endDate);
            case 'getLevelSwitchAnalysis':
                return await getLevelSwitchAnalysis(event.openid, event.startDate, event.endDate);
            case 'detectAnomalousUsage':
                return await detectAnomalousUsage(event.startDate, event.endDate);
            case 'getPerformanceMetrics':
                return await getPerformanceMetrics(event.startDate, event.endDate);
            case 'getDashboardData':
                return await getDashboardData();
            case 'recordDialogueMetrics':
                return await recordDialogueMetrics(event.data);
            case 'recordLevelSwitch':
                return await recordLevelSwitch(event.data);
            case 'recordEndingMessageMetrics':
                return await recordEndingMessageMetrics(event.data);
            case 'recordEndingMessageFeedback':
                return await recordEndingMessageFeedback(event.data);
            case 'getEndingMessageAnalytics':
                return await getEndingMessageAnalytics(event.startDate, event.endDate, event.userLevel);
            case 'getEndingMessageABTestResults':
                return await getEndingMessageABTestResults(event.startDate, event.endDate);
            case 'recordThreeQuestionsMetrics':
                return await recordThreeQuestionsMetrics(event.data);
            case 'getThreeQuestionsAnalytics':
                return await getThreeQuestionsAnalytics(event.startDate, event.endDate, event.userLevel);
            case 'getThreeQuestionsParticipationRate':
                return await getThreeQuestionsParticipationRate(event.startDate, event.endDate);
            case 'getAIDiaryGenerationMetrics':
                return await getAIDiaryGenerationMetrics(event.startDate, event.endDate, event.userLevel);
            case 'getScheduledMessageDeliveryStats':
                return await getScheduledMessageDeliveryStats(event.startDate, event.endDate);
            case 'getUserBehaviorAnalysis':
                return await getUserBehaviorAnalysis(event.openid, event.startDate, event.endDate);
            default:
                return { success: false, error: '未知操作类型' };
        }
    } catch (error) {
        console.error('tieringAnalytics error:', error);
        return { success: false, error: error.message };
    }
};

// 1. 用户层级分布统计功能
async function getUserLevelDistribution() {
    try {
        // 获取所有用户的层级分布
        const users = await db.collection('users')
            .field({
                level: true,
                levelName: true,
                _openid: true
            })
            .get();

        // 统计各层级用户数量
        const distribution = {
            1: { name: '关闭层', count: 0, percentage: 0 },
            2: { name: '徘徊层', count: 0, percentage: 0 },
            3: { name: '挣扎层', count: 0, percentage: 0 },
            4: { name: '主人翁层', count: 0, percentage: 0 },
            5: { name: '创造者层', count: 0, percentage: 0 },
            undefined: { name: '未设置', count: 0, percentage: 0 }
        };

        const totalUsers = users.data.length;

        users.data.forEach(user => {
            const level = user.level || 'undefined';
            if (distribution[level]) {
                distribution[level].count++;
            }
        });

        // 计算百分比
        Object.keys(distribution).forEach(level => {
            distribution[level].percentage = totalUsers > 0
                ? Math.round((distribution[level].count / totalUsers) * 100 * 100) / 100
                : 0;
        });

        return {
            success: true,
            data: {
                totalUsers,
                distribution,
                timestamp: new Date()
            }
        };
    } catch (error) {
        console.error('获取用户层级分布失败:', error);
        return { success: false, error: error.message };
    }
}

// 2. 对话质量监控
async function getDialogueQualityMetrics(level, startDate, endDate) {
    try {
        const start = startDate ? new Date(startDate) : new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        const end = endDate ? new Date(endDate) : new Date();

        // 获取指定层级和时间范围内的对话记录
        let query = db.collection('dialogue_metrics')
            .where({
                createTime: _.gte(start).and(_.lte(end))
            });

        if (level) {
            query = query.where({
                userLevel: level
            });
        }

        const dialogues = await query.get();

        if (dialogues.data.length === 0) {
            return {
                success: true,
                data: {
                    level,
                    period: { start, end },
                    totalDialogues: 0,
                    metrics: {}
                }
            };
        }

        // 计算质量指标
        const metrics = {
            averageResponseTime: 0,
            averageDialogueLength: 0,
            userSatisfactionScore: 0,
            completionRate: 0,
            errorRate: 0
        };

        let totalResponseTime = 0;
        let totalDialogueLength = 0;
        let totalSatisfactionScore = 0;
        let completedDialogues = 0;
        let errorCount = 0;

        dialogues.data.forEach(dialogue => {
            totalResponseTime += dialogue.responseTime || 0;
            totalDialogueLength += dialogue.dialogueLength || 0;
            totalSatisfactionScore += dialogue.satisfactionScore || 0;
            if (dialogue.completed) completedDialogues++;
            if (dialogue.hasError) errorCount++;
        });

        const totalDialogues = dialogues.data.length;
        metrics.averageResponseTime = Math.round(totalResponseTime / totalDialogues);
        metrics.averageDialogueLength = Math.round(totalDialogueLength / totalDialogues);
        metrics.userSatisfactionScore = Math.round((totalSatisfactionScore / totalDialogues) * 100) / 100;
        metrics.completionRate = Math.round((completedDialogues / totalDialogues) * 100 * 100) / 100;
        metrics.errorRate = Math.round((errorCount / totalDialogues) * 100 * 100) / 100;

        return {
            success: true,
            data: {
                level,
                period: { start, end },
                totalDialogues,
                metrics
            }
        };
    } catch (error) {
        console.error('获取对话质量指标失败:', error);
        return { success: false, error: error.message };
    }
}

// 3. 层级切换行为分析
async function getLevelSwitchAnalysis(openid, startDate, endDate) {
    try {
        const start = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        const end = endDate ? new Date(endDate) : new Date();

        let query = db.collection('level_switch_logs')
            .where({
                createTime: _.gte(start).and(_.lte(end))
            })
            .orderBy('createTime', 'desc');

        if (openid) {
            query = query.where({
                _openid: openid
            });
        }

        const switches = await query.get();

        // 分析切换模式
        const analysis = {
            totalSwitches: switches.data.length,
            switchPatterns: {},
            frequentSwitches: [],
            averageTimeInLevel: {},
            switchReasons: {}
        };

        // 统计切换模式
        switches.data.forEach(switchLog => {
            const pattern = `${switchLog.fromLevel}->${switchLog.toLevel}`;
            analysis.switchPatterns[pattern] = (analysis.switchPatterns[pattern] || 0) + 1;

            // 统计切换原因
            if (switchLog.reason) {
                analysis.switchReasons[switchLog.reason] = (analysis.switchReasons[switchLog.reason] || 0) + 1;
            }
        });

        // 找出频繁切换的用户（如果没有指定openid）
        if (!openid) {
            const userSwitchCount = {};
            switches.data.forEach(switchLog => {
                userSwitchCount[switchLog._openid] = (userSwitchCount[switchLog._openid] || 0) + 1;
            });

            analysis.frequentSwitches = Object.entries(userSwitchCount)
                .filter(([_, count]) => count > 5)
                .sort((a, b) => b[1] - a[1])
                .slice(0, 10)
                .map(([openid, count]) => ({ openid, switchCount: count }));
        }

        return {
            success: true,
            data: {
                openid,
                period: { start, end },
                analysis
            }
        };
    } catch (error) {
        console.error('获取层级切换分析失败:', error);
        return { success: false, error: error.message };
    }
}

// 4. 异常层级使用模式检测
async function detectAnomalousUsage(startDate, endDate) {
    try {
        const start = startDate ? new Date(startDate) : new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        const end = endDate ? new Date(endDate) : new Date();

        const anomalies = [];

        // 检测1: 频繁切换层级的用户
        const switchLogs = await db.collection('level_switch_logs')
            .where({
                createTime: _.gte(start).and(_.lte(end))
            })
            .get();

        const userSwitchCount = {};
        switchLogs.data.forEach(log => {
            userSwitchCount[log._openid] = (userSwitchCount[log._openid] || 0) + 1;
        });

        Object.entries(userSwitchCount).forEach(([openid, count]) => {
            if (count > 10) { // 一周内切换超过10次
                anomalies.push({
                    type: 'frequent_level_switching',
                    openid,
                    details: { switchCount: count },
                    severity: count > 20 ? 'high' : 'medium'
                });
            }
        });

        // 检测2: 异常的对话模式
        const dialogueMetrics = await db.collection('dialogue_metrics')
            .where({
                createTime: _.gte(start).and(_.lte(end))
            })
            .get();

        const userDialogueStats = {};
        dialogueMetrics.data.forEach(metric => {
            if (!userDialogueStats[metric._openid]) {
                userDialogueStats[metric._openid] = {
                    totalDialogues: 0,
                    totalErrors: 0,
                    totalResponseTime: 0
                };
            }
            userDialogueStats[metric._openid].totalDialogues++;
            if (metric.hasError) userDialogueStats[metric._openid].totalErrors++;
            userDialogueStats[metric._openid].totalResponseTime += metric.responseTime || 0;
        });

        Object.entries(userDialogueStats).forEach(([openid, stats]) => {
            const errorRate = stats.totalErrors / stats.totalDialogues;
            const avgResponseTime = stats.totalResponseTime / stats.totalDialogues;

            if (errorRate > 0.3) { // 错误率超过30%
                anomalies.push({
                    type: 'high_error_rate',
                    openid,
                    details: { errorRate: Math.round(errorRate * 100) },
                    severity: 'high'
                });
            }

            if (avgResponseTime > 10000) { // 平均响应时间超过10秒
                anomalies.push({
                    type: 'slow_response',
                    openid,
                    details: { avgResponseTime: Math.round(avgResponseTime) },
                    severity: 'medium'
                });
            }
        });

        // 检测3: 层级分布异常
        const levelDistribution = await getUserLevelDistribution();
        if (levelDistribution.success) {
            const dist = levelDistribution.data.distribution;
            // 如果某个层级用户占比超过50%，标记为异常
            Object.entries(dist).forEach(([level, data]) => {
                if (data.percentage > 50 && level !== 'undefined') {
                    anomalies.push({
                        type: 'level_distribution_skew',
                        details: { level, percentage: data.percentage },
                        severity: 'low'
                    });
                }
            });
        }

        return {
            success: true,
            data: {
                period: { start, end },
                anomalies: anomalies.sort((a, b) => {
                    const severityOrder = { high: 3, medium: 2, low: 1 };
                    return severityOrder[b.severity] - severityOrder[a.severity];
                }),
                summary: {
                    total: anomalies.length,
                    high: anomalies.filter(a => a.severity === 'high').length,
                    medium: anomalies.filter(a => a.severity === 'medium').length,
                    low: anomalies.filter(a => a.severity === 'low').length
                }
            }
        };
    } catch (error) {
        console.error('检测异常使用模式失败:', error);
        return { success: false, error: error.message };
    }
}

// 5. 性能监控指标
async function getPerformanceMetrics(startDate, endDate) {
    try {
        const start = startDate ? new Date(startDate) : new Date(Date.now() - 24 * 60 * 60 * 1000);
        const end = endDate ? new Date(endDate) : new Date();

        // 获取系统性能指标
        const performanceData = await db.collection('system_performance')
            .where({
                timestamp: _.gte(start).and(_.lte(end))
            })
            .orderBy('timestamp', 'desc')
            .get();

        if (performanceData.data.length === 0) {
            return {
                success: true,
                data: {
                    period: { start, end },
                    metrics: {
                        averageResponseTime: 0,
                        systemUptime: 100,
                        errorRate: 0,
                        throughput: 0
                    }
                }
            };
        }

        // 计算性能指标
        let totalResponseTime = 0;
        let totalRequests = 0;
        let totalErrors = 0;
        let uptimeSum = 0;

        performanceData.data.forEach(record => {
            totalResponseTime += record.responseTime || 0;
            totalRequests += record.requestCount || 0;
            totalErrors += record.errorCount || 0;
            uptimeSum += record.uptime || 100;
        });

        const recordCount = performanceData.data.length;
        const metrics = {
            averageResponseTime: Math.round(totalResponseTime / recordCount),
            systemUptime: Math.round((uptimeSum / recordCount) * 100) / 100,
            errorRate: totalRequests > 0 ? Math.round((totalErrors / totalRequests) * 100 * 100) / 100 : 0,
            throughput: Math.round(totalRequests / recordCount)
        };

        return {
            success: true,
            data: {
                period: { start, end },
                metrics,
                dataPoints: recordCount
            }
        };
    } catch (error) {
        console.error('获取性能指标失败:', error);
        return { success: false, error: error.message };
    }
}

// 6. 监控仪表板数据
async function getDashboardData() {
    try {
        // 并行获取各种数据
        const [
            levelDistribution,
            recentQualityMetrics,
            recentAnomalies,
            performanceMetrics
        ] = await Promise.all([
            getUserLevelDistribution(),
            getDialogueQualityMetrics(null, new Date(Date.now() - 24 * 60 * 60 * 1000), new Date()),
            detectAnomalousUsage(new Date(Date.now() - 24 * 60 * 60 * 1000), new Date()),
            getPerformanceMetrics(new Date(Date.now() - 24 * 60 * 60 * 1000), new Date())
        ]);

        // 获取今日统计
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);

        const todayDialogues = await db.collection('dialogue_metrics')
            .where({
                createTime: _.gte(today).and(_.lt(tomorrow))
            })
            .count();

        const todaySwitches = await db.collection('level_switch_logs')
            .where({
                createTime: _.gte(today).and(_.lt(tomorrow))
            })
            .count();

        const todayEndingMessages = await db.collection('ending_message_metrics')
            .where({
                createTime: _.gte(today).and(_.lt(tomorrow))
            })
            .count();

        // 获取结束语生成分析数据
        const endingMessageAnalytics = await getEndingMessageAnalytics(
            new Date(Date.now() - 24 * 60 * 60 * 1000),
            new Date()
        );

        // 获取三问仪式分析数据
        const threeQuestionsAnalytics = await getThreeQuestionsAnalytics(
            new Date(Date.now() - 24 * 60 * 60 * 1000),
            new Date()
        );

        // 获取三问参与度数据
        const threeQuestionsParticipation = await getThreeQuestionsParticipationRate(
            new Date(Date.now() - 24 * 60 * 60 * 1000),
            new Date()
        );

        // 获取AI日记生成指标
        const aiDiaryMetrics = await getAIDiaryGenerationMetrics(
            new Date(Date.now() - 24 * 60 * 60 * 1000),
            new Date()
        );

        // 获取定时消息投递统计
        const messageDeliveryStats = await getScheduledMessageDeliveryStats(
            new Date(Date.now() - 24 * 60 * 60 * 1000),
            new Date()
        );

        // 获取今日三问统计
        const todayThreeQuestions = await db.collection('three_questions_metrics')
            .where({
                createTime: _.gte(today).and(_.lt(tomorrow))
            })
            .count();

        return {
            success: true,
            data: {
                overview: {
                    totalUsers: levelDistribution.success ? levelDistribution.data.totalUsers : 0,
                    todayDialogues: todayDialogues.total || 0,
                    todaySwitches: todaySwitches.total || 0,
                    todayEndingMessages: todayEndingMessages.total || 0,
                    todayThreeQuestions: todayThreeQuestions.total || 0,
                    systemHealth: performanceMetrics.success ?
                        (performanceMetrics.data.metrics.systemUptime > 95 ? 'healthy' : 'warning') : 'unknown'
                },
                levelDistribution: levelDistribution.success ? levelDistribution.data.distribution : {},
                qualityMetrics: recentQualityMetrics.success ? recentQualityMetrics.data.metrics : {},
                anomalies: recentAnomalies.success ? recentAnomalies.data.summary : {},
                performance: performanceMetrics.success ? performanceMetrics.data.metrics : {},
                endingMessageMetrics: endingMessageAnalytics.success ? endingMessageAnalytics.data.analytics : {},
                threeQuestionsMetrics: threeQuestionsAnalytics.success ? threeQuestionsAnalytics.data.analytics : {},
                threeQuestionsParticipation: threeQuestionsParticipation.success ? threeQuestionsParticipation.data : {},
                aiDiaryMetrics: aiDiaryMetrics.success ? aiDiaryMetrics.data.metrics : {},
                messageDeliveryStats: messageDeliveryStats.success ? messageDeliveryStats.data.stats : {},
                lastUpdated: new Date()
            }
        };
    } catch (error) {
        console.error('获取仪表板数据失败:', error);
        return { success: false, error: error.message };
    }
}

// 记录对话指标（供其他云函数调用）
async function recordDialogueMetrics(data) {
    try {
        const metricsData = {
            _openid: data.openid,
            userLevel: data.userLevel,
            responseTime: data.responseTime,
            dialogueLength: data.dialogueLength,
            satisfactionScore: data.satisfactionScore,
            completed: data.completed || false,
            hasError: data.hasError || false,
            errorType: data.errorType,
            createTime: new Date()
        };

        await db.collection('dialogue_metrics').add({
            data: metricsData
        });

        return { success: true, message: '对话指标记录成功' };
    } catch (error) {
        console.error('记录对话指标失败:', error);
        return { success: false, error: error.message };
    }
}

// 记录层级切换（供其他云函数调用）
async function recordLevelSwitch(data) {
    try {
        const switchData = {
            _openid: data.openid,
            fromLevel: data.fromLevel,
            toLevel: data.toLevel,
            reason: data.reason,
            triggeredBy: data.triggeredBy || 'user', // user, system, admin
            createTime: new Date()
        };

        await db.collection('level_switch_logs').add({
            data: switchData
        });

        return { success: true, message: '层级切换记录成功' };
    } catch (error) {
        console.error('记录层级切换失败:', error);
        return { success: false, error: error.message };
    }
}

// 记录结束语生成性能指标（供其他云函数调用）
async function recordEndingMessageMetrics(data) {
    try {
        const metricsData = {
            _openid: data.openid,
            userLevel: data.userLevel,
            totalTime: data.totalTime,
            emotionAnalysisTime: data.emotionAnalysisTime,
            aiGenerationTime: data.aiGenerationTime,
            success: data.success,
            errorType: data.errorType,
            fallbackUsed: data.fallbackUsed,
            emotionAnalysisSuccess: data.emotionAnalysisSuccess,
            aiGenerationSuccess: data.aiGenerationSuccess,
            endingMessageLength: data.endingMessageLength,
            errorMessage: data.errorMessage,
            abTestGroup: data.abTestGroup, // A/B测试组别
            timestamp: data.timestamp || new Date(),
            createTime: new Date()
        };

        await db.collection('ending_message_metrics').add({
            data: metricsData
        });

        return { success: true, message: '结束语生成指标记录成功' };
    } catch (error) {
        console.error('记录结束语生成指标失败:', error);
        return { success: false, error: error.message };
    }
}

// 获取结束语生成分析数据
async function getEndingMessageAnalytics(startDate, endDate, userLevel) {
    try {
        const start = startDate ? new Date(startDate) : new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        const end = endDate ? new Date(endDate) : new Date();

        // 构建查询条件
        let query = db.collection('ending_message_metrics')
            .where({
                createTime: _.gte(start).and(_.lte(end))
            });

        if (userLevel) {
            query = query.where({
                userLevel: userLevel
            });
        }

        const metrics = await query.get();

        if (metrics.data.length === 0) {
            return {
                success: true,
                data: {
                    period: { start, end },
                    userLevel,
                    totalGenerations: 0,
                    analytics: {}
                }
            };
        }

        // 计算分析指标
        const analytics = {
            totalGenerations: metrics.data.length,
            successRate: 0,
            averageTotalTime: 0,
            averageEmotionAnalysisTime: 0,
            averageAiGenerationTime: 0,
            fallbackUsageRate: 0,
            emotionAnalysisSuccessRate: 0,
            aiGenerationSuccessRate: 0,
            averageMessageLength: 0,
            errorTypeDistribution: {},
            performanceByLevel: {},
            timeDistribution: {
                fast: 0,    // < 2秒
                normal: 0,  // 2-5秒
                slow: 0     // > 5秒
            }
        };

        let totalSuccessful = 0;
        let totalTime = 0;
        let totalEmotionTime = 0;
        let totalAiTime = 0;
        let totalFallback = 0;
        let totalEmotionSuccess = 0;
        let totalAiSuccess = 0;
        let totalMessageLength = 0;

        metrics.data.forEach(metric => {
            // 基础统计
            if (metric.success) totalSuccessful++;
            totalTime += metric.totalTime || 0;
            totalEmotionTime += metric.emotionAnalysisTime || 0;
            totalAiTime += metric.aiGenerationTime || 0;
            if (metric.fallbackUsed) totalFallback++;
            if (metric.emotionAnalysisSuccess) totalEmotionSuccess++;
            if (metric.aiGenerationSuccess) totalAiSuccess++;
            totalMessageLength += metric.endingMessageLength || 0;

            // 错误类型分布
            if (metric.errorType) {
                analytics.errorTypeDistribution[metric.errorType] =
                    (analytics.errorTypeDistribution[metric.errorType] || 0) + 1;
            }

            // 按层级统计性能
            const level = metric.userLevel || 'unknown';
            if (!analytics.performanceByLevel[level]) {
                analytics.performanceByLevel[level] = {
                    count: 0,
                    successCount: 0,
                    totalTime: 0,
                    fallbackCount: 0
                };
            }
            analytics.performanceByLevel[level].count++;
            if (metric.success) analytics.performanceByLevel[level].successCount++;
            analytics.performanceByLevel[level].totalTime += metric.totalTime || 0;
            if (metric.fallbackUsed) analytics.performanceByLevel[level].fallbackCount++;

            // 时间分布统计
            const time = metric.totalTime || 0;
            if (time < 2000) {
                analytics.timeDistribution.fast++;
            } else if (time <= 5000) {
                analytics.timeDistribution.normal++;
            } else {
                analytics.timeDistribution.slow++;
            }
        });

        const totalCount = metrics.data.length;

        // 计算百分比和平均值
        analytics.successRate = Math.round((totalSuccessful / totalCount) * 100 * 100) / 100;
        analytics.averageTotalTime = Math.round(totalTime / totalCount);
        analytics.averageEmotionAnalysisTime = Math.round(totalEmotionTime / totalCount);
        analytics.averageAiGenerationTime = Math.round(totalAiTime / totalCount);
        analytics.fallbackUsageRate = Math.round((totalFallback / totalCount) * 100 * 100) / 100;
        analytics.emotionAnalysisSuccessRate = Math.round((totalEmotionSuccess / totalCount) * 100 * 100) / 100;
        analytics.aiGenerationSuccessRate = Math.round((totalAiSuccess / totalCount) * 100 * 100) / 100;
        analytics.averageMessageLength = Math.round(totalMessageLength / totalCount);

        // 计算各层级的平均性能
        Object.keys(analytics.performanceByLevel).forEach(level => {
            const levelData = analytics.performanceByLevel[level];
            levelData.successRate = Math.round((levelData.successCount / levelData.count) * 100 * 100) / 100;
            levelData.averageTime = Math.round(levelData.totalTime / levelData.count);
            levelData.fallbackRate = Math.round((levelData.fallbackCount / levelData.count) * 100 * 100) / 100;
        });

        return {
            success: true,
            data: {
                period: { start, end },
                userLevel,
                totalGenerations: totalCount,
                analytics
            }
        };
    } catch (error) {
        console.error('获取结束语生成分析失败:', error);
        return { success: false, error: error.message };
    }
}

// 记录结束语用户反馈（供其他云函数调用）
async function recordEndingMessageFeedback(data) {
    try {
        const feedbackData = {
            _openid: data.openid,
            userLevel: data.userLevel,
            endingMessage: data.endingMessage,
            feedbackType: data.feedbackType, // 'positive', 'negative', 'neutral', 'skip', 'proceed'
            feedbackData: data.feedbackData || {},
            abTestGroup: data.abTestGroup,
            timestamp: data.timestamp || new Date(),
            createTime: new Date()
        };

        await db.collection('ending_message_feedback').add({
            data: feedbackData
        });

        return { success: true, message: '结束语反馈记录成功' };
    } catch (error) {
        console.error('记录结束语反馈失败:', error);
        return { success: false, error: error.message };
    }
}

// 获取结束语A/B测试结果分析
async function getEndingMessageABTestResults(startDate, endDate) {
    try {
        const start = startDate ? new Date(startDate) : new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        const end = endDate ? new Date(endDate) : new Date();

        // 获取A/B测试的性能指标数据
        const metricsData = await db.collection('ending_message_metrics')
            .where({
                createTime: _.gte(start).and(_.lte(end)),
                abTestGroup: _.exists(true)
            })
            .get();

        // 获取A/B测试的用户反馈数据
        const feedbackData = await db.collection('ending_message_feedback')
            .where({
                createTime: _.gte(start).and(_.lte(end)),
                abTestGroup: _.exists(true)
            })
            .get();

        if (metricsData.data.length === 0 && feedbackData.data.length === 0) {
            return {
                success: true,
                data: {
                    period: { start, end },
                    hasData: false,
                    message: '暂无A/B测试数据'
                }
            };
        }

        // 分析A/B测试结果
        const abTestResults = {
            groupA: {
                totalGenerations: 0,
                successRate: 0,
                averageTime: 0,
                fallbackRate: 0,
                userFeedback: {
                    total: 0,
                    positive: 0,
                    negative: 0,
                    neutral: 0,
                    proceed: 0,
                    skip: 0
                }
            },
            groupB: {
                totalGenerations: 0,
                successRate: 0,
                averageTime: 0,
                fallbackRate: 0,
                userFeedback: {
                    total: 0,
                    positive: 0,
                    negative: 0,
                    neutral: 0,
                    proceed: 0,
                    skip: 0
                }
            },
            comparison: {
                significantDifference: false,
                betterGroup: null,
                confidenceLevel: 0,
                recommendations: []
            }
        };

        // 分析性能指标
        const groupAMetrics = metricsData.data.filter(m => m.abTestGroup === 'A');
        const groupBMetrics = metricsData.data.filter(m => m.abTestGroup === 'B');

        // 计算A组指标
        if (groupAMetrics.length > 0) {
            abTestResults.groupA.totalGenerations = groupAMetrics.length;
            abTestResults.groupA.successRate = Math.round(
                (groupAMetrics.filter(m => m.success).length / groupAMetrics.length) * 100 * 100
            ) / 100;
            abTestResults.groupA.averageTime = Math.round(
                groupAMetrics.reduce((sum, m) => sum + (m.totalTime || 0), 0) / groupAMetrics.length
            );
            abTestResults.groupA.fallbackRate = Math.round(
                (groupAMetrics.filter(m => m.fallbackUsed).length / groupAMetrics.length) * 100 * 100
            ) / 100;
        }

        // 计算B组指标
        if (groupBMetrics.length > 0) {
            abTestResults.groupB.totalGenerations = groupBMetrics.length;
            abTestResults.groupB.successRate = Math.round(
                (groupBMetrics.filter(m => m.success).length / groupBMetrics.length) * 100 * 100
            ) / 100;
            abTestResults.groupB.averageTime = Math.round(
                groupBMetrics.reduce((sum, m) => sum + (m.totalTime || 0), 0) / groupBMetrics.length
            );
            abTestResults.groupB.fallbackRate = Math.round(
                (groupBMetrics.filter(m => m.fallbackUsed).length / groupBMetrics.length) * 100 * 100
            ) / 100;
        }

        // 分析用户反馈
        const groupAFeedback = feedbackData.data.filter(f => f.abTestGroup === 'A');
        const groupBFeedback = feedbackData.data.filter(f => f.abTestGroup === 'B');

        // 统计A组反馈
        abTestResults.groupA.userFeedback.total = groupAFeedback.length;
        groupAFeedback.forEach(feedback => {
            if (abTestResults.groupA.userFeedback[feedback.feedbackType] !== undefined) {
                abTestResults.groupA.userFeedback[feedback.feedbackType]++;
            }
        });

        // 统计B组反馈
        abTestResults.groupB.userFeedback.total = groupBFeedback.length;
        groupBFeedback.forEach(feedback => {
            if (abTestResults.groupB.userFeedback[feedback.feedbackType] !== undefined) {
                abTestResults.groupB.userFeedback[feedback.feedbackType]++;
            }
        });

        // 简单的统计显著性分析
        const minSampleSize = 30; // 最小样本量
        if (abTestResults.groupA.totalGenerations >= minSampleSize &&
            abTestResults.groupB.totalGenerations >= minSampleSize) {

            // 比较成功率差异
            const successRateDiff = Math.abs(abTestResults.groupA.successRate - abTestResults.groupB.successRate);
            const timeDiff = Math.abs(abTestResults.groupA.averageTime - abTestResults.groupB.averageTime);

            if (successRateDiff > 5 || timeDiff > 1000) { // 成功率差异>5%或时间差异>1秒
                abTestResults.comparison.significantDifference = true;

                // 确定更好的组别
                if (abTestResults.groupA.successRate > abTestResults.groupB.successRate) {
                    abTestResults.comparison.betterGroup = 'A';
                } else if (abTestResults.groupB.successRate > abTestResults.groupA.successRate) {
                    abTestResults.comparison.betterGroup = 'B';
                } else if (abTestResults.groupA.averageTime < abTestResults.groupB.averageTime) {
                    abTestResults.comparison.betterGroup = 'A';
                } else {
                    abTestResults.comparison.betterGroup = 'B';
                }

                abTestResults.comparison.confidenceLevel = successRateDiff > 10 ? 95 : 80;
            }
        }

        // 生成建议
        if (abTestResults.comparison.significantDifference) {
            const betterGroup = abTestResults.comparison.betterGroup;
            const betterGroupData = abTestResults[`group${betterGroup}`];

            abTestResults.comparison.recommendations.push(
                `建议采用组别${betterGroup}的配置，其成功率为${betterGroupData.successRate}%，平均响应时间为${betterGroupData.averageTime}ms`
            );

            if (betterGroupData.fallbackRate < 10) {
                abTestResults.comparison.recommendations.push(
                    `组别${betterGroup}的备用方案使用率较低(${betterGroupData.fallbackRate}%)，表现稳定`
                );
            }
        } else {
            abTestResults.comparison.recommendations.push(
                '两组表现差异不显著，建议继续收集更多数据或调整测试参数'
            );
        }

        return {
            success: true,
            data: {
                period: { start, end },
                hasData: true,
                abTestResults
            }
        };
    } catch (error) {
        console.error('获取A/B测试结果失败:', error);
        return { success: false, error: error.message };
    }
}
// 
记录三问仪式指标（供其他云函数调用）
async function recordThreeQuestionsMetrics(data) {
    try {
        const metricsData = {
            _openid: data.openid,
            userLevel: data.userLevel,

            // 参与度指标
            startedAt: data.startedAt || new Date(),
            completedAt: data.completedAt,
            totalTime: data.totalTime, // 完成三问的总时间

            // 各问题参与情况
            question1Answered: data.question1Answered || false,
            question1Skipped: data.question1Skipped || false,
            question1Time: data.question1Time || 0,

            question2Answered: data.question2Answered || false,
            question2Skipped: data.question2Skipped || false,
            question2Time: data.question2Time || 0,

            question3Answered: data.question3Answered || false,
            question3Skipped: data.question3Skipped || false,
            question3Time: data.question3Time || 0,

            // AI日记生成相关
            requestedDiary: data.requestedDiary || false,
            diaryGenerated: data.diaryGenerated || false,
            diaryGenerationTime: data.diaryGenerationTime || 0,
            diaryGenerationSuccess: data.diaryGenerationSuccess || false,
            diaryFallbackUsed: data.diaryFallbackUsed || false,
            diaryWordCount: data.diaryWordCount || 0,

            // 定时消息相关
            scheduledMessage: data.scheduledMessage || false,
            messageScheduleSuccess: data.messageScheduleSuccess || false,

            // 完成状态
            completed: data.completed || false,
            exitPoint: data.exitPoint, // 用户退出的位置

            // 关联信息
            dialogueTheme: data.dialogueTheme,
            sourceRecordId: data.sourceRecordId,

            createTime: new Date()
        };

        await db.collection('three_questions_metrics').add({
            data: metricsData
        });

        return { success: true, message: '三问仪式指标记录成功' };
    } catch (error) {
        console.error('记录三问仪式指标失败:', error);
        return { success: false, error: error.message };
    }
}

// 获取三问仪式分析数据
async function getThreeQuestionsAnalytics(startDate, endDate, userLevel) {
    try {
        const start = startDate ? new Date(startDate) : new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        const end = endDate ? new Date(endDate) : new Date();

        // 构建查询条件
        let query = db.collection('three_questions_metrics')
            .where({
                createTime: _.gte(start).and(_.lte(end))
            });

        if (userLevel) {
            query = query.where({
                userLevel: userLevel
            });
        }

        const metrics = await query.get();

        if (metrics.data.length === 0) {
            return {
                success: true,
                data: {
                    period: { start, end },
                    userLevel,
                    totalSessions: 0,
                    analytics: {}
                }
            };
        }

        // 计算分析指标
        const analytics = {
            totalSessions: metrics.data.length,
            completionRate: 0,
            averageSessionTime: 0,

            // 各问题参与率
            question1: {
                participationRate: 0,
                skipRate: 0,
                averageTime: 0
            },
            question2: {
                participationRate: 0,
                skipRate: 0,
                averageTime: 0
            },
            question3: {
                participationRate: 0,
                skipRate: 0,
                averageTime: 0
            },

            // AI日记生成指标
            diaryGeneration: {
                requestRate: 0,
                successRate: 0,
                fallbackRate: 0,
                averageGenerationTime: 0,
                averageWordCount: 0
            },

            // 定时消息指标
            scheduledMessages: {
                creationRate: 0,
                successRate: 0
            },

            // 用户行为模式
            exitPoints: {},
            performanceByLevel: {},
            timeDistribution: {
                quick: 0,    // < 2分钟
                normal: 0,   // 2-5分钟
                slow: 0      // > 5分钟
            }
        };

        let totalCompleted = 0;
        let totalSessionTime = 0;
        let q1Answered = 0, q1Skipped = 0, q1TotalTime = 0;
        let q2Answered = 0, q2Skipped = 0, q2TotalTime = 0;
        let q3Answered = 0, q3Skipped = 0, q3TotalTime = 0;
        let diaryRequested = 0, diaryGenerated = 0, diaryFallback = 0;
        let diaryTotalTime = 0, diaryTotalWords = 0;
        let messagesScheduled = 0, messagesSuccess = 0;

        metrics.data.forEach(metric => {
            // 基础统计
            if (metric.completed) totalCompleted++;
            totalSessionTime += metric.totalTime || 0;

            // 问题参与统计
            if (metric.question1Answered) q1Answered++;
            if (metric.question1Skipped) q1Skipped++;
            q1TotalTime += metric.question1Time || 0;

            if (metric.question2Answered) q2Answered++;
            if (metric.question2Skipped) q2Skipped++;
            q2TotalTime += metric.question2Time || 0;

            if (metric.question3Answered) q3Answered++;
            if (metric.question3Skipped) q3Skipped++;
            q3TotalTime += metric.question3Time || 0;

            // AI日记统计
            if (metric.requestedDiary) diaryRequested++;
            if (metric.diaryGenerated) diaryGenerated++;
            if (metric.diaryFallbackUsed) diaryFallback++;
            diaryTotalTime += metric.diaryGenerationTime || 0;
            diaryTotalWords += metric.diaryWordCount || 0;

            // 定时消息统计
            if (metric.scheduledMessage) messagesScheduled++;
            if (metric.messageScheduleSuccess) messagesSuccess++;

            // 退出点统计
            if (metric.exitPoint) {
                analytics.exitPoints[metric.exitPoint] = (analytics.exitPoints[metric.exitPoint] || 0) + 1;
            }

            // 按层级统计
            const level = metric.userLevel || 'unknown';
            if (!analytics.performanceByLevel[level]) {
                analytics.performanceByLevel[level] = {
                    count: 0,
                    completedCount: 0,
                    totalTime: 0,
                    diaryRequestCount: 0,
                    diarySuccessCount: 0
                };
            }
            analytics.performanceByLevel[level].count++;
            if (metric.completed) analytics.performanceByLevel[level].completedCount++;
            analytics.performanceByLevel[level].totalTime += metric.totalTime || 0;
            if (metric.requestedDiary) analytics.performanceByLevel[level].diaryRequestCount++;
            if (metric.diaryGenerated) analytics.performanceByLevel[level].diarySuccessCount++;

            // 时间分布统计
            const time = metric.totalTime || 0;
            if (time < 120000) { // 2分钟
                analytics.timeDistribution.quick++;
            } else if (time <= 300000) { // 5分钟
                analytics.timeDistribution.normal++;
            } else {
                analytics.timeDistribution.slow++;
            }
        });

        const totalCount = metrics.data.length;

        // 计算百分比和平均值
        analytics.completionRate = Math.round((totalCompleted / totalCount) * 100 * 100) / 100;
        analytics.averageSessionTime = Math.round(totalSessionTime / totalCount);

        // 问题参与率
        analytics.question1.participationRate = Math.round((q1Answered / totalCount) * 100 * 100) / 100;
        analytics.question1.skipRate = Math.round((q1Skipped / totalCount) * 100 * 100) / 100;
        analytics.question1.averageTime = q1Answered > 0 ? Math.round(q1TotalTime / q1Answered) : 0;

        analytics.question2.participationRate = Math.round((q2Answered / totalCount) * 100 * 100) / 100;
        analytics.question2.skipRate = Math.round((q2Skipped / totalCount) * 100 * 100) / 100;
        analytics.question2.averageTime = q2Answered > 0 ? Math.round(q2TotalTime / q2Answered) : 0;

        analytics.question3.participationRate = Math.round((q3Answered / totalCount) * 100 * 100) / 100;
        analytics.question3.skipRate = Math.round((q3Skipped / totalCount) * 100 * 100) / 100;
        analytics.question3.averageTime = q3Answered > 0 ? Math.round(q3TotalTime / q3Answered) : 0;

        // AI日记生成指标
        analytics.diaryGeneration.requestRate = Math.round((diaryRequested / totalCount) * 100 * 100) / 100;
        analytics.diaryGeneration.successRate = diaryRequested > 0 ? Math.round((diaryGenerated / diaryRequested) * 100 * 100) / 100 : 0;
        analytics.diaryGeneration.fallbackRate = diaryRequested > 0 ? Math.round((diaryFallback / diaryRequested) * 100 * 100) / 100 : 0;
        analytics.diaryGeneration.averageGenerationTime = diaryRequested > 0 ? Math.round(diaryTotalTime / diaryRequested) : 0;
        analytics.diaryGeneration.averageWordCount = diaryGenerated > 0 ? Math.round(diaryTotalWords / diaryGenerated) : 0;

        // 定时消息指标
        analytics.scheduledMessages.creationRate = Math.round((messagesScheduled / totalCount) * 100 * 100) / 100;
        analytics.scheduledMessages.successRate = messagesScheduled > 0 ? Math.round((messagesSuccess / messagesScheduled) * 100 * 100) / 100 : 0;

        // 计算各层级的平均性能
        Object.keys(analytics.performanceByLevel).forEach(level => {
            const levelData = analytics.performanceByLevel[level];
            levelData.completionRate = Math.round((levelData.completedCount / levelData.count) * 100 * 100) / 100;
            levelData.averageTime = Math.round(levelData.totalTime / levelData.count);
            levelData.diaryRequestRate = Math.round((levelData.diaryRequestCount / levelData.count) * 100 * 100) / 100;
            levelData.diarySuccessRate = levelData.diaryRequestCount > 0 ?
                Math.round((levelData.diarySuccessCount / levelData.diaryRequestCount) * 100 * 100) / 100 : 0;
        });

        return {
            success: true,
            data: {
                period: { start, end },
                userLevel,
                totalSessions: totalCount,
                analytics
            }
        };
    } catch (error) {
        console.error('获取三问仪式分析失败:', error);
        return { success: false, error: error.message };
    }
}

// 获取三问参与度统计
async function getThreeQuestionsParticipationRate(startDate, endDate) {
    try {
        const start = startDate ? new Date(startDate) : new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        const end = endDate ? new Date(endDate) : new Date();

        // 获取对话总数（作为基数）
        const totalDialogues = await db.collection('dialogue_metrics')
            .where({
                createTime: _.gte(start).and(_.lte(end))
            })
            .count();

        // 获取三问参与数
        const threeQuestionsParticipation = await db.collection('three_questions_metrics')
            .where({
                createTime: _.gte(start).and(_.lte(end))
            })
            .count();

        // 获取完成三问的数量
        const completedThreeQuestions = await db.collection('three_questions_metrics')
            .where({
                createTime: _.gte(start).and(_.lte(end)),
                completed: true
            })
            .count();

        const totalDialogueCount = totalDialogues.total || 0;
        const participationCount = threeQuestionsParticipation.total || 0;
        const completedCount = completedThreeQuestions.total || 0;

        const participationRate = totalDialogueCount > 0 ?
            Math.round((participationCount / totalDialogueCount) * 100 * 100) / 100 : 0;
        const completionRate = participationCount > 0 ?
            Math.round((completedCount / participationCount) * 100 * 100) / 100 : 0;

        return {
            success: true,
            data: {
                period: { start, end },
                totalDialogues: totalDialogueCount,
                participationCount,
                completedCount,
                participationRate,
                completionRate,
                dropoffRate: Math.round((100 - completionRate) * 100) / 100
            }
        };
    } catch (error) {
        console.error('获取三问参与度统计失败:', error);
        return { success: false, error: error.message };
    }
}

// 获取AI日记生成指标
async function getAIDiaryGenerationMetrics(startDate, endDate, userLevel) {
    try {
        const start = startDate ? new Date(startDate) : new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        const end = endDate ? new Date(endDate) : new Date();

        // 构建查询条件
        let query = db.collection('three_questions_metrics')
            .where({
                createTime: _.gte(start).and(_.lte(end)),
                requestedDiary: true
            });

        if (userLevel) {
            query = query.where({
                userLevel: userLevel
            });
        }

        const diaryMetrics = await query.get();

        if (diaryMetrics.data.length === 0) {
            return {
                success: true,
                data: {
                    period: { start, end },
                    userLevel,
                    totalRequests: 0,
                    metrics: {}
                }
            };
        }

        // 计算AI日记生成指标
        const metrics = {
            totalRequests: diaryMetrics.data.length,
            successRate: 0,
            fallbackRate: 0,
            averageGenerationTime: 0,
            averageWordCount: 0,
            qualityDistribution: {
                high: 0,    // > 400字
                medium: 0,  // 200-400字
                low: 0      // < 200字
            },
            performanceByLevel: {},
            timeDistribution: {
                fast: 0,    // < 3秒
                normal: 0,  // 3-10秒
                slow: 0     // > 10秒
            },
            errorTypes: {}
        };

        let successCount = 0;
        let fallbackCount = 0;
        let totalGenerationTime = 0;
        let totalWordCount = 0;
        let validGenerations = 0;

        diaryMetrics.data.forEach(metric => {
            // 基础统计
            if (metric.diaryGenerated) successCount++;
            if (metric.diaryFallbackUsed) fallbackCount++;
            totalGenerationTime += metric.diaryGenerationTime || 0;

            if (metric.diaryWordCount > 0) {
                totalWordCount += metric.diaryWordCount;
                validGenerations++;

                // 质量分布
                if (metric.diaryWordCount > 400) {
                    metrics.qualityDistribution.high++;
                } else if (metric.diaryWordCount >= 200) {
                    metrics.qualityDistribution.medium++;
                } else {
                    metrics.qualityDistribution.low++;
                }
            }

            // 按层级统计
            const level = metric.userLevel || 'unknown';
            if (!metrics.performanceByLevel[level]) {
                metrics.performanceByLevel[level] = {
                    requests: 0,
                    successes: 0,
                    totalTime: 0,
                    totalWords: 0,
                    validCount: 0
                };
            }
            metrics.performanceByLevel[level].requests++;
            if (metric.diaryGenerated) metrics.performanceByLevel[level].successes++;
            metrics.performanceByLevel[level].totalTime += metric.diaryGenerationTime || 0;
            if (metric.diaryWordCount > 0) {
                metrics.performanceByLevel[level].totalWords += metric.diaryWordCount;
                metrics.performanceByLevel[level].validCount++;
            }

            // 时间分布统计
            const time = metric.diaryGenerationTime || 0;
            if (time < 3000) {
                metrics.timeDistribution.fast++;
            } else if (time <= 10000) {
                metrics.timeDistribution.normal++;
            } else {
                metrics.timeDistribution.slow++;
            }
        });

        const totalRequests = diaryMetrics.data.length;

        // 计算百分比和平均值
        metrics.successRate = Math.round((successCount / totalRequests) * 100 * 100) / 100;
        metrics.fallbackRate = Math.round((fallbackCount / totalRequests) * 100 * 100) / 100;
        metrics.averageGenerationTime = Math.round(totalGenerationTime / totalRequests);
        metrics.averageWordCount = validGenerations > 0 ? Math.round(totalWordCount / validGenerations) : 0;

        // 计算各层级的平均性能
        Object.keys(metrics.performanceByLevel).forEach(level => {
            const levelData = metrics.performanceByLevel[level];
            levelData.successRate = Math.round((levelData.successes / levelData.requests) * 100 * 100) / 100;
            levelData.averageTime = Math.round(levelData.totalTime / levelData.requests);
            levelData.averageWordCount = levelData.validCount > 0 ?
                Math.round(levelData.totalWords / levelData.validCount) : 0;
        });

        return {
            success: true,
            data: {
                period: { start, end },
                userLevel,
                totalRequests,
                metrics
            }
        };
    } catch (error) {
        console.error('获取AI日记生成指标失败:', error);
        return { success: false, error: error.message };
    }
}

// 获取定时消息投递统计
async function getScheduledMessageDeliveryStats(startDate, endDate) {
    try {
        const start = startDate ? new Date(startDate) : new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        const end = endDate ? new Date(endDate) : new Date();

        // 获取定时消息数据
        const scheduledMessages = await db.collection('scheduled_messages')
            .where({
                createTime: _.gte(start).and(_.lte(end))
            })
            .get();

        if (scheduledMessages.data.length === 0) {
            return {
                success: true,
                data: {
                    period: { start, end },
                    totalMessages: 0,
                    stats: {}
                }
            };
        }

        // 计算投递统计
        const stats = {
            totalMessages: scheduledMessages.data.length,
            deliveredCount: 0,
            pendingCount: 0,
            failedCount: 0,
            retryCount: 0,
            deliveryRate: 0,
            averageDeliveryDelay: 0,
            retryDistribution: {},
            statusDistribution: {},
            deliveryChannelStats: {}
        };

        let totalDeliveryDelay = 0;
        let deliveredMessages = 0;

        scheduledMessages.data.forEach(message => {
            // 状态统计
            const status = message.status || 'unknown';
            stats.statusDistribution[status] = (stats.statusDistribution[status] || 0) + 1;

            switch (status) {
                case 'delivered':
                    stats.deliveredCount++;
                    deliveredMessages++;

                    // 计算投递延迟
                    if (message.scheduledTime && message.deliveredTime) {
                        const delay = new Date(message.deliveredTime) - new Date(message.scheduledTime);
                        totalDeliveryDelay += Math.max(0, delay);
                    }

                    // 投递渠道统计
                    if (message.primaryChannel) {
                        stats.deliveryChannelStats[message.primaryChannel] =
                            (stats.deliveryChannelStats[message.primaryChannel] || 0) + 1;
                    }
                    break;
                case 'pending':
                case 'retry_pending':
                    stats.pendingCount++;
                    break;
                case 'failed':
                case 'error':
                    stats.failedCount++;
                    break;
            }

            // 重试次数统计
            const retryCount = message.retryCount || 0;
            stats.retryDistribution[retryCount] = (stats.retryDistribution[retryCount] || 0) + 1;
            if (retryCount > 0) {
                stats.retryCount++;
            }
        });

        // 计算百分比和平均值
        stats.deliveryRate = Math.round((stats.deliveredCount / stats.totalMessages) * 100 * 100) / 100;
        stats.averageDeliveryDelay = deliveredMessages > 0 ?
            Math.round(totalDeliveryDelay / deliveredMessages / 1000) : 0; // 转换为秒

        return {
            success: true,
            data: {
                period: { start, end },
                totalMessages: stats.totalMessages,
                stats
            }
        };
    } catch (error) {
        console.error('获取定时消息投递统计失败:', error);
        return { success: false, error: error.message };
    }
}

// 获取用户行为分析
async function getUserBehaviorAnalysis(openid, startDate, endDate) {
    try {
        const start = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        const end = endDate ? new Date(endDate) : new Date();

        if (!openid) {
            return {
                success: false,
                error: '用户ID不能为空'
            };
        }

        // 获取用户的三问参与记录
        const userThreeQuestions = await db.collection('three_questions_metrics')
            .where({
                _openid: openid,
                createTime: _.gte(start).and(_.lte(end))
            })
            .orderBy('createTime', 'desc')
            .get();

        // 获取用户的对话记录
        const userDialogues = await db.collection('dialogue_metrics')
            .where({
                _openid: openid,
                createTime: _.gte(start).and(_.lte(end))
            })
            .orderBy('createTime', 'desc')
            .get();

        // 获取用户的定时消息记录
        const userMessages = await db.collection('scheduled_messages')
            .where({
                openid: openid,
                createTime: _.gte(start).and(_.lte(end))
            })
            .orderBy('createTime', 'desc')
            .get();

        const analysis = {
            period: { start, end },
            openid,

            // 基础统计
            totalDialogues: userDialogues.data.length,
            totalThreeQuestionsSessions: userThreeQuestions.data.length,
            totalScheduledMessages: userMessages.data.length,

            // 参与模式
            participationPattern: {
                threeQuestionsParticipationRate: 0,
                averageSessionInterval: 0,
                preferredQuestions: [],
                skipPatterns: []
            },

            // AI日记使用情况
            diaryUsage: {
                totalRequests: 0,
                successfulGenerations: 0,
                averageWordCount: 0,
                preferredStyle: 'unknown'
            },

            // 定时消息行为
            messageScheduling: {
                totalScheduled: userMessages.data.length,
                deliverySuccessRate: 0,
                averageMessageLength: 0,
                schedulingFrequency: 'low'
            },

            // 行为趋势
            trends: {
                engagementTrend: 'stable', // increasing, decreasing, stable
                qualityTrend: 'stable',
                consistencyScore: 0
            }
        };

        // 计算参与率
        if (userDialogues.data.length > 0) {
            analysis.participationPattern.threeQuestionsParticipationRate =
                Math.round((userThreeQuestions.data.length / userDialogues.data.length) * 100 * 100) / 100;
        }

        // 分析三问行为模式
        if (userThreeQuestions.data.length > 0) {
            let q1Answered = 0, q2Answered = 0, q3Answered = 0;
            let q1Skipped = 0, q2Skipped = 0, q3Skipped = 0;
            let totalDiaryRequests = 0, successfulDiaries = 0;
            let totalWords = 0, validDiaries = 0;

            userThreeQuestions.data.forEach(session => {
                if (session.question1Answered) q1Answered++;
                if (session.question1Skipped) q1Skipped++;
                if (session.question2Answered) q2Answered++;
                if (session.question2Skipped) q2Skipped++;
                if (session.question3Answered) q3Answered++;
                if (session.question3Skipped) q3Skipped++;

                if (session.requestedDiary) totalDiaryRequests++;
                if (session.diaryGenerated) successfulDiaries++;
                if (session.diaryWordCount > 0) {
                    totalWords += session.diaryWordCount;
                    validDiaries++;
                }
            });

            // 偏好问题分析
            const questionPreferences = [
                { question: '第一问', answered: q1Answered, skipped: q1Skipped },
                { question: '第二问', answered: q2Answered, skipped: q2Skipped },
                { question: '第三问', answered: q3Answered, skipped: q3Skipped }
            ];

            analysis.participationPattern.preferredQuestions = questionPreferences
                .sort((a, b) => (b.answered / (b.answered + b.skipped)) - (a.answered / (a.answered + a.skipped)))
                .map(q => q.question);

            // AI日记使用分析
            analysis.diaryUsage.totalRequests = totalDiaryRequests;
            analysis.diaryUsage.successfulGenerations = successfulDiaries;
            analysis.diaryUsage.averageWordCount = validDiaries > 0 ? Math.round(totalWords / validDiaries) : 0;
        }

        // 定时消息分析
        if (userMessages.data.length > 0) {
            const deliveredMessages = userMessages.data.filter(m => m.status === 'delivered').length;
            analysis.messageScheduling.deliverySuccessRate =
                Math.round((deliveredMessages / userMessages.data.length) * 100 * 100) / 100;

            const totalMessageLength = userMessages.data.reduce((sum, m) => sum + (m.message?.length || 0), 0);
            analysis.messageScheduling.averageMessageLength =
                Math.round(totalMessageLength / userMessages.data.length);

            // 调度频率分析
            const daysInPeriod = (end - start) / (24 * 60 * 60 * 1000);
            const messagesPerDay = userMessages.data.length / daysInPeriod;

            if (messagesPerDay > 0.5) {
                analysis.messageScheduling.schedulingFrequency = 'high';
            } else if (messagesPerDay > 0.2) {
                analysis.messageScheduling.schedulingFrequency = 'medium';
            } else {
                analysis.messageScheduling.schedulingFrequency = 'low';
            }
        }

        // 计算一致性分数（基于参与的规律性）
        if (userThreeQuestions.data.length >= 3) {
            const intervals = [];
            for (let i = 1; i < userThreeQuestions.data.length; i++) {
                const interval = new Date(userThreeQuestions.data[i - 1].createTime) -
                    new Date(userThreeQuestions.data[i].createTime);
                intervals.push(Math.abs(interval));
            }

            const avgInterval = intervals.reduce((sum, interval) => sum + interval, 0) / intervals.length;
            const variance = intervals.reduce((sum, interval) => sum + Math.pow(interval - avgInterval, 2), 0) / intervals.length;
            const consistency = Math.max(0, 100 - (Math.sqrt(variance) / avgInterval * 100));

            analysis.trends.consistencyScore = Math.round(consistency * 100) / 100;
        }

        return {
            success: true,
            data: analysis
        };
    } catch (error) {
        console.error('获取用户行为分析失败:', error);
        return { success: false, error: error.message };
    }
}