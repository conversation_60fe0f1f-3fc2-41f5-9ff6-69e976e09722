const cloud = require('wx-server-sdk')
cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV })

// 引入 Gemini API 代理
const gemini = require('./geminiProxy')

exports.main = async (event, context) => {
  const { dialogueTheme, dialogueContent } = event

  try {
    // 构建总结提示词
    const summaryPrompt = `你是一个温暖、富有洞察力的倾听者。请根据以下主题和对话内容，生成一段深度、富有同理心的总结。

对话主题：${dialogueTheme}

对话内容：${JSON.stringify(dialogueContent)}

请按照以下要求生成总结：
1. 语言要温暖、富有同理心
2. 总结不超过100字
3. 突出对话中用户的情感和成长
4. 用朋友般亲切的语气
5. 给出一个温和的鼓励或洞察`

    // 调用 Gemini API 生成总结
    const summaryResponse = await gemini.callGemini({
      prompt: summaryPrompt,
      temperature: 0.7,
      maxOutputTokens: 200
    })

    return {
      success: true,
      summary: summaryResponse
    }
  } catch (error) {
    console.error('生成AI总结错误', error)
    return {
      success: false,
      error: error.message || '总结生成失败'
    }
  }
} 