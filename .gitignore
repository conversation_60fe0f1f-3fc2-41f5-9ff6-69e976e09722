# 微信小程序常用忽略文件
miniprogram_npm
# node依赖
node_modules/

# 云开发环境文件
cloudfunctions/*/node_modules/
cloudfunctions/*/package-lock.json
cloudfunctions/*/.env

# 编译输出
miniprogram_dist/
dist/
build/

# IDE配置
.idea/
.vscode/

# 日志文件
*.log

# Mac系统文件
.DS_Store

# 微信开发者工具自动生成
.project.config.json
.project.private.config.json

# 其他
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 测试文件和调试文件
test-*.js
simple-*.js
debug-*.js
run-*.js
deploy-*.js
monitor-*.js
validate-*.js
*-test.js
*-debug.js

# 文档和总结文件
*-SUMMARY.md
*-implementation-summary.md
*-deployment-guide.md
*-README.md
TASK_*.md
TEST_*.md

# 云开发自动生成
.cloudbase/ 
/assets/bgm.mp31
miniprogram-2
images
assets
images1
assets1
.kiro