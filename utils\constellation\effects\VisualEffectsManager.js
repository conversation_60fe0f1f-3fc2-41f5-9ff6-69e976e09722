/**
 * 视觉效果管理器
 * 负责管理星星的颜色、发光、闪烁等视觉效果
 */

class VisualEffectsManager {
    constructor() {
        this.emotionColors = {
            // 积极情感
            '开心': '#FFD700',     // 金色
            '快乐': '#FF69B4',     // 热粉色
            '兴奋': '#FF4500',     // 橙红色
            '满足': '#32CD32',     // 酸橙绿
            '平静': '#87CEEB',     // 天蓝色
            '感激': '#DDA0DD',     // 梅花色
            '希望': '#98FB98',     // 淡绿色
            '爱': '#FF1493',       // 深粉色
            
            // 中性情感
            '思考': '#9370DB',     // 中紫色
            '好奇': '#20B2AA',     // 浅海绿
            '专注': '#4169E1',     // 皇家蓝
            '平静': '#708090',     // 石板灰
            '期待': '#FFA500',     // 橙色
            
            // 消极情感
            '难过': '#4682B4',     // 钢蓝色
            '焦虑': '#CD853F',     // 秘鲁色
            '疲惫': '#696969',     // 暗灰色
            '困惑': '#BC8F8F',     // 玫瑰棕
            '失望': '#778899',     // 浅石板灰
            '孤独': '#2F4F4F',     // 暗石板灰
            
            // 默认颜色
            '默认': '#FFFFFF'      // 白色
        }

        this.glowIntensities = {
            '开心': 1.5,
            '快乐': 1.4,
            '兴奋': 1.6,
            '爱': 1.5,
            '希望': 1.3,
            '平静': 0.8,
            '思考': 1.0,
            '难过': 0.6,
            '焦虑': 1.2,
            '疲惫': 0.5,
            '默认': 1.0
        }

        this.twinklePatterns = {
            '开心': { frequency: 0.8, intensity: 0.3 },
            '兴奋': { frequency: 1.2, intensity: 0.5 },
            '平静': { frequency: 0.3, intensity: 0.2 },
            '思考': { frequency: 0.5, intensity: 0.25 },
            '默认': { frequency: 0.6, intensity: 0.3 }
        }

        this.activeEffects = new Map()
        this.effectId = 0
    }

    /**
     * 初始化视觉效果管理器
     */
    initialize() {
        console.log('初始化视觉效果管理器...')
        return { success: true }
    }

    /**
     * 根据情感关键词获取星星颜色
     */
    getEmotionColor(emotionKeyword) {
        if (!emotionKeyword) return this.emotionColors['默认']
        
        // 直接匹配
        if (this.emotionColors[emotionKeyword]) {
            return this.emotionColors[emotionKeyword]
        }

        // 模糊匹配
        for (const [emotion, color] of Object.entries(this.emotionColors)) {
            if (emotionKeyword.includes(emotion) || emotion.includes(emotionKeyword)) {
                return color
            }
        }

        return this.emotionColors['默认']
    }

    /**
     * 获取情感对应的发光强度
     */
    getGlowIntensity(emotionKeyword) {
        return this.glowIntensities[emotionKeyword] || this.glowIntensities['默认']
    }

    /**
     * 获取闪烁模式
     */
    getTwinklePattern(emotionKeyword) {
        return this.twinklePatterns[emotionKeyword] || this.twinklePatterns['默认']
    }

    /**
     * 应用星星基础视觉效果
     */
    applyStarVisualEffects(starElement, emotionKeyword, options = {}) {
        if (!starElement) return null

        const color = this.getEmotionColor(emotionKeyword)
        const glowIntensity = this.getGlowIntensity(emotionKeyword)
        const twinklePattern = this.getTwinklePattern(emotionKeyword)

        // 应用基础样式
        this.applyBaseStyles(starElement, color, glowIntensity, options)

        // 启动闪烁效果
        const twinkleEffectId = this.startTwinkleEffect(starElement, twinklePattern, options)

        // 记录效果
        const effectId = ++this.effectId
        this.activeEffects.set(effectId, {
            element: starElement,
            type: 'star_visual',
            color,
            glowIntensity,
            twinkleEffectId,
            emotionKeyword
        })

        return effectId
    }

    /**
     * 应用基础样式
     */
    applyBaseStyles(element, color, glowIntensity, options = {}) {
        const size = options.size || 1.0
        const brightness = options.brightness || 1.0
        
        // 设置颜色
        element.style.backgroundColor = color
        element.style.color = color

        // 设置发光效果
        const glowSize = glowIntensity * 8 * size
        const glowOpacity = Math.min(glowIntensity * brightness * 0.8, 1)
        element.style.boxShadow = `
            0 0 ${glowSize}px ${this.hexToRgba(color, glowOpacity)},
            0 0 ${glowSize * 2}px ${this.hexToRgba(color, glowOpacity * 0.5)},
            inset 0 0 ${glowSize * 0.5}px ${this.hexToRgba(color, 0.3)}
        `

        // 设置边框
        element.style.border = `2px solid ${this.hexToRgba(color, 0.8)}`
        element.style.borderRadius = '50%'

        // 设置大小
        const starSize = 20 * size
        element.style.width = `${starSize}px`
        element.style.height = `${starSize}px`
    }

    /**
     * 启动闪烁效果
     */
    startTwinkleEffect(element, pattern, options = {}) {
        const { frequency, intensity } = pattern
        const baseOpacity = options.brightness || 1.0
        
        let startTime = Date.now()
        
        const twinkle = () => {
            const elapsed = (Date.now() - startTime) / 1000
            const twinkleValue = Math.sin(elapsed * frequency * Math.PI * 2) * intensity
            const opacity = Math.max(0.3, Math.min(1, baseOpacity + twinkleValue))
            
            if (element && element.style) {
                element.style.opacity = opacity
            }
            
            return requestAnimationFrame(twinkle)
        }
        
        return twinkle()
    }

    /**
     * 创建星座连线效果
     */
    createConstellationLines(stars, options = {}) {
        const {
            color = '#FFFFFF',
            opacity = 0.3,
            width = 1,
            animated = true
        } = options

        const lines = []
        
        // 计算连线
        for (let i = 0; i < stars.length - 1; i++) {
            const currentStar = stars[i]
            const nextStar = stars[i + 1]
            
            if (currentStar.position && nextStar.position) {
                const line = this.createLine(
                    currentStar.position,
                    nextStar.position,
                    { color, opacity, width, animated }
                )
                lines.push(line)
            }
        }

        return lines
    }

    /**
     * 创建单条连线
     */
    createLine(startPos, endPos, options = {}) {
        const line = {
            id: `line_${++this.effectId}`,
            start: startPos,
            end: endPos,
            style: {
                stroke: options.color,
                strokeOpacity: options.opacity,
                strokeWidth: options.width,
                strokeDasharray: options.animated ? '5,5' : 'none'
            }
        }

        if (options.animated) {
            this.animateLine(line)
        }

        return line
    }

    /**
     * 动画连线
     */
    animateLine(line) {
        let offset = 0
        const animate = () => {
            offset += 0.5
            line.style.strokeDashoffset = -offset
            requestAnimationFrame(animate)
        }
        animate()
    }

    /**
     * 创建粒子效果
     */
    createParticleEffect(centerPosition, options = {}) {
        const {
            particleCount = 20,
            color = '#FFFFFF',
            size = 2,
            duration = 2000,
            spread = 50
        } = options

        const particles = []
        
        for (let i = 0; i < particleCount; i++) {
            const angle = (i / particleCount) * Math.PI * 2
            const distance = Math.random() * spread
            
            const particle = {
                id: `particle_${++this.effectId}`,
                startPos: { ...centerPosition },
                endPos: {
                    x: centerPosition.x + Math.cos(angle) * distance,
                    y: centerPosition.y + Math.sin(angle) * distance
                },
                color,
                size,
                duration,
                startTime: Date.now()
            }
            
            particles.push(particle)
            this.animateParticle(particle)
        }

        return particles
    }

    /**
     * 动画粒子
     */
    animateParticle(particle) {
        const animate = () => {
            const elapsed = Date.now() - particle.startTime
            const progress = Math.min(elapsed / particle.duration, 1)
            
            if (progress >= 1) {
                // 粒子动画完成
                return
            }

            // 更新粒子位置和透明度
            const currentPos = {
                x: particle.startPos.x + (particle.endPos.x - particle.startPos.x) * progress,
                y: particle.startPos.y + (particle.endPos.y - particle.startPos.y) * progress
            }
            
            const opacity = 1 - progress
            
            // 这里应该更新实际的粒子元素
            // 由于是小程序环境，可能需要通过回调来更新视图
            
            requestAnimationFrame(animate)
        }
        
        animate()
    }

    /**
     * 创建脉冲效果
     */
    createPulseEffect(element, options = {}) {
        const {
            duration = 1000,
            intensity = 0.3,
            color = '#FFFFFF'
        } = options

        const effectId = ++this.effectId
        let startTime = Date.now()
        
        const pulse = () => {
            const elapsed = Date.now() - startTime
            const progress = (elapsed % duration) / duration
            const pulseValue = Math.sin(progress * Math.PI * 2) * intensity
            const scale = 1 + pulseValue
            
            if (element && element.style) {
                element.style.transform = `scale(${scale})`
                element.style.boxShadow = `0 0 ${20 * (1 + pulseValue)}px ${this.hexToRgba(color, 0.6)}`
            }
            
            if (this.activeEffects.has(effectId)) {
                requestAnimationFrame(pulse)
            }
        }
        
        this.activeEffects.set(effectId, {
            element,
            type: 'pulse',
            animationId: requestAnimationFrame(pulse)
        })
        
        return effectId
    }

    /**
     * 停止指定效果
     */
    stopEffect(effectId) {
        if (this.activeEffects.has(effectId)) {
            const effect = this.activeEffects.get(effectId)
            
            if (effect.twinkleEffectId) {
                cancelAnimationFrame(effect.twinkleEffectId)
            }
            
            if (effect.animationId) {
                cancelAnimationFrame(effect.animationId)
            }
            
            this.activeEffects.delete(effectId)
            return true
        }
        return false
    }

    /**
     * 停止元素的所有效果
     */
    stopElementEffects(element) {
        const effectsToStop = []
        
        for (const [effectId, effect] of this.activeEffects) {
            if (effect.element === element) {
                effectsToStop.push(effectId)
            }
        }
        
        effectsToStop.forEach(id => this.stopEffect(id))
        return effectsToStop.length
    }

    /**
     * 停止所有效果
     */
    stopAllEffects() {
        const count = this.activeEffects.size
        
        for (const [effectId] of this.activeEffects) {
            this.stopEffect(effectId)
        }
        
        return count
    }

    /**
     * 十六进制颜色转RGBA
     */
    hexToRgba(hex, alpha = 1) {
        const r = parseInt(hex.slice(1, 3), 16)
        const g = parseInt(hex.slice(3, 5), 16)
        const b = parseInt(hex.slice(5, 7), 16)
        return `rgba(${r}, ${g}, ${b}, ${alpha})`
    }

    /**
     * 获取效果状态
     */
    getEffectStatus() {
        return {
            activeEffects: this.activeEffects.size,
            effects: Array.from(this.activeEffects.keys())
        }
    }

    /**
     * 销毁视觉效果管理器
     */
    destroy() {
        this.stopAllEffects()
        console.log('视觉效果管理器已销毁')
    }
}

// 创建全局实例
const visualEffectsManager = new VisualEffectsManager()

export default visualEffectsManager
