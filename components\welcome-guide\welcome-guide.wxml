<view class="guide-overlay">
  <view class="highlight-box" style="top: {{highlightPosition.top}}; left: {{highlightPosition.left}}; width: {{highlightPosition.width}}; height: {{highlightPosition.height}};"></view>
  <view class="guide-box" style="{{guideBoxStyle}}">
    <view class="guide-text">{{displayText}}<view wx:if="{{!showChoiceBtns}}" class="cursor"></view></view>
    <button wx:if="{{showContinueBtn}}" class="guide-btn" bindtap="handleNextStep">继续</button>
    <view wx:if="{{showChoiceBtns}}" class="choice-container">
      <button class="choice-btn" bindtap="handleChoice" data-choice="chat">就从聊天开始吧！</button>
      <button class="choice-btn" bindtap="handleChoice" data-choice="game">我想去玩游戏！</button>
      <button class="choice-btn" bindtap="handleChoice" data-choice="story">给我讲个故事吧~</button>
    </view>
  </view>
</view>