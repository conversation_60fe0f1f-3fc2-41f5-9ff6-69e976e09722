/**
 * Star Culling Manager
 * Handles performance optimization for large constellations through culling and LOD
 */

class StarCullingManager {
    constructor() {
        // Culling configuration
        this.cullingEnabled = true;
        this.cullingMargin = 50; // Extra pixels around viewport for smooth scrolling

        // LOD (Level of Detail) configuration
        this.lodEnabled = true;
        this.lodLevels = {
            high: { minZoom: 1.5, maxStars: 50 },
            medium: { minZoom: 0.8, maxStars: 100 },
            low: { minZoom: 0.0, maxStars: 200 }
        };

        // Performance monitoring
        this.performanceMetrics = {
            totalStars: 0,
            visibleStars: 0,
            culledStars: 0,
            renderTime: 0,
            lastFrameTime: 0
        };

        // Batch processing
        this.batchSize = 20; // Process stars in batches
        this.processingQueue = [];
        this.isProcessing = false;

        // Cache for visibility calculations
        this.visibilityCache = new Map();
        this.cacheTimeout = 100; // ms
        this.lastCacheUpdate = 0;
    }

    /**
     * Initialize the culling manager
     * @param {Object} options - Configuration options
     */
    initialize(options = {}) {
        this.cullingEnabled = options.cullingEnabled !== false;
        this.lodEnabled = options.lodEnabled !== false;
        this.cullingMargin = options.cullingMargin || this.cullingMargin;
        this.batchSize = options.batchSize || this.batchSize;

        console.log('StarCullingManager initialized:', {
            cullingEnabled: this.cullingEnabled,
            lodEnabled: this.lodEnabled,
            cullingMargin: this.cullingMargin,
            batchSize: this.batchSize
        });

        return { success: true };
    }

    /**
     * Cull stars based on viewport visibility
     * @param {Array} stars - Array of star objects
     * @param {Object} viewport - Viewport bounds
     * @param {Object} mapNavigation - Map navigation handler
     */
    cullStars(stars, viewport, mapNavigation) {
        const startTime = performance.now();

        if (!this.cullingEnabled || !stars || stars.length === 0) {
            return {
                visibleStars: stars || [],
                culledStars: [],
                metrics: this.performanceMetrics
            };
        }

        // Update performance metrics
        this.performanceMetrics.totalStars = stars.length;

        // Get visible bounds with margin
        const visibleBounds = this.getExpandedVisibleBounds(viewport, mapNavigation);

        // Check cache validity
        const now = Date.now();
        const cacheKey = this.generateCacheKey(visibleBounds, mapNavigation.getState());

        if (this.shouldUseCache(cacheKey, now)) {
            const cachedResult = this.visibilityCache.get(cacheKey);
            if (cachedResult) {
                this.performanceMetrics.renderTime = performance.now() - startTime;
                return cachedResult;
            }
        }

        // Perform culling
        const visibleStars = [];
        const culledStars = [];

        stars.forEach(star => {
            if (this.isStarVisible(star, visibleBounds, viewport)) {
                visibleStars.push(star);
            } else {
                culledStars.push(star);
            }
        });

        // Update metrics
        this.performanceMetrics.visibleStars = visibleStars.length;
        this.performanceMetrics.culledStars = culledStars.length;
        this.performanceMetrics.renderTime = performance.now() - startTime;

        const result = {
            visibleStars,
            culledStars,
            metrics: { ...this.performanceMetrics }
        };

        // Cache the result
        this.visibilityCache.set(cacheKey, result);
        this.lastCacheUpdate = now;

        // Clean old cache entries
        this.cleanCache(now);

        return result;
    }

    /**
     * Apply Level of Detail optimization
     * @param {Array} visibleStars - Visible stars array
     * @param {number} zoomLevel - Current zoom level
     */
    applyLOD(visibleStars, zoomLevel) {
        if (!this.lodEnabled || !visibleStars || visibleStars.length === 0) {
            return visibleStars.map(star => ({ ...star, lodLevel: 'high' }));
        }

        // Determine LOD level based on zoom
        let currentLOD = 'low';
        if (zoomLevel >= this.lodLevels.high.minZoom) {
            currentLOD = 'high';
        } else if (zoomLevel >= this.lodLevels.medium.minZoom) {
            currentLOD = 'medium';
        }

        const maxStars = this.lodLevels[currentLOD].maxStars;

        // If we have too many stars, prioritize by importance
        if (visibleStars.length > maxStars) {
            const prioritizedStars = this.prioritizeStars(visibleStars, maxStars);
            return prioritizedStars.map(star => ({ ...star, lodLevel: currentLOD }));
        }

        return visibleStars.map(star => ({ ...star, lodLevel: currentLOD }));
    }

    /**
     * Process stars in batches for smooth performance
     * @param {Array} stars - Stars to process
     * @param {Function} processor - Processing function
     * @param {Function} onComplete - Completion callback
     */
    processBatch(stars, processor, onComplete) {
        if (this.isProcessing) {
            // Queue the request
            this.processingQueue.push({ stars, processor, onComplete });
            return;
        }

        this.isProcessing = true;
        const batches = this.createBatches(stars, this.batchSize);
        let currentBatch = 0;

        const processBatch = () => {
            if (currentBatch >= batches.length) {
                this.isProcessing = false;
                if (onComplete) onComplete();

                // Process next item in queue
                if (this.processingQueue.length > 0) {
                    const next = this.processingQueue.shift();
                    this.processBatch(next.stars, next.processor, next.onComplete);
                }
                return;
            }

            const batch = batches[currentBatch];

            // Process batch
            try {
                batch.forEach(processor);
            } catch (error) {
                console.error('Batch processing error:', error);
            }

            currentBatch++;

            // Schedule next batch
            requestAnimationFrame(processBatch);
        };

        processBatch();
    }

    /**
     * Get expanded visible bounds with margin
     * @param {Object} viewport - Viewport bounds
     * @param {Object} mapNavigation - Map navigation handler
     */
    getExpandedVisibleBounds(viewport, mapNavigation) {
        const visibleBounds = mapNavigation.getVisibleBounds();

        return {
            left: visibleBounds.left - this.cullingMargin,
            top: visibleBounds.top - this.cullingMargin,
            right: visibleBounds.right + this.cullingMargin,
            bottom: visibleBounds.bottom + this.cullingMargin,
            width: visibleBounds.width + (this.cullingMargin * 2),
            height: visibleBounds.height + (this.cullingMargin * 2)
        };
    }

    /**
     * Check if a star is visible within bounds
     * @param {Object} star - Star object
     * @param {Object} bounds - Visible bounds
     * @param {Object} viewport - Viewport dimensions
     */
    isStarVisible(star, bounds, viewport) {
        if (!star.starStyle) return false;

        // Convert percentage position to absolute coordinates
        const starX = (star.starStyle.left / 100) * viewport.width;
        const starY = (star.starStyle.top / 100) * viewport.height;

        // Check if star is within visible bounds
        return (
            starX >= bounds.left &&
            starX <= bounds.right &&
            starY >= bounds.top &&
            starY <= bounds.bottom
        );
    }

    /**
     * Prioritize stars for LOD rendering
     * @param {Array} stars - Stars to prioritize
     * @param {number} maxStars - Maximum number of stars to keep
     */
    prioritizeStars(stars, maxStars) {
        // Priority factors:
        // 1. Recent stars (higher priority)
        // 2. Stars with diary entries
        // 3. Stars with strong emotions
        // 4. Distance from center

        const prioritizedStars = stars.map(star => {
            let priority = 0;

            // Recent stars get higher priority
            if (star.createTime) {
                const daysSinceCreation = (Date.now() - new Date(star.createTime).getTime()) / (1000 * 60 * 60 * 24);
                priority += Math.max(0, 100 - daysSinceCreation * 2);
            }

            // Stars with diary entries
            if (star.hasGeneratedDiary) {
                priority += 50;
            }

            // Strong emotions
            if (star.emotionKeyword) {
                const strongEmotions = ['开心', '快乐', '喜悦', '感动', '治愈'];
                if (strongEmotions.includes(star.emotionKeyword)) {
                    priority += 30;
                }
            }

            // Distance from center (closer = higher priority)
            if (star.starStyle) {
                const centerX = 50; // 50% is center
                const centerY = 50;
                const distance = Math.sqrt(
                    Math.pow(star.starStyle.left - centerX, 2) +
                    Math.pow(star.starStyle.top - centerY, 2)
                );
                priority += Math.max(0, 50 - distance);
            }

            return { ...star, priority };
        });

        // Sort by priority and take top stars
        return prioritizedStars
            .sort((a, b) => b.priority - a.priority)
            .slice(0, maxStars);
    }

    /**
     * Create batches from array
     * @param {Array} array - Array to batch
     * @param {number} batchSize - Size of each batch
     */
    createBatches(array, batchSize) {
        const batches = [];
        for (let i = 0; i < array.length; i += batchSize) {
            batches.push(array.slice(i, i + batchSize));
        }
        return batches;
    }

    /**
     * Generate cache key for visibility calculations
     * @param {Object} bounds - Visible bounds
     * @param {Object} navState - Navigation state
     */
    generateCacheKey(bounds, navState) {
        return `${bounds.left.toFixed(0)},${bounds.top.toFixed(0)},${bounds.right.toFixed(0)},${bounds.bottom.toFixed(0)},${navState.zoomLevel.toFixed(2)}`;
    }

    /**
     * Check if cache should be used
     * @param {string} cacheKey - Cache key
     * @param {number} now - Current timestamp
     */
    shouldUseCache(cacheKey, now) {
        return (
            this.visibilityCache.has(cacheKey) &&
            (now - this.lastCacheUpdate) < this.cacheTimeout
        );
    }

    /**
     * Clean old cache entries
     * @param {number} now - Current timestamp
     */
    cleanCache(now) {
        if ((now - this.lastCacheUpdate) > this.cacheTimeout * 10) {
            this.visibilityCache.clear();
        }
    }

    /**
     * Get performance metrics
     */
    getMetrics() {
        return {
            ...this.performanceMetrics,
            cacheSize: this.visibilityCache.size,
            queueLength: this.processingQueue.length,
            isProcessing: this.isProcessing
        };
    }

    /**
     * Update configuration
     * @param {Object} config - New configuration
     */
    updateConfig(config) {
        if (config.cullingEnabled !== undefined) {
            this.cullingEnabled = config.cullingEnabled;
        }
        if (config.lodEnabled !== undefined) {
            this.lodEnabled = config.lodEnabled;
        }
        if (config.cullingMargin !== undefined) {
            this.cullingMargin = config.cullingMargin;
        }
        if (config.batchSize !== undefined) {
            this.batchSize = config.batchSize;
        }
        if (config.lodLevels) {
            this.lodLevels = { ...this.lodLevels, ...config.lodLevels };
        }

        console.log('StarCullingManager configuration updated:', config);
    }

    /**
     * Reset performance metrics
     */
    resetMetrics() {
        this.performanceMetrics = {
            totalStars: 0,
            visibleStars: 0,
            culledStars: 0,
            renderTime: 0,
            lastFrameTime: 0
        };
    }

    /**
     * Enable/disable culling
     * @param {boolean} enabled - Whether to enable culling
     */
    setCullingEnabled(enabled) {
        this.cullingEnabled = enabled;
        if (!enabled) {
            this.visibilityCache.clear();
        }
    }

    /**
     * Enable/disable LOD
     * @param {boolean} enabled - Whether to enable LOD
     */
    setLODEnabled(enabled) {
        this.lodEnabled = enabled;
    }
}

module.exports = StarCullingManager;