page {
  background-color: #000;
  height: 100%;
  overflow: hidden;
}
.container {
  width: 100%;
  height: 100%;
  position: relative;
}
.background-image,
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}
.background-image {
  opacity: 0.5;
}
.overlay {
  background-color: rgba(0, 0, 0, 0.4);
}

/* 顶部导航栏 */
.custom-nav {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 10;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.4), transparent);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transform: translateY(-100%);
  transition: all 0.3s ease-out;
}
.custom-nav.visible {
  opacity: 1;
  transform: translateY(0);
}
.nav-back {
  position: absolute;
  left: 20rpx;
  bottom: 0;
  height: 100%;
  width: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.back-icon {
  width: 22rpx;
  height: 22rpx;
  border: solid #e0e0e0;
  border-width: 0 0 4rpx 4rpx;
  transform: rotate(45deg);
}
.nav-title {
  color: #e0e0e0;
  font-size: 32rpx;
  font-weight: 500;
}

/* 故事内容区 */
.story-scroll {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding-bottom: 140rpx;
  position: relative;
  z-index: 5;
}
.story-content-card {
  margin: 180rpx 40rpx 40rpx 40rpx;
  padding: 50rpx 40rpx;
  background-color: rgba(25, 25, 28, 0.85);
  border: 1px solid rgba(224, 190, 134, 0.4);
  border-radius: 20rpx;
  color: #eaeaea;
  backdrop-filter: blur(12px);
  position: relative;
  overflow: hidden;
}
.story-content-card::before {
  content: "";
  position: absolute;
  top: -100rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 300%;
  height: 200rpx;
  background: radial-gradient(
    circle,
    rgba(224, 190, 134, 0.2) 0%,
    transparent 60%
  );
}

.story-title {
  font-size: 44rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 30rpx;
  color: #f0e6d2;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.5);
}
.story-divider {
  width: 100rpx;
  height: 2rpx;
  background: linear-gradient(
    to right,
    transparent,
    rgba(224, 190, 134, 0.6),
    transparent
  );
  margin: 0 auto 50rpx auto;
  border: none;
}
.story-text {
  font-size: 32rpx;
  line-height: 2.1;
  text-align: justify;
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.4);
}
.paragraph {
  display: block;
  margin-bottom: 40rpx;
}

/* 章节切换 */
.chapter-nav {
  display: flex;
  justify-content: space-between;
  margin-top: 60rpx;
  padding-top: 40rpx;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}
.nav-btn {
  color: #e0be86;
  font-size: 28rpx;
  padding: 10rpx 20rpx;
}
.nav-btn.disabled {
  color: #666;
  opacity: 0.5;
}

/* 底部音频播放器 */
.audio-player {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 140rpx;
  background-color: rgba(10, 10, 10, 0.7);
  backdrop-filter: blur(15px);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  padding: 0 40rpx;
  box-sizing: border-box;
  z-index: 100;
}
.play-button {
  width: 80rpx;
  height: 80rpx;
  background-color: #e0be86;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow: 0 0 15rpx rgba(224, 190, 134, 0.6);
}
.icon-play,
.icon-pause {
  position: absolute;
  transition: opacity 0.2s;
}
.icon-play {
  width: 0;
  height: 0;
  border-top: 18rpx solid transparent;
  border-bottom: 18rpx solid transparent;
  border-left: 28rpx solid #333;
  margin-left: 6rpx;
  opacity: 1;
}
.icon-pause {
  width: 24rpx;
  height: 28rpx;
  border-left: 8rpx solid #333;
  border-right: 8rpx solid #333;
  opacity: 0;
}
.play-button.playing .icon-play {
  opacity: 0;
}
.play-button.playing .icon-pause {
  opacity: 1;
}

.progress-bar {
  flex: 1;
  display: flex;
  align-items: center;
  margin: 0 30rpx;
  color: #aaa;
  font-size: 22rpx;
}
.progress-track {
  flex: 1;
  height: 6rpx;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 3rpx;
  margin: 0 20rpx;
  position: relative;
  padding: 20rpx 0; /* 增加触摸区域 */
  margin-top: -20rpx;
  margin-bottom: -20rpx;
}
.progress-fill {
  position: absolute;
  left: 0;
  top: 20rpx;
  height: 6rpx;
  background-color: #e0be86;
  border-radius: 3rpx;
}
.progress-thumb {
  position: absolute;
  top: 14rpx;
  width: 18rpx;
  height: 18rpx;
  background-color: #e0be86;
  border-radius: 50%;
  transform: translateX(-50%);
  box-shadow: 0 0 8rpx rgba(224, 190, 134, 0.6);
  transition: transform 0.1s ease;
}
.progress-thumb:active {
  transform: translateX(-50%) scale(1.2);
}

.timer-button {
  width: 60rpx;
  height: 60rpx;
}
.timer-icon {
  width: 100%;
  height: 100%;
  opacity: 0.8;
}

/* 定时器选择弹窗 */
.action-sheet-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 101;
}
.action-sheet {
  position: fixed;
  bottom: -100%;
  left: 0;
  width: 100%;
  background-color: #252525;
  z-index: 102;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
  transition: bottom 0.3s ease-out;
}
.action-sheet.show {
  bottom: 0;
}
.sheet-item {
  padding: 35rpx 0;
  text-align: center;
  font-size: 32rpx;
  color: #eaeaea;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}
.sheet-item:active {
  background-color: rgba(255, 255, 255, 0.1);
}
.sheet-item.cancel {
  border-top: 8rpx solid #1c1c1c;
  color: #aaa;
}
