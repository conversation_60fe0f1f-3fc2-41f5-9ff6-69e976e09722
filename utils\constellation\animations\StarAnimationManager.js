/**
 * 星星动画管理器
 * 负责管理所有星星相关的动画效果
 */

class StarAnimationManager {
    constructor() {
        this.activeAnimations = new Map()
        this.animationQueue = []
        this.isAnimating = false
        this.animationId = null
        
        // 动画配置
        this.config = {
            starAppearance: {
                duration: 800,
                easing: 'easeOutBack',
                scale: { from: 0, to: 1 },
                opacity: { from: 0, to: 1 },
                rotation: { from: 0, to: 360 }
            },
            starPlacement: {
                duration: 600,
                easing: 'easeOutElastic',
                bounce: 0.3,
                scale: { from: 1.2, to: 1 }
            },
            orbitPath: {
                duration: 1000,
                easing: 'easeInOutQuad',
                strokeWidth: { from: 0, to: 2 },
                opacity: { from: 0, to: 0.6 }
            },
            starGlow: {
                duration: 2000,
                easing: 'easeInOutSine',
                intensity: { from: 0.5, to: 1.5 },
                loop: true
            }
        }
    }

    /**
     * 初始化动画管理器
     */
    initialize() {
        console.log('初始化星星动画管理器...')
        this.startAnimationLoop()
        return { success: true }
    }

    /**
     * 启动动画循环
     */
    startAnimationLoop() {
        const animate = (timestamp) => {
            this.updateAnimations(timestamp)
            this.animationId = requestAnimationFrame(animate)
        }
        this.animationId = requestAnimationFrame(animate)
    }

    /**
     * 停止动画循环
     */
    stopAnimationLoop() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId)
            this.animationId = null
        }
    }

    /**
     * 更新所有活动动画
     */
    updateAnimations(timestamp) {
        for (const [animationId, animation] of this.activeAnimations) {
            if (!animation.startTime) {
                animation.startTime = timestamp
            }

            const elapsed = timestamp - animation.startTime
            const progress = Math.min(elapsed / animation.duration, 1)
            const easedProgress = this.applyEasing(progress, animation.easing)

            // 更新动画属性
            this.updateAnimationProperties(animation, easedProgress)

            // 调用更新回调
            if (animation.onUpdate) {
                animation.onUpdate(animation.properties, easedProgress)
            }

            // 检查动画是否完成
            if (progress >= 1) {
                if (animation.loop) {
                    // 重新开始循环动画
                    animation.startTime = timestamp
                } else {
                    // 完成动画
                    if (animation.onComplete) {
                        animation.onComplete(animation.properties)
                    }
                    this.activeAnimations.delete(animationId)
                }
            }
        }
    }

    /**
     * 更新动画属性
     */
    updateAnimationProperties(animation, progress) {
        for (const [property, config] of Object.entries(animation.config)) {
            if (typeof config === 'object' && config.from !== undefined && config.to !== undefined) {
                const value = this.interpolate(config.from, config.to, progress)
                animation.properties[property] = value
            }
        }
    }

    /**
     * 线性插值
     */
    interpolate(from, to, progress) {
        return from + (to - from) * progress
    }

    /**
     * 应用缓动函数
     */
    applyEasing(progress, easing) {
        switch (easing) {
            case 'easeOutBack':
                const c1 = 1.70158
                const c3 = c1 + 1
                return 1 + c3 * Math.pow(progress - 1, 3) + c1 * Math.pow(progress - 1, 2)
            
            case 'easeOutElastic':
                const c4 = (2 * Math.PI) / 3
                return progress === 0 ? 0 : progress === 1 ? 1 :
                    Math.pow(2, -10 * progress) * Math.sin((progress * 10 - 0.75) * c4) + 1
            
            case 'easeInOutQuad':
                return progress < 0.5 ? 2 * progress * progress : 1 - Math.pow(-2 * progress + 2, 2) / 2
            
            case 'easeInOutSine':
                return -(Math.cos(Math.PI * progress) - 1) / 2
            
            default:
                return progress
        }
    }

    /**
     * 创建星星出现动画
     */
    animateStarAppearance(starElement, options = {}) {
        const config = { ...this.config.starAppearance, ...options }
        const animationId = `star_appearance_${Date.now()}_${Math.random()}`

        const animation = {
            id: animationId,
            element: starElement,
            duration: config.duration,
            easing: config.easing,
            loop: false,
            config: config,
            properties: {},
            onUpdate: (properties) => {
                this.applyStarAppearanceStyles(starElement, properties)
            },
            onComplete: (properties) => {
                if (options.onComplete) {
                    options.onComplete(properties)
                }
            }
        }

        this.activeAnimations.set(animationId, animation)
        return animationId
    }

    /**
     * 应用星星出现动画样式
     */
    applyStarAppearanceStyles(element, properties) {
        if (!element) return

        const transforms = []
        
        if (properties.scale !== undefined) {
            transforms.push(`scale(${properties.scale})`)
        }
        
        if (properties.rotation !== undefined) {
            transforms.push(`rotate(${properties.rotation}deg)`)
        }

        if (transforms.length > 0) {
            element.style.transform = transforms.join(' ')
        }

        if (properties.opacity !== undefined) {
            element.style.opacity = properties.opacity
        }
    }

    /**
     * 创建星星放置动画
     */
    animateStarPlacement(starElement, fromPosition, toPosition, options = {}) {
        const config = { ...this.config.starPlacement, ...options }
        const animationId = `star_placement_${Date.now()}_${Math.random()}`

        const animation = {
            id: animationId,
            element: starElement,
            duration: config.duration,
            easing: config.easing,
            loop: false,
            config: {
                x: { from: fromPosition.x, to: toPosition.x },
                y: { from: fromPosition.y, to: toPosition.y },
                scale: config.scale
            },
            properties: {},
            onUpdate: (properties) => {
                this.applyStarPlacementStyles(starElement, properties)
            },
            onComplete: (properties) => {
                if (options.onComplete) {
                    options.onComplete(properties)
                }
            }
        }

        this.activeAnimations.set(animationId, animation)
        return animationId
    }

    /**
     * 应用星星放置动画样式
     */
    applyStarPlacementStyles(element, properties) {
        if (!element) return

        const transforms = []
        
        if (properties.scale !== undefined) {
            transforms.push(`scale(${properties.scale})`)
        }

        if (transforms.length > 0) {
            element.style.transform = transforms.join(' ')
        }

        if (properties.x !== undefined && properties.y !== undefined) {
            element.style.left = `${properties.x}%`
            element.style.top = `${properties.y}%`
        }
    }

    /**
     * 创建轨道路径动画
     */
    animateOrbitPath(pathElement, options = {}) {
        const config = { ...this.config.orbitPath, ...options }
        const animationId = `orbit_path_${Date.now()}_${Math.random()}`

        const animation = {
            id: animationId,
            element: pathElement,
            duration: config.duration,
            easing: config.easing,
            loop: false,
            config: config,
            properties: {},
            onUpdate: (properties) => {
                this.applyOrbitPathStyles(pathElement, properties)
            },
            onComplete: (properties) => {
                if (options.onComplete) {
                    options.onComplete(properties)
                }
            }
        }

        this.activeAnimations.set(animationId, animation)
        return animationId
    }

    /**
     * 应用轨道路径动画样式
     */
    applyOrbitPathStyles(element, properties) {
        if (!element) return

        if (properties.strokeWidth !== undefined) {
            element.style.strokeWidth = `${properties.strokeWidth}px`
        }

        if (properties.opacity !== undefined) {
            element.style.opacity = properties.opacity
        }
    }

    /**
     * 创建星星发光动画
     */
    animateStarGlow(starElement, options = {}) {
        const config = { ...this.config.starGlow, ...options }
        const animationId = `star_glow_${Date.now()}_${Math.random()}`

        const animation = {
            id: animationId,
            element: starElement,
            duration: config.duration,
            easing: config.easing,
            loop: config.loop,
            config: config,
            properties: {},
            onUpdate: (properties) => {
                this.applyStarGlowStyles(starElement, properties)
            }
        }

        this.activeAnimations.set(animationId, animation)
        return animationId
    }

    /**
     * 应用星星发光动画样式
     */
    applyStarGlowStyles(element, properties) {
        if (!element) return

        if (properties.intensity !== undefined) {
            const glowSize = properties.intensity * 10
            element.style.boxShadow = `0 0 ${glowSize}px rgba(255, 255, 255, ${properties.intensity * 0.8})`
        }
    }

    /**
     * 停止指定动画
     */
    stopAnimation(animationId) {
        if (this.activeAnimations.has(animationId)) {
            const animation = this.activeAnimations.get(animationId)
            if (animation.onComplete) {
                animation.onComplete(animation.properties)
            }
            this.activeAnimations.delete(animationId)
            return true
        }
        return false
    }

    /**
     * 停止元素的所有动画
     */
    stopElementAnimations(element) {
        const animationsToStop = []
        
        for (const [animationId, animation] of this.activeAnimations) {
            if (animation.element === element) {
                animationsToStop.push(animationId)
            }
        }

        animationsToStop.forEach(id => this.stopAnimation(id))
        return animationsToStop.length
    }

    /**
     * 停止所有动画
     */
    stopAllAnimations() {
        const count = this.activeAnimations.size
        this.activeAnimations.clear()
        return count
    }

    /**
     * 获取动画状态
     */
    getAnimationStatus() {
        return {
            activeCount: this.activeAnimations.size,
            queueLength: this.animationQueue.length,
            isAnimating: this.isAnimating,
            animations: Array.from(this.activeAnimations.keys())
        }
    }

    /**
     * 销毁动画管理器
     */
    destroy() {
        this.stopAnimationLoop()
        this.stopAllAnimations()
        console.log('星星动画管理器已销毁')
    }
}

// 创建全局实例
const starAnimationManager = new StarAnimationManager()

export default starAnimationManager
