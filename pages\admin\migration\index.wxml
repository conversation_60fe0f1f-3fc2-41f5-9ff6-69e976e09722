<!--数据迁移管理页面-->
<view class="migration-admin">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">数据迁移管理</text>
    <button class="refresh-btn" bindtap="refreshAll" disabled="{{isLoading}}">
      <text class="iconfont icon-refresh"></text>
      刷新
    </button>
  </view>

  <!-- 迁移状态卡片 -->
  <view class="status-card">
    <view class="card-header">
      <text class="card-title">迁移状态</text>
      <view class="status-indicator {{migrationStatus.migrationRate === '100%' ? 'complete' : 'pending'}}">
        {{migrationStatus.migrationRate === '100%' ? '已完成' : '进行中'}}
      </view>
    </view>
    
    <view class="status-content">
      <view class="status-item">
        <text class="status-label">总记录数</text>
        <text class="status-value">{{migrationStatus.totalRecords}}</text>
      </view>
      <view class="status-item">
        <text class="status-label">已迁移</text>
        <text class="status-value success">{{migrationStatus.migratedRecords}}</text>
      </view>
      <view class="status-item">
        <text class="status-label">未迁移</text>
        <text class="status-value warning">{{migrationStatus.unmigratedRecords}}</text>
      </view>
      <view class="status-item">
        <text class="status-label">迁移率</text>
        <text class="status-value">{{migrationStatus.migrationRate}}</text>
      </view>
    </view>

    <!-- 进度条 -->
    <view class="progress-container">
      <view class="progress-bar">
        <view class="progress-fill" style="width: {{migrationStatus.migrationRate}}"></view>
      </view>
      <text class="progress-text">{{migrationStatus.migrationRate}}</text>
    </view>
  </view>

  <!-- 同步状态卡片 -->
  <view class="status-card">
    <view class="card-header">
      <text class="card-title">同步状态</text>
      <view class="sync-indicator {{syncStatus.isSyncing ? 'syncing' : 'idle'}}">
        {{syncStatus.isSyncing ? '同步中' : '空闲'}}
      </view>
    </view>
    
    <view class="status-content">
      <view class="status-item">
        <text class="status-label">队列长度</text>
        <text class="status-value">{{syncStatus.queueLength}}</text>
      </view>
      <view class="status-item">
        <text class="status-label">上次同步</text>
        <text class="status-value">{{syncStatus.lastSyncTime ? formatTime(syncStatus.lastSyncTime) : '从未同步'}}</text>
      </view>
      <view class="status-item">
        <text class="status-label">需要全量同步</text>
        <text class="status-value {{syncStatus.needFullSync ? 'warning' : 'success'}}">
          {{syncStatus.needFullSync ? '是' : '否'}}
        </text>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-section">
    <view class="action-group">
      <button class="action-btn primary" bindtap="performTestMigration" disabled="{{isLoading}}">
        测试迁移
      </button>
      <button class="action-btn danger" bindtap="performActualMigration" disabled="{{isLoading}}">
        执行迁移
      </button>
    </view>
    
    <view class="action-group">
      <button class="action-btn secondary" bindtap="forceFullSync" disabled="{{isLoading}}">
        强制同步
      </button>
      <button class="action-btn secondary" bindtap="clearLocalCache" disabled="{{isLoading}}">
        清理缓存
      </button>
    </view>
  </view>

  <!-- 迁移日志 -->
  <view class="logs-section">
    <view class="logs-header" bindtap="toggleLogs">
      <text class="logs-title">迁移日志 ({{logs.length}})</text>
      <text class="toggle-icon {{showLogs ? 'expanded' : ''}}">▼</text>
    </view>
    
    <view class="logs-content {{showLogs ? 'show' : 'hide'}}">
      <view wx:if="{{logs.length === 0}}" class="empty-logs">
        <text>暂无日志记录</text>
      </view>
      
      <view wx:for="{{logs}}" wx:key="_id" class="log-item">
        <view class="log-header">
          <text class="log-operation">{{item.operation}}</text>
          <text class="log-status {{item.status}}">{{item.status}}</text>
          <text class="log-time">{{formatTime(item.timestamp)}}</text>
        </view>
        
        <view class="log-details">
          <text class="log-content">{{formatLogContent(item)}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载遮罩 -->
  <view wx:if="{{isLoading}}" class="loading-overlay">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>
</view>
