page {
  background-color: #f4f4f8;
  height: 100vh;
}

.dialogue-summary-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.header {
  background-color: #6b7aff;
  color: white;
  padding: 30rpx;
  text-align: center;
}

.theme {
  font-size: 36rpx;
  font-weight: bold;
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.summary-section,
.three-questions-section,
.emotion-section,
.tomorrow-message-section {
  background-color: white;
  padding: 30rpx;
  margin: 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
}

.summary-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.three-questions-section .question-answer-item {
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.three-questions-section .question {
  display: block;
  font-size: 28rpx;
  color: #6b7aff;
  margin-bottom: 10rpx;
}

.three-questions-section .answer {
  display: block;
  font-size: 28rpx;
  color: #333;
}

.emotion-section .emotion-word {
  font-size: 48rpx;
  font-weight: bold;
  color: #6b7aff;
  text-align: center;
  display: block;
  margin: 20rpx 0;
}

.tomorrow-message-section .tomorrow-message {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  text-align: center;
  display: block;
}

.action-buttons {
  display: flex;
  justify-content: space-around;
  margin: 40rpx 20rpx;
}

.share-btn,
.home-btn {
  width: 40%;
  border-radius: 50rpx;
  padding: 20rpx;
  font-size: 32rpx;
}

.share-btn {
  background-color: #6b7aff;
  color: white;
}

.home-btn {
  background-color: white;
  color: #6b7aff;
  border: 2rpx solid #6b7aff;
} 