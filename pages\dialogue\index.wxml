<view class="dialogue-container">
  <image 
    class="background" 
    src="cloud://cloudbase-8gji862jcfb501e7.636c-cloudbase-8gji862jcfb501e7-1365531166/images/homeBG.png" 
    mode="aspectFill" 
  />

  <!-- 前置七问阶段 -->
  <view wx:if="{{showPreQuestions}}" class="pre-questions-container">
    <view class="pre-questions-header">
      <text class="pre-questions-title">在开始对话前，让我先了解一下你</text>
      <text class="pre-questions-progress">{{currentQuestionIndex + 1}}/{{preQuestions.length}}</text>
    </view>
    
    <view class="pre-question-content">
      <view class="pre-question-text">{{preQuestions[currentQuestionIndex].question}}</view>
      
      <view class="pre-options-container">
        <block wx:for="{{preQuestions[currentQuestionIndex].options}}" wx:key="index">
          <view 
            class="pre-option-item"
            bindtap="selectPreAnswer"
            data-index="{{index}}"
          >
            {{item}}
          </view>
        </block>
      </view>
    </view>
  </view>

  <!-- 主题选择阶段 -->
  <view wx:elif="{{!dialogueStarted}}" class="theme-selection-container">
    <view class="points-display">
      <text class="points-text">光点余额：{{userPoints}}</text>
      <text class="points-cost">（本次对话需要{{config.pointsCost}}个光点）</text>
    </view>
    <view class="theme-title">选择本次对话的主题</view>
    <view class="theme-grid">
      <block wx:for="{{dialogueThemes}}" wx:key="index">
        <view 
          class="theme-item {{selectedTheme === item ? 'selected' : ''}}"
          bindtap="selectTheme"
          data-theme="{{item}}"
        >
          {{item}}
        </view>
      </block>
    </view>
  </view>

  <!-- 对话主界面 -->
  <view wx:else class="dialogue-main">
    <view class="dialogue-header">
      <view class="dialogue-header-left">
        <text class="dialogue-theme">{{dialogueTheme}}</text>
        <text class="user-level-display">{{userLevelName}} (Level {{userLevel}})</text>
      </view>
      <text class="dialogue-round">第 {{dialogueRound}}/{{config.maxDialogueRound}} 轮</text>
    </view>

    <scroll-view 
      scroll-y 
      class="dialogue-content" 
      scroll-with-animation="true"
      enable-back-to-top="true"
      scroll-into-view="{{scrollToView}}"
      scroll-top="{{scrollTop}}"
      scroll-anchoring="true"
      enhanced="true"
      show-scrollbar="true"
      enable-passive="false"
      refresher-enabled="false"
    >
      <block wx:for="{{dialogueContent}}" wx:key="index" wx:for-item="message">
        <!-- 系统消息 -->
        <view wx:if="{{message.role === 'system'}}" 
          id="msg-{{index}}" 
          class="dialogue-message system-message"
        >
          {{message.content}}
        </view>
        <!-- 普通对话消息 -->
        <view wx:else
          id="msg-{{index}}" 
          class="dialogue-message-wrapper {{message.role === 'user' ? 'user-message-wrapper' : 'ai-message-wrapper'}}"
        >
          <view class="dialogue-avatar {{message.role === 'user' ? 'user-avatar' : 'ai-avatar'}}">
            {{message.role === 'user' ? '我' : 'AI'}}
          </view>
          <view 
            class="dialogue-message {{message.role === 'user' ? 'user-message' : 'ai-message'}}"
          >
            {{message.content}}
          </view>
        </view>
      </block>
    </scroll-view>

    <!-- 结束语显示区域 -->
    <view wx:if="{{showEndingMessage}}" class="ending-message-container">
      <view class="ending-message-content">
        <view class="ending-message-avatar">
          <text class="avatar-text">微光</text>
        </view>
        <view class="ending-message-bubble">
          <view wx:if="{{isGeneratingEnding}}" class="ending-generating">
            <text class="generating-text">正在为你准备温暖的结束语...</text>
            <view class="generating-dots">
              <view class="dot dot1"></view>
              <view class="dot dot2"></view>
              <view class="dot dot3"></view>
            </view>
          </view>
          <view wx:else class="ending-message-text">
            {{endingMessage}}
          </view>
        </view>
      </view>
      
      <view wx:if="{{!isGeneratingEnding && endingMessage}}" class="ending-actions">
        <view class="ending-guide-text">
          <text>愿意和我一起做个简单的对话回顾吗？</text>
        </view>
        <view class="ending-buttons">
          <view class="ending-btn ending-btn-secondary" bindtap="skipThreeQuestions">
            <text>暂时不了</text>
          </view>
          <view class="ending-btn ending-btn-primary" bindtap="proceedToThreeQuestions">
            <text>好的，开始吧</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 输入区域 -->
    <view wx:if="{{!showEndingMessage}}" class="dialogue-input-container">
      <input 
        class="dialogue-input" 
        placeholder="请输入你的想法..." 
        value="{{inputText}}"
        bindinput="onInputChange"
        disabled="{{isInputDisabled}}"
        confirm-type="send"
        bindconfirm="sendMessage"
        return-key-type="send"
      />
      <view 
        class="send-btn" 
        bindtap="sendMessage"
        style="opacity: {{isInputDisabled ? 0.3 : 1}}"
      >
        <text style="color: white;">⏎</text>
      </view>
    </view>

    <!-- 打字状态提示 -->
    <view wx:if="{{isTyping}}" class="typing-status">
      <text>AI正在输入...</text>
    </view>
  </view>
</view>
