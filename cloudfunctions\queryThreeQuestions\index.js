const cloud = require('wx-server-sdk')

cloud.init({
    env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 验证查询参数
function validateQueryParams(params) {
    const errors = []

    if (params.limit && (!Number.isInteger(params.limit) || params.limit < 1 || params.limit > 100)) {
        errors.push('limit 必须是1-100之间的整数')
    }

    if (params.skip && (!Number.isInteger(params.skip) || params.skip < 0)) {
        errors.push('skip 必须是非负整数')
    }

    if (params.startDate && !(params.startDate instanceof Date) && typeof params.startDate !== 'string') {
        errors.push('startDate 必须是日期类型或日期字符串')
    }

    if (params.endDate && !(params.endDate instanceof Date) && typeof params.endDate !== 'string') {
        errors.push('endDate 必须是日期类型或日期字符串')
    }

    if (params.theme && typeof params.theme !== 'string') {
        errors.push('theme 必须是字符串类型')
    }

    if (params.completed !== undefined && typeof params.completed !== 'boolean') {
        errors.push('completed 必须是布尔类型')
    }

    return { valid: errors.length === 0, errors }
}

// 构建查询条件
function buildQueryConditions(openid, params) {
    const conditions = { openid }

    if (params.theme) {
        conditions.theme = params.theme
    }

    if (params.completed !== undefined) {
        conditions.completed = params.completed
    }

    if (params.wantDiary !== undefined) {
        conditions.wantDiary = params.wantDiary
    }

    if (params.startDate || params.endDate) {
        conditions.createTime = {}
        if (params.startDate) {
            conditions.createTime[_.gte] = new Date(params.startDate)
        }
        if (params.endDate) {
            conditions.createTime[_.lte] = new Date(params.endDate)
        }
    }

    return conditions
}

exports.main = async (event, context) => {
    console.log('查询三问记录请求:', JSON.stringify(event))

    const {
        queryType = 'list',  // 查询类型: list, single, count, stats
        recordId,            // 单个记录ID
        limit = 20,          // 分页限制
        skip = 0,            // 跳过数量
        orderBy = 'createTime', // 排序字段
        orderDirection = 'desc', // 排序方向

        // 过滤条件
        theme,               // 对话主题
        startDate,           // 开始日期
        endDate,             // 结束日期
        completed,           // 是否完成
        wantDiary,           // 是否想要日记

        // 字段选择
        fields               // 要返回的字段数组
    } = event

    const wxContext = cloud.getWXContext()
    const openid = wxContext.OPENID

    if (!openid) {
        return {
            success: false,
            error: '用户身份验证失败',
            errorType: 'AUTH_ERROR'
        }
    }

    try {
        // 验证查询参数
        const validation = validateQueryParams({ limit, skip, startDate, endDate, theme, completed })
        if (!validation.valid) {
            return {
                success: false,
                error: '查询参数验证失败',
                validationErrors: validation.errors,
                errorType: 'VALIDATION_ERROR'
            }
        }

        const collection = db.collection('three_questions_records')

        switch (queryType) {
            case 'single':
                // 查询单个记录
                if (!recordId) {
                    return {
                        success: false,
                        error: '查询单个记录时必须提供 recordId',
                        errorType: 'MISSING_PARAM'
                    }
                }

                const singleResult = await collection.doc(recordId).get()

                if (!singleResult.data || singleResult.data.openid !== openid) {
                    return {
                        success: false,
                        error: '记录不存在或无权访问',
                        errorType: 'NOT_FOUND'
                    }
                }

                return {
                    success: true,
                    data: singleResult.data,
                    queryType: 'single'
                }

            case 'count':
                // 统计记录数量
                const conditions = buildQueryConditions(openid, { theme, startDate, endDate, completed, wantDiary })
                const countResult = await collection.where(conditions).count()

                return {
                    success: true,
                    count: countResult.total,
                    queryType: 'count'
                }

            case 'stats':
                // 统计分析
                const statsConditions = buildQueryConditions(openid, { startDate, endDate })
                const statsResult = await collection.where(statsConditions).get()

                const stats = {
                    totalRecords: statsResult.data.length,
                    completedRecords: statsResult.data.filter(r => r.completed).length,
                    diaryGenerated: statsResult.data.filter(r => r.diaryGenerated).length,
                    withTomorrowMessage: statsResult.data.filter(r => r.tomorrowMessage && r.tomorrowMessage.trim().length > 0).length,
                    withDailyFeeling: statsResult.data.filter(r => r.dailyFeeling && r.dailyFeeling.trim().length > 0).length,
                    averageWordCount: 0,
                    themes: {}
                }

                // 计算平均字数
                const diaryRecords = statsResult.data.filter(r => r.diaryContent)
                if (diaryRecords.length > 0) {
                    stats.averageWordCount = Math.round(
                        diaryRecords.reduce((sum, r) => sum + (r.diaryWordCount || r.diaryContent.length), 0) / diaryRecords.length
                    )
                }

                // 统计主题分布
                statsResult.data.forEach(record => {
                    if (record.theme) {
                        stats.themes[record.theme] = (stats.themes[record.theme] || 0) + 1
                    }
                })

                return {
                    success: true,
                    stats,
                    queryType: 'stats'
                }

            case 'list':
            default:
                // 列表查询
                const listConditions = buildQueryConditions(openid, { theme, startDate, endDate, completed, wantDiary })

                let query = collection.where(listConditions)

                // 排序
                if (orderBy && ['createTime', 'completedTime', 'theme', 'userLevel'].includes(orderBy)) {
                    query = query.orderBy(orderBy, orderDirection === 'asc' ? 'asc' : 'desc')
                } else {
                    query = query.orderBy('createTime', 'desc')
                }

                // 分页
                if (skip > 0) {
                    query = query.skip(skip)
                }
                query = query.limit(limit)

                // 字段选择
                if (fields && Array.isArray(fields) && fields.length > 0) {
                    const fieldObj = {}
                    fields.forEach(field => {
                        fieldObj[field] = true
                    })
                    query = query.field(fieldObj)
                }

                const listResult = await query.get()

                return {
                    success: true,
                    data: listResult.data,
                    count: listResult.data.length,
                    hasMore: listResult.data.length === limit,
                    queryType: 'list',
                    pagination: {
                        limit,
                        skip,
                        total: listResult.data.length
                    }
                }
        }

    } catch (error) {
        console.error('查询三问记录失败:', error)

        return {
            success: false,
            error: error.message,
            errorCode: error.errCode,
            errorType: 'DATABASE_ERROR',
            context: {
                openid,
                queryType,
                recordId
            }
        }
    }
}