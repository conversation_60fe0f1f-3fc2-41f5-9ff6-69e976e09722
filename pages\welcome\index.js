const welcomeQuotes = require('./welcomeQuotes.js');
const dailyQuotes = require('./dailyQuotes.js');

const shareTitles = [
  "我抽到了一句话，觉得你也会喜欢。",
  "从宇宙里抽到的一句话，我想与你分享。",
  "这句话我反复看了三遍。",
  "不一定适合所有人，但如果你点进来，它可能就是为你写的。",
  "你点进来也许会遇见那一束正在等你的微光。",
  "点进来看看你今天的微光卡片是什么。"
];

let bgm = null;

Page({
  data: {
    userInfo: null,
    hasUserInfo: false
  },

  onLoad() {
    // 只做登录状态检查，跳转逻辑全部以lightName为唯一条件
    const hasUserInfo = wx.getStorageSync('hasUserInfo');
    if (hasUserInfo) {
      this.checkUserLogin();
    }
  },

  // 检查用户登录状态
  async checkUserLogin() {
    try {
      const { result } = await wx.cloud.callFunction({
        name: 'user',
        data: {
          type: 'getUserInfo'
        }
      })

      if (result.success) {
        this.setData({
          userInfo: result.data,
          hasUserInfo: true
        })
        // 跳转逻辑只看lightName
        if (!result.data.lightName) {
          wx.redirectTo({ url: '/pages/lightup/index' })
        } else {
          wx.redirectTo({ url: '/pages/home/<USER>' })
        }
      } else {
        wx.clearStorageSync();
      }
    } catch (error) {
      console.error('检查登录状态失败：', error)
      wx.clearStorageSync();
    }
  },

  // 处理用户登录
  async handleUserLogin(e) {
    try {
      // 显示加载提示
      wx.showLoading({
        title: '登录中...'
      })

      // 获取用户信息
      const { userInfo } = await wx.getUserProfile({
        desc: '用于完善会员资料'
      })

      // 获取naming页面存储的lightName和userName
      const lightName = wx.getStorageSync('lightName') || '';
      const userName = wx.getStorageSync('userName') || '';

      // 调用登录云函数
      const { result } = await wx.cloud.callFunction({
        name: 'user',
        data: {
          type: 'login',
          userInfo,
          lightName, // 新增
          userName   // 新增
        }
      })

      if (result.success) {
        // 保存登录状态
        wx.setStorageSync('hasUserInfo', true);
        wx.setStorageSync('userInfo', userInfo);

        this.setData({
          userInfo: result.data,
          hasUserInfo: true
        })

        wx.showToast({
          title: '登录成功',
          icon: 'success'
        })

        // 登录成功后根据lightName跳转
        setTimeout(() => {
          if (!result.data.lightName) {
            wx.redirectTo({
              url: '/pages/lightup/index'
            })
          } else {
            wx.redirectTo({
              url: '/pages/home/<USER>'
            })
          }
        }, 1500)
      }
    } catch (error) {
      console.error('登录失败：', error)
      wx.showToast({
        title: '登录失败',
        icon: 'error'
      })
    } finally {
      wx.hideLoading()
    }
  }
});
