<view class="vip-container">
  <view class="vip-header">
    <text class="title">微光 VIP 会员</text>
    <view class="vip-status">
      <text wx:if="{{isVip}}">会员有效期至：{{utils.formatDate(vipExpireTime)}}</text>
      <text wx:else>当前不是会员</text>
    </view>
  </view>

  <view class="vip-privileges">
    <text class="privileges-title">会员专属特权</text>
    <view class="privileges-list">
      <block wx:for="{{vipPrivileges}}" wx:key="name">
        <view class="privilege-item">
          <text class="privilege-name">{{item.name}}</text>
          <text class="privilege-value">{{item.value}}</text>
        </view>
      </block>
    </view>
  </view>

  <view class="vip-packages">
    <text class="packages-title">选择会员套餐</text>
    <view class="packages-list">
      <block wx:for="{{packages}}" wx:key="months">
        <view 
          class="package-item {{selectedPackage.months === item.months ? 'selected' : ''}}"
          data-package="{{item}}"
          bindtap="selectPackage"
        >
          <text class="package-label">{{item.label}}</text>
          <text class="package-price">¥{{item.price}}</text>
          <text wx:if="{{item.discount > 0}}" class="package-discount">
            立减{{item.discount * 100}}%
          </text>
        </view>
      </block>
    </view>
  </view>

  <button 
    class="purchase-btn" 
    bindtap="purchaseVip"
  >
    立即开通
  </button>
</view>

<wxs module="utils">
module.exports = {
  formatDate: function(timestamp) {
    if (!timestamp) return '未开通';
    var date = getDate(timestamp);
    return date.getFullYear() + '-' + 
           (date.getMonth() + 1).toString().padStart(2, '0') + '-' + 
           date.getDate().toString().padStart(2, '0')
  }
} 