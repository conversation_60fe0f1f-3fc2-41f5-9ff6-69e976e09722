Page({
  data: {
    dialogueTheme: '',
    aiSummary: '',
    emotionWord: '',
    tomorrowMessage: ''
  },

  onLoad() {
    // 从缓存中获取对话数据
    const dialogueData = wx.getStorageSync('dialogueData')
    
    if (dialogueData) {
      this.setData({
        dialogueTheme: dialogueData.dialogueTheme,
        aiSummary: dialogueData.aiSummary
      })
      
      // 清除缓存
      wx.removeStorageSync('dialogueData')
    } else {
      // 如果没有缓存数据，返回上一页
      wx.navigateBack()
    }
  },

  // 保存情感词
  bindEmotionWordInput(e) {
    this.setData({
      emotionWord: e.detail.value
    })
  },

  // 保存明日寄语
  bindTomorrowMessageInput(e) {
    this.setData({
      tomorrowMessage: e.detail.value
    })
  },

  // 完成三问仪式并保存日记
  finishThreeQuestions() {
    // 检查VIP状态
    const isVip = wx.getStorageSync('isVip') || false

    if (!isVip) {
      wx.showModal({
        title: '提示',
        content: '生成日记需要VIP权限，是否开通？',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/vip/index'
            })
          }
        }
      })
      return
    }

    // 校验数据
    if (!this.data.emotionWord || !this.data.tomorrowMessage) {
      wx.showToast({
        title: '请填写完整信息',
        icon: 'none'
      })
      return
    }

    // 调用云函数保存日记
    wx.cloud.callFunction({
      name: 'createStarTrackDiary',
      data: {
        openid: wx.getStorageSync('openid'),
        aiSummary: this.data.aiSummary,
        emotionWord: this.data.emotionWord,
        tomorrowMessage: this.data.tomorrowMessage,
        dialogueTheme: this.data.dialogueTheme
      }
    }).then(res => {
      if (res.result.success) {
        wx.showToast({
          title: '日记保存成功',
          icon: 'success'
        })
        
        // 跳转到日记页面
        wx.redirectTo({
          url: '/pages/journal/store/index'
        })
      } else {
        wx.showToast({
          title: '日记保存失败',
          icon: 'none'
        })
      }
    }).catch(err => {
      console.error('创建星轨日记错误', err)
      wx.showToast({
        title: '日记保存失败',
        icon: 'none'
      })
    })
  }
}) 