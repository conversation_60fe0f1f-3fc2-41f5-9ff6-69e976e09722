const cloud = require('wx-server-sdk');

cloud.init({
    env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
    const { action, charId, chapId, cost } = event;
    const wxContext = cloud.getWXContext();
    const userId = wxContext.OPENID;

    try {
        switch (action) {
            case 'getUserPurchases':
                // 获取用户购买记录
                return await getUserPurchases(userId);

            case 'purchaseChapter':
                // 购买章节
                return await purchaseChapter(userId, charId, chapId, cost);

            case 'checkChapterAccess':
                // 检查章节访问权限
                return await checkChapterAccess(userId, charId, chapId);

            case 'getUserPoints':
                // 获取用户光点余额
                return await getUserPoints(userId);

            default:
                return { success: false, error: '未知操作' };
        }
    } catch (error) {
        console.error('userPurchase error:', error);
        return { success: false, error: error.message };
    }
};

// 获取用户购买记录
async function getUserPurchases(userId) {
    try {
        // 查找或创建用户记录
        let userRecord = await findOrCreateUser(userId);

        return {
            success: true,
            data: {
                purchases: userRecord.purchases || [],
                points: userRecord.points || 0
            }
        };
    } catch (error) {
        console.error('获取用户购买记录失败:', error);
        return { success: false, error: error.message };
    }
}

// 购买章节
async function purchaseChapter(userId, charId, chapId, cost) {
    try {
        // 查找或创建用户记录
        let userRecord = await findOrCreateUser(userId);

        // 检查光点余额
        if (userRecord.points < cost) {
            return { success: false, error: '光点余额不足' };
        }

        // 检查是否已购买
        const purchaseKey = `${charId}_${chapId}`;
        if (userRecord.purchases && userRecord.purchases.includes(purchaseKey)) {
            return { success: false, error: '章节已购买' };
        }

        // 扣除光点并添加购买记录
        const newPoints = userRecord.points - cost;
        const newPurchases = userRecord.purchases || [];
        newPurchases.push(purchaseKey);

        await db.collection('users').doc(userRecord._id).update({
            data: {
                points: newPoints,
                purchases: newPurchases,
                updatedAt: new Date()
            }
        });

        return {
            success: true,
            message: '购买成功',
            data: {
                remainingPoints: newPoints,
                purchasedChapter: purchaseKey
            }
        };
    } catch (error) {
        console.error('购买章节失败:', error);
        return { success: false, error: error.message };
    }
}

// 检查章节访问权限
async function checkChapterAccess(userId, charId, chapId) {
    try {
        // 首先检查章节是否免费
        const storyRes = await db.collection('stories').doc(charId).get();
        if (!storyRes.data) {
            return { success: false, error: '角色不存在' };
        }

        const chapter = storyRes.data.chapters.find(ch => ch.id === chapId);
        if (!chapter) {
            return { success: false, error: '章节不存在' };
        }

        // 如果是免费章节，直接允许访问
        if (chapter.isFree) {
            return { success: true, hasAccess: true, reason: 'free' };
        }

        // 检查用户是否已购买
        const userRecord = await findOrCreateUser(userId);
        const purchaseKey = `${charId}_${chapId}`;
        const hasPurchased = userRecord.purchases && userRecord.purchases.includes(purchaseKey);

        return {
            success: true,
            hasAccess: hasPurchased,
            reason: hasPurchased ? 'purchased' : 'locked',
            cost: chapter.cost
        };
    } catch (error) {
        console.error('检查章节访问权限失败:', error);
        return { success: false, error: error.message };
    }
}

// 获取用户光点余额
async function getUserPoints(userId) {
    try {
        const userRecord = await findOrCreateUser(userId);
        return {
            success: true,
            data: {
                points: userRecord.points || 0
            }
        };
    } catch (error) {
        console.error('获取用户光点失败:', error);
        return { success: false, error: error.message };
    }
}

// 查找或创建用户记录 - 使用 _openid
async function findOrCreateUser(userId) {
    try {
        // 先尝试查找用户 - 直接查询，云数据库会自动匹配 _openid
        const userRes = await db.collection('users').get();

        if (userRes.data.length > 0) {
            return userRes.data[0];
        }

        // 用户不存在，创建新用户
        const newUser = {
            points: 10, // 新用户赠送10个光点
            purchases: [],
            createdAt: new Date(),
            updatedAt: new Date()
        };

        const createRes = await db.collection('users').add({
            data: newUser
        });

        return {
            _id: createRes._id,
            _openid: userId,
            ...newUser
        };
    } catch (error) {
        console.error('查找或创建用户失败:', error);
        throw error;
    }
}