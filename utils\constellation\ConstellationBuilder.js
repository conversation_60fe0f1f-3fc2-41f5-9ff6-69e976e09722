/**
 * Constellation Builder - Core orbital calculation logic
 */

const ORBITAL_CONFIG = {
    RADIUS_PERCENT: 0.15,
    MIN_RADIUS_PX: 50,
    MAX_RADIUS_PX: 200,
    SCREEN_MARGIN_PERCENT: 0.1,
    RADIUS_ADJUSTMENT_FACTOR: 0.8
};

const INTERACTION_CONFIG = {
    FIRST_STAR: {
        EDGE_MARGIN_PERCENT: 0.1
    }
};

class ConstellationBuilder {
    constructor() {
        this.screenBounds = { width: 0, height: 0 };
        this.constellationData = null;
    }

    /**
     * Calculate position for first star (free placement)
     */
    calculateFirstStarPosition(touchPoint, screenBounds) {
        try {
            const percentageX = touchPoint.x / screenBounds.width;
            const percentageY = touchPoint.y / screenBounds.height;
            const margin = INTERACTION_CONFIG.FIRST_STAR.EDGE_MARGIN_PERCENT;

            const constrainedPosition = {
                x: Math.max(margin, Math.min(1 - margin, percentageX)),
                y: Math.max(margin, Math.min(1 - margin, percentageY))
            };

            const validation = this.validateFirstStarPlacement(constrainedPosition, screenBounds);

            if (!validation.valid) {
                return {
                    success: false,
                    error: validation.errors.join(', '),
                    suggestedPosition: validation.suggestedPosition
                };
            }

            return {
                success: true,
                position: {
                    x: constrainedPosition.x,
                    y: constrainedPosition.y,
                    anchorStarId: null,
                    orbitRadius: null,
                    orbitAngle: null
                }
            };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    /**
     * Calculate orbital position for subsequent stars
     * @param {Object} anchorStar - The anchor star to orbit around
     * @param {number} starIndex - Index of the star (unused but kept for compatibility)
     * @param {Object} screenBounds - Screen dimensions
     * @param {number} [fixedAngle] - Optional fixed angle for testing (in radians)
     */
    calculateOrbitPosition(anchorStar, starIndex, screenBounds, fixedAngle = null) {
        try {
            if (!anchorStar || !anchorStar.position) {
                throw new Error('Invalid anchor star provided');
            }

            let orbitRadius = screenBounds.width * ORBITAL_CONFIG.RADIUS_PERCENT;
            orbitRadius = Math.max(ORBITAL_CONFIG.MIN_RADIUS_PX, orbitRadius);
            orbitRadius = Math.min(ORBITAL_CONFIG.MAX_RADIUS_PX, orbitRadius);

            const anchorPixelX = anchorStar.position.x * screenBounds.width;
            const anchorPixelY = anchorStar.position.y * screenBounds.height;
            const randomAngle = fixedAngle !== null ? fixedAngle : Math.random() * 2 * Math.PI;

            const adjustedResult = this._adjustOrbitForScreenBounds(
                anchorPixelX, anchorPixelY, orbitRadius, randomAngle, screenBounds
            );

            const percentageX = adjustedResult.x / screenBounds.width;
            const percentageY = adjustedResult.y / screenBounds.height;

            return {
                success: true,
                position: {
                    x: percentageX,
                    y: percentageY,
                    anchorStarId: anchorStar.id,
                    orbitRadius: adjustedResult.radius,
                    orbitAngle: randomAngle
                },
                orbitInfo: {
                    centerX: anchorStar.position.x,
                    centerY: anchorStar.position.y,
                    radius: adjustedResult.radius / screenBounds.width,
                    angle: randomAngle
                }
            };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    /**
     * Adjust orbital radius to keep star within screen bounds
     */
    _adjustOrbitForScreenBounds(anchorX, anchorY, originalRadius, angle, screenBounds) {
        // Use appropriate margin for each axis
        const marginX = screenBounds.width * ORBITAL_CONFIG.SCREEN_MARGIN_PERCENT;
        const marginY = screenBounds.height * ORBITAL_CONFIG.SCREEN_MARGIN_PERCENT;
        let adjustedRadius = originalRadius;

        let x = anchorX + originalRadius * Math.cos(angle);
        let y = anchorY + originalRadius * Math.sin(angle);

        const exceedsLeft = x < marginX;
        const exceedsRight = x > (screenBounds.width - marginX);
        const exceedsTop = y < marginY;
        const exceedsBottom = y > (screenBounds.height - marginY);

        if (exceedsLeft || exceedsRight || exceedsTop || exceedsBottom) {
            const constraints = [];

            // Calculate X-axis constraints
            if (Math.cos(angle) !== 0) {
                if (Math.cos(angle) > 0) {
                    // Moving right - constrain by right edge
                    constraints.push(Math.abs((screenBounds.width - marginX - anchorX) / Math.cos(angle)));
                } else {
                    // Moving left - constrain by left edge
                    constraints.push(Math.abs((anchorX - marginX) / Math.cos(angle)));
                }
            }

            // Calculate Y-axis constraints
            if (Math.sin(angle) !== 0) {
                if (Math.sin(angle) > 0) {
                    // Moving down - constrain by bottom edge
                    constraints.push(Math.abs((screenBounds.height - marginY - anchorY) / Math.sin(angle)));
                } else {
                    // Moving up - constrain by top edge
                    constraints.push(Math.abs((anchorY - marginY) / Math.sin(angle)));
                }
            }

            if (constraints.length > 0) {
                const maxAllowedRadius = Math.min(...constraints.filter(r => r > 0));
                adjustedRadius = Math.min(originalRadius, maxAllowedRadius * ORBITAL_CONFIG.RADIUS_ADJUSTMENT_FACTOR);
                adjustedRadius = Math.max(ORBITAL_CONFIG.MIN_RADIUS_PX, adjustedRadius);
            }

            // Recalculate position with adjusted radius
            x = anchorX + adjustedRadius * Math.cos(angle);
            y = anchorY + adjustedRadius * Math.sin(angle);

            // Final safety check - ensure we're still within bounds
            x = Math.max(marginX, Math.min(screenBounds.width - marginX, x));
            y = Math.max(marginY, Math.min(screenBounds.height - marginY, y));
        }

        return { x: x, y: y, radius: adjustedRadius };
    }

    /**
     * Generate orbital path for dragging visualization
     */
    generateOrbitPath(centerPoint, radius) {
        const points = [];
        const segments = 64;

        for (let i = 0; i <= segments; i++) {
            const angle = (i / segments) * 2 * Math.PI;
            points.push({
                x: centerPoint.x + radius * Math.cos(angle),
                y: centerPoint.y + radius * Math.sin(angle)
            });
        }

        return { points: points, center: centerPoint, radius: radius };
    }

    /**
     * Validate first star placement with enhanced validation and position suggestions
     */
    validateFirstStarPlacement(position, screenBounds) {
        const margin = INTERACTION_CONFIG.FIRST_STAR.EDGE_MARGIN_PERCENT;
        const errors = [];
        const warnings = [];

        // Input validation
        if (!position || typeof position !== 'object') {
            errors.push('Position must be a valid object');
            return {
                valid: false,
                errors: errors,
                warnings: warnings,
                suggestedPosition: this._generateCenterPosition()
            };
        }

        if (typeof position.x !== 'number' || typeof position.y !== 'number' ||
            isNaN(position.x) || isNaN(position.y)) {
            errors.push('Position coordinates must be valid numbers');
            return {
                valid: false,
                errors: errors,
                warnings: warnings,
                suggestedPosition: this._generateCenterPosition()
            };
        }

        // Range validation
        if (position.x < 0 || position.x > 1) {
            errors.push('Position X must be between 0 and 1');
        }
        if (position.y < 0 || position.y > 1) {
            errors.push('Position Y must be between 0 and 1');
        }

        // Margin validation with detailed feedback
        const tooCloseToLeft = position.x < margin;
        const tooCloseToRight = position.x > (1 - margin);
        const tooCloseToTop = position.y < margin;
        const tooCloseToBottom = position.y > (1 - margin);

        if (tooCloseToLeft || tooCloseToRight) {
            errors.push(`Star must be at least ${margin * 100}% from left/right edges`);
        }
        if (tooCloseToTop || tooCloseToBottom) {
            errors.push(`Star must be at least ${margin * 100}% from top/bottom edges`);
        }

        // Generate warnings for positions that are valid but not optimal
        const nearEdgeThreshold = margin + 0.05; // 5% additional buffer for warnings
        if (position.x < nearEdgeThreshold || position.x > (1 - nearEdgeThreshold)) {
            warnings.push('Position is close to screen edge, consider moving towards center');
        }
        if (position.y < nearEdgeThreshold || position.y > (1 - nearEdgeThreshold)) {
            warnings.push('Position is close to screen edge, consider moving towards center');
        }

        // Generate suggested position if validation fails
        let suggestedPosition = null;
        if (errors.length > 0) {
            suggestedPosition = this._generateSuggestedPosition(position, screenBounds, margin);
        }

        return {
            valid: errors.length === 0,
            errors: errors,
            warnings: warnings,
            suggestedPosition: suggestedPosition,
            placementInfo: {
                distanceFromEdges: {
                    left: position.x,
                    right: 1 - position.x,
                    top: position.y,
                    bottom: 1 - position.y
                },
                isOptimalPlacement: warnings.length === 0 && errors.length === 0
            }
        };
    }

    /**
     * Generate suggested position when user placement is invalid
     */
    _generateSuggestedPosition(originalPosition, screenBounds, margin) {
        // Try to keep the position as close to original as possible while making it valid
        let suggestedX = originalPosition.x;
        let suggestedY = originalPosition.y;

        // Adjust X coordinate
        if (originalPosition.x < margin) {
            suggestedX = margin + 0.05; // Add small buffer
        } else if (originalPosition.x > (1 - margin)) {
            suggestedX = (1 - margin) - 0.05; // Subtract small buffer
        }

        // Adjust Y coordinate
        if (originalPosition.y < margin) {
            suggestedY = margin + 0.05; // Add small buffer
        } else if (originalPosition.y > (1 - margin)) {
            suggestedY = (1 - margin) - 0.05; // Subtract small buffer
        }

        // If the original position was completely invalid, suggest multiple alternatives
        const alternatives = [];

        // Center position (safest option)
        alternatives.push({
            position: { x: 0.5, y: 0.5 },
            reason: 'Center position - safest placement',
            priority: 1
        });

        // Golden ratio positions (aesthetically pleasing)
        const goldenRatio = 0.618;
        alternatives.push({
            position: { x: goldenRatio, y: goldenRatio },
            reason: 'Golden ratio position - aesthetically pleasing',
            priority: 2
        });
        alternatives.push({
            position: { x: 1 - goldenRatio, y: goldenRatio },
            reason: 'Golden ratio position - aesthetically pleasing',
            priority: 2
        });

        // Adjusted original position
        alternatives.push({
            position: { x: suggestedX, y: suggestedY },
            reason: 'Closest valid position to your original choice',
            priority: 3
        });

        return {
            primary: { x: suggestedX, y: suggestedY },
            alternatives: alternatives,
            explanation: 'Position adjusted to meet placement requirements'
        };
    }

    /**
     * Generate center position as fallback
     */
    _generateCenterPosition() {
        return {
            primary: { x: 0.5, y: 0.5 },
            alternatives: [
                {
                    position: { x: 0.5, y: 0.5 },
                    reason: 'Center position - default safe placement',
                    priority: 1
                }
            ],
            explanation: 'Default center position due to invalid input'
        };
    }

    /**
     * Get optimal placement suggestions for different screen sizes
     */
    getOptimalPlacementSuggestions(screenBounds) {
        const suggestions = [];
        const margin = INTERACTION_CONFIG.FIRST_STAR.EDGE_MARGIN_PERCENT;

        // Calculate safe zone
        const safeZone = {
            left: margin + 0.05,
            right: 1 - margin - 0.05,
            top: margin + 0.05,
            bottom: 1 - margin - 0.05
        };

        // Screen size specific suggestions
        const aspectRatio = screenBounds.width / screenBounds.height;

        if (aspectRatio > 1.5) {
            // Wide screen (landscape)
            suggestions.push(
                { x: 0.3, y: 0.5, reason: 'Left-center for wide screens' },
                { x: 0.7, y: 0.5, reason: 'Right-center for wide screens' }
            );
        } else if (aspectRatio < 0.8) {
            // Tall screen (portrait)
            suggestions.push(
                { x: 0.5, y: 0.3, reason: 'Upper-center for tall screens' },
                { x: 0.5, y: 0.7, reason: 'Lower-center for tall screens' }
            );
        } else {
            // Square-ish screen
            suggestions.push(
                { x: 0.4, y: 0.4, reason: 'Upper-left quadrant' },
                { x: 0.6, y: 0.6, reason: 'Lower-right quadrant' }
            );
        }

        // Always include center as safe option
        suggestions.push({ x: 0.5, y: 0.5, reason: 'Center - always safe' });

        // Filter suggestions to ensure they're within safe zone
        return suggestions.filter(suggestion =>
            suggestion.x >= safeZone.left && suggestion.x <= safeZone.right &&
            suggestion.y >= safeZone.top && suggestion.y <= safeZone.bottom
        );
    }

    /**
     * Validate star placement within screen bounds
     */
    validateStarPlacement(position, screenBounds) {
        const errors = [];
        const margin = ORBITAL_CONFIG.SCREEN_MARGIN_PERCENT;

        if (typeof position.x !== 'number' || position.x < 0 || position.x > 1) {
            errors.push('Position X must be between 0 and 1');
        }
        if (typeof position.y !== 'number' || position.y < 0 || position.y > 1) {
            errors.push('Position Y must be between 0 and 1');
        }
        if (position.x < margin || position.x > (1 - margin)) {
            errors.push(`Position X must be at least ${margin * 100}% from screen edges`);
        }
        if (position.y < margin || position.y > (1 - margin)) {
            errors.push(`Position Y must be at least ${margin * 100}% from screen edges`);
        }

        return { valid: errors.length === 0, errors: errors };
    }

    /**
     * Save constellation state (mock implementation)
     */
    async saveConstellationData(userOpenid, starPositions) {
        try {
            // Mock implementation - in real app this would save to cloud database
            return { success: true, message: 'Constellation data saved successfully' };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }
}

module.exports = ConstellationBuilder;