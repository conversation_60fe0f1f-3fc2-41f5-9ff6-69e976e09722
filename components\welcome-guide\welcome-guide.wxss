:host {
    --gold-color: #EAD3A3;
    --gold-glow: rgba(234, 211, 163, 0.5);
  }
  .guide-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 999;
    pointer-events: none;
  }
  .highlight-box {
    position: fixed;
    border-radius: 32rpx;
    box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.75), 
                inset 0 0 12px 2px var(--gold-glow);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    animation: highlightPulse 2.5s infinite ease-in-out;
  }
  @keyframes highlightPulse {
    0%, 100% {
      box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.75), inset 0 0 12px 2px var(--gold-glow);
    }
    50% {
      box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.75), inset 0 0 24px 4px var(--gold-glow);
    }
  }
  .guide-box {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    transition: top 0.5s cubic-bezier(0.4, 0, 0.2, 1), bottom 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    width: 90%;
    max-width: 620rpx;
    background: linear-gradient(135deg, rgba(45, 38, 28, 0.5), rgba(25, 22, 18, 0.6));
    backdrop-filter: blur(24px) saturate(150%);
    border-radius: 32rpx;
    padding: 48rpx;
    border: 1px solid rgba(234, 211, 163, 0.3);
    box-shadow: 0 16rpx 40rpx rgba(0,0,0,0.3);
    text-align: center;
    z-index: 1001;
    pointer-events: auto;
  }
  .guide-text {
    min-height: 180rpx;
    text-align: left;
    font-size: 30rpx;
    color: #fdf6ec;
    line-height: 1.8;
    margin-bottom: 48rpx;
    white-space: pre-wrap;
  }
  .cursor {
    display: inline-block;
    width: 4rpx;
    height: 32rpx;
    background-color: var(--gold-color);
    animation: blink 1s infinite;
    margin-left: 8rpx;
    vertical-align: text-bottom;
    border-radius: 2rpx;
  }
  @keyframes blink {
    50% { opacity: 0; }
  }
  .guide-btn, .choice-btn {
    border-radius: 50rpx;
    font-size: 30rpx;
    padding: 22rpx 0;
    width: 100%;
    transition: all 0.2s ease-in-out;
    border: none;
  }
  .guide-btn {
    background: var(--gold-color);
    color: #503D20;
    font-weight: bold;
    box-shadow: 0 4rpx 12rpx rgba(234, 211, 163, 0.4);
  }
  .guide-btn:active {
    transform: scale(0.98);
    opacity: 0.9;
  }
  .choice-container {
    display: flex;
    flex-direction: column;
    gap: 24rpx;
  }
  .choice-btn {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--gold-color);
    border: 1px solid var(--gold-color);
  }