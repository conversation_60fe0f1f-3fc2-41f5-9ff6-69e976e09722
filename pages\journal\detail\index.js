Page({
  data: {
    diaryDetail: null,
    dialogueContent: [],
    isVip: false
  },

  onLoad(options) {
    // 从上一个页面传递的日记ID
    const diaryId = options.id
    
    // 检查VIP状态
    this.checkVipStatus()
    
    // 获取日记详情
    this.fetchDiaryDetail(diaryId)
  },

  // 检查VIP状态
  checkVipStatus() {
    wx.cloud.callFunction({
      name: 'vipManager',
      data: {
        type: 'checkVipStatus',
        openid: wx.getStorageSync('openid')
      }
    }).then(res => {
      if (res.result.success) {
        this.setData({
          isVip: res.result.isVip
        })
      }
    })
  },

  // 获取日记详情
  fetchDiaryDetail(diaryId) {
    wx.cloud.callFunction({
      name: 'getStarTrackDiary',
      data: {
        type: 'getDiaryDetail',
        diaryId: diaryId,
        openid: wx.getStorageSync('openid')
      }
    }).then(res => {
      if (res.result.success) {
        const diaryDetail = res.result.diary
        const dialogueContent = JSON.parse(diaryDetail.dialogueContent)

        this.setData({
          diaryDetail,
          dialogueContent
        })
      } else {
        wx.showToast({
          title: '获取日记详情失败',
          icon: 'none'
        })
      }
    }).catch(err => {
      console.error('获取日记详情错误', err)
      wx.showToast({
        title: '网络错误',
        icon: 'none'
      })
    })
  },

  // 解锁完整对话（仅VIP）
  unlockFullDialogue() {
    if (!this.data.isVip) {
      wx.showModal({
        title: '提示',
        content: '查看完整对话需要开通VIP，是否立即开通？',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/vip/index'
            })
          }
        }
      })
    }
  },

  // 分享日记
  onShareAppMessage() {
    return {
      title: `来自微光的${this.data.diaryDetail.dialogueTheme}日记`,
      path: `/pages/journal/detail/index?id=${this.data.diaryDetail._id}`
    }
  }
}) 