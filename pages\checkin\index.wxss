/* pages/checkin/index.wxss */
.container {
    width: 100vw;
    height: 100vh;
    position: relative;
    overflow: hidden;
    background-color: #000;
  }
  
  .background-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
  }
  
  .header {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    padding-top: var(--status-bar-height);
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
  }
  
  .header-title {
    color: rgba(255, 255, 255, 0.9);
    font-size: 17px;
    font-weight: 600;
    text-shadow: 0 1px 3px rgba(0,0,0,0.3);
  }
  
  .close-button {
    position: absolute;
    right: 20px;
    top: var(--status-bar-height);
    height: 44px;
    width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28px;
    color: rgba(255, 255, 255, 0.8);
    font-family: Arial, sans-serif;
  }
  
  .stars-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    z-index: 5;
  }
  
  .star-item {
    position: absolute;
    width: 100rpx;
    height: 100rpx;
    transform: translate(-50%, -50%);
    transition: transform 0.3s ease;
  }
  
  .star-item.special {
    width: 140rpx;
    height: 140rpx;
  }
  
  .star-item image {
    width: 100%;
    height: 100%;
    filter: drop-shadow(0 0 10rpx rgba(255, 215, 0, 0.3));
  }
  
  /* --- 核心动画效果 --- */
  
  /* 呼吸光晕动画：应用于当天待点亮的水晶 */
  .breathing {
    animation: breath 2.5s ease-in-out infinite;
  }
  @keyframes breath {
    0%, 100% {
      transform: translate(-50%, -50%) scale(1);
      filter: drop-shadow(0 0 20rpx rgba(255, 223, 130, 0.5));
    }
    50% {
      transform: translate(-50%, -50%) scale(1.1);
      filter: drop-shadow(0 0 40rpx rgba(255, 223, 130, 0.8));
    }
  }
  
  /* 普通点亮瞬间的放大弹出动画 */
  .pop-out {
    animation: pop-out 0.8s ease-in-out forwards;
  }
  @keyframes pop-out {
    0% { transform: translate(-50%, -50%) scale(1); }
    50% { transform: translate(-50%, -50%) scale(1.5); }
    100% { transform: translate(-50%, -50%) scale(1); }
  }
  
  /* 【V1.5新增】第七天大奖专属动画 */
  .grand-prize {
    animation: grand-prize-effect 1.5s ease-in-out forwards;
  }
  @keyframes grand-prize-effect {
    0% {
      transform: translate(-50%, -50%) scale(1);
      filter: drop-shadow(0 0 20rpx rgba(255, 223, 130, 0.8));
    }
    30% {
      transform: translate(-50%, -50%) scale(0.8);
      filter: drop-shadow(0 0 10rpx rgba(255, 255, 255, 0.5));
    }
    80% {
      transform: translate(-50%, -50%) scale(1.8);
      filter: drop-shadow(0 0 80rpx rgba(255, 223, 130, 1)) drop-shadow(0 0 120rpx rgba(255, 255, 255, 0.7));
    }
    100% {
      transform: translate(-50%, -50%) scale(1);
      filter: drop-shadow(0 0 30rpx rgba(255, 223, 130, 0.7));
    }
  }
  
  /* 【V1.5新增】屏幕震动效果 */
  .screen-shake {
    animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
  }
  @keyframes shake {
    10%, 90% { transform: translate3d(-1px, 0, 0); }
    20%, 80% { transform: translate3d(2px, 0, 0); }
    30%, 50%, 70% { transform: translate3d(-4px, 0, 0); }
    40%, 60% { transform: translate3d(4px, 0, 0); }
  }
  
  
  .action-area {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 0 40rpx 60rpx;
    box-sizing: border-box;
    text-align: center;
    z-index: 10;
  }
  
  .action-title {
    color: rgba(255, 255, 255, 0.7);
    font-size: 13px;
    margin-bottom: 15px;
  }
  
  .checkin-button {
    background: linear-gradient(135deg, #FEEAAB, #E8C16D);
    color: #604512;
    border-radius: 50rpx;
    font-weight: bold;
    font-size: 17px;
    box-shadow: 0 4px 20px rgba(254, 234, 171, 0.35);
    transition: all 0.2s ease;
  }
  .checkin-button:active {
    transform: scale(0.98);
    opacity: 0.9;
  }
  .checkin-button[disabled] {
    background: #888;
    color: #ccc;
    box-shadow: none;
  }
  
  /* --- 奖励弹窗样式 --- */
  .reward-modal-mask {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    z-index: 998;
    display: flex;
    justify-content: center;
    align-items: center;
    backdrop-filter: blur(8rpx);
  }
  
  .reward-modal-content {
    width: 80vw;
    max-width: 300px;
    background: linear-gradient(160deg, #3a3a3a, #1e1e1e);
    border-radius: 20rpx;
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 40rpx;
    box-sizing: border-box;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.5);
  }
  
  .reward-title {
    font-size: 20px;
    font-weight: bold;
    color: #FEEAAB;
    margin-bottom: 20rpx;
  }
  
  .reward-message {
    font-size: 16px;
    color: #fff;
    margin-bottom: 50rpx;
    line-height: 1.6;
  }
  
  .reward-confirm-button {
    background: linear-gradient(135deg, #FEEAAB, #E8C16D);
    color: #604512;
    border-radius: 40rpx;
    font-weight: bold;
    font-size: 16px;
  }
  
  /* 弹窗进入动画 */
  .swing-in-top-fwd {
      animation: swing-in-top-fwd 0.5s cubic-bezier(0.175, 0.885, 0.320, 1.275) both;
  }
  @keyframes swing-in-top-fwd {
    0% {
      transform: rotateX(-100deg);
      transform-origin: top;
      opacity: 0;
    }
    100% {
      transform: rotateX(0deg);
      transform-origin: top;
      opacity: 1;
    }
  }
  