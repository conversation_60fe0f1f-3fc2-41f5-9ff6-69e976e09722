/**
 * 星座恢复管理器
 * 负责处理数据损坏和系统恢复
 */

class ConstellationRecovery {
    constructor() {
        this.recoveryStrategies = {
            dataCorruption: 'restore_from_backup',
            networkFailure: 'use_local_cache',
            validationFailure: 'sanitize_and_retry',
            systemError: 'reset_to_safe_state'
        }
        
        this.backupData = null
        this.recoveryHistory = []
        this.maxRecoveryAttempts = 3
    }

    /**
     * 初始化恢复管理器
     */
    initialize() {
        console.log('初始化星座恢复管理器...')
        
        // 加载备份数据
        this.loadBackupData()
        
        // 清理旧的恢复记录
        this.cleanupRecoveryHistory()
        
        return { success: true }
    }

    /**
     * 执行数据恢复
     */
    async performRecovery(errorType, errorData = {}) {
        try {
            console.log(`开始执行恢复操作: ${errorType}`)
            
            const strategy = this.recoveryStrategies[errorType] || 'reset_to_safe_state'
            const recoveryId = `recovery_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
            
            // 记录恢复开始
            this.recordRecoveryAttempt(recoveryId, errorType, strategy, 'started')
            
            let recoveryResult
            
            switch (strategy) {
                case 'restore_from_backup':
                    recoveryResult = await this.restoreFromBackup(errorData)
                    break
                    
                case 'use_local_cache':
                    recoveryResult = await this.useLocalCache(errorData)
                    break
                    
                case 'sanitize_and_retry':
                    recoveryResult = await this.sanitizeAndRetry(errorData)
                    break
                    
                case 'reset_to_safe_state':
                    recoveryResult = await this.resetToSafeState(errorData)
                    break
                    
                default:
                    throw new Error(`未知的恢复策略: ${strategy}`)
            }
            
            // 记录恢复结果
            this.recordRecoveryAttempt(recoveryId, errorType, strategy, 
                recoveryResult.success ? 'success' : 'failed', recoveryResult)
            
            return {
                success: recoveryResult.success,
                recoveryId: recoveryId,
                strategy: strategy,
                result: recoveryResult
            }
            
        } catch (error) {
            console.error('恢复操作失败:', error)
            return {
                success: false,
                error: error.message,
                strategy: 'unknown'
            }
        }
    }

    /**
     * 从备份恢复数据
     */
    async restoreFromBackup(errorData) {
        try {
            console.log('从备份恢复数据...')
            
            if (!this.backupData) {
                console.warn('没有可用的备份数据')
                return { success: false, error: '没有备份数据' }
            }

            // 验证备份数据
            const validation = this.validateBackupData(this.backupData)
            if (!validation.valid) {
                console.error('备份数据验证失败:', validation.errors)
                return { success: false, error: '备份数据损坏' }
            }

            // 恢复数据到本地存储
            wx.setStorageSync('starRecords', this.backupData.starRecords || [])
            wx.setStorageSync('constellation_cache', this.backupData.constellationCache || {})
            
            console.log('从备份恢复数据成功')
            return {
                success: true,
                restoredItems: {
                    starRecords: this.backupData.starRecords?.length || 0,
                    cacheItems: Object.keys(this.backupData.constellationCache || {}).length
                }
            }
            
        } catch (error) {
            console.error('从备份恢复失败:', error)
            return { success: false, error: error.message }
        }
    }

    /**
     * 使用本地缓存
     */
    async useLocalCache(errorData) {
        try {
            console.log('使用本地缓存数据...')
            
            const cachedData = {
                starRecords: wx.getStorageSync('starRecords') || [],
                constellationCache: wx.getStorageSync('constellation_cache') || {}
            }

            // 验证缓存数据
            if (cachedData.starRecords.length === 0) {
                return { success: false, error: '本地缓存为空' }
            }

            console.log('本地缓存数据可用')
            return {
                success: true,
                cacheData: cachedData,
                itemCount: cachedData.starRecords.length
            }
            
        } catch (error) {
            console.error('使用本地缓存失败:', error)
            return { success: false, error: error.message }
        }
    }

    /**
     * 清理并重试
     */
    async sanitizeAndRetry(errorData) {
        try {
            console.log('清理数据并重试...')
            
            // 获取原始数据
            const originalData = errorData.data || {}
            
            // 清理数据
            const sanitizedData = this.sanitizeData(originalData)
            
            // 验证清理后的数据
            const validation = this.validateSanitizedData(sanitizedData)
            if (!validation.valid) {
                return { success: false, error: '数据清理后仍然无效' }
            }

            console.log('数据清理成功')
            return {
                success: true,
                sanitizedData: sanitizedData,
                removedFields: validation.removedFields || []
            }
            
        } catch (error) {
            console.error('数据清理失败:', error)
            return { success: false, error: error.message }
        }
    }

    /**
     * 重置到安全状态
     */
    async resetToSafeState(errorData) {
        try {
            console.log('重置到安全状态...')
            
            // 清除可能损坏的数据
            const keysToReset = [
                'constellation_cache',
                'performance_cache',
                'animation_cache'
            ]

            keysToReset.forEach(key => {
                try {
                    wx.removeStorageSync(key)
                } catch (error) {
                    console.warn(`清除 ${key} 失败:`, error)
                }
            })

            // 设置安全的默认状态
            const safeState = {
                zoomLevel: 1.0,
                mapTransform: 'translate(0px, 0px) scale(1)',
                isPlacementMode: false,
                selectedRecord: null
            }

            console.log('系统已重置到安全状态')
            return {
                success: true,
                safeState: safeState,
                resetKeys: keysToReset
            }
            
        } catch (error) {
            console.error('重置到安全状态失败:', error)
            return { success: false, error: error.message }
        }
    }

    /**
     * 创建数据备份
     */
    async createBackup() {
        try {
            console.log('创建数据备份...')
            
            const backupData = {
                starRecords: wx.getStorageSync('starRecords') || [],
                constellationCache: wx.getStorageSync('constellation_cache') || {},
                userPreferences: wx.getStorageSync('user_preferences') || {},
                timestamp: Date.now(),
                version: '1.0.0'
            }

            this.backupData = backupData
            
            // 保存备份到本地
            wx.setStorageSync('constellation_backup', backupData)
            
            console.log('数据备份创建成功')
            return { success: true, backupSize: JSON.stringify(backupData).length }
            
        } catch (error) {
            console.error('创建备份失败:', error)
            return { success: false, error: error.message }
        }
    }

    /**
     * 加载备份数据
     */
    loadBackupData() {
        try {
            const backupData = wx.getStorageSync('constellation_backup')
            if (backupData) {
                this.backupData = backupData
                console.log('备份数据加载成功')
            }
        } catch (error) {
            console.error('加载备份数据失败:', error)
        }
    }

    /**
     * 验证备份数据
     */
    validateBackupData(backupData) {
        const errors = []
        
        if (!backupData || typeof backupData !== 'object') {
            errors.push('备份数据格式无效')
            return { valid: false, errors }
        }

        if (!Array.isArray(backupData.starRecords)) {
            errors.push('星星记录数据无效')
        }

        if (!backupData.timestamp || Date.now() - backupData.timestamp > 7 * 24 * 60 * 60 * 1000) {
            errors.push('备份数据过期')
        }

        return { valid: errors.length === 0, errors }
    }

    /**
     * 清理数据
     */
    sanitizeData(data) {
        if (!data || typeof data !== 'object') {
            return {}
        }

        const sanitized = {}
        
        // 只保留安全的字段
        const safeFields = [
            'id', 'theme', 'summary', 'emotionKeyword', 
            'createTime', 'starPosition', 'x', 'y'
        ]

        for (const [key, value] of Object.entries(data)) {
            if (safeFields.includes(key) && value !== null && value !== undefined) {
                // 基本类型直接复制
                if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {
                    sanitized[key] = value
                }
                // 对象类型递归清理
                else if (typeof value === 'object') {
                    sanitized[key] = this.sanitizeData(value)
                }
            }
        }

        return sanitized
    }

    /**
     * 验证清理后的数据
     */
    validateSanitizedData(data) {
        const errors = []
        const removedFields = []

        // 基本验证逻辑
        if (data.id && typeof data.id !== 'string') {
            errors.push('ID字段类型错误')
        }

        if (data.starPosition) {
            if (typeof data.starPosition.x !== 'number' || typeof data.starPosition.y !== 'number') {
                errors.push('星星位置数据无效')
            }
        }

        return { 
            valid: errors.length === 0, 
            errors, 
            removedFields 
        }
    }

    /**
     * 记录恢复尝试
     */
    recordRecoveryAttempt(recoveryId, errorType, strategy, status, result = null) {
        const record = {
            recoveryId,
            errorType,
            strategy,
            status,
            result,
            timestamp: Date.now()
        }

        this.recoveryHistory.push(record)
        
        // 限制历史记录数量
        if (this.recoveryHistory.length > 50) {
            this.recoveryHistory = this.recoveryHistory.slice(-50)
        }

        console.log(`恢复记录: ${recoveryId} - ${status}`)
    }

    /**
     * 清理恢复历史
     */
    cleanupRecoveryHistory() {
        const cutoffTime = Date.now() - 24 * 60 * 60 * 1000 // 24小时前
        
        const originalCount = this.recoveryHistory.length
        this.recoveryHistory = this.recoveryHistory.filter(record => record.timestamp > cutoffTime)
        
        const cleanedCount = originalCount - this.recoveryHistory.length
        if (cleanedCount > 0) {
            console.log(`清理了 ${cleanedCount} 条过期恢复记录`)
        }
    }

    /**
     * 获取恢复统计
     */
    getRecoveryStats() {
        const stats = {
            totalAttempts: this.recoveryHistory.length,
            successfulRecoveries: 0,
            failedRecoveries: 0,
            strategiesUsed: {},
            recentRecoveries: []
        }

        this.recoveryHistory.forEach(record => {
            if (record.status === 'success') {
                stats.successfulRecoveries++
            } else if (record.status === 'failed') {
                stats.failedRecoveries++
            }

            stats.strategiesUsed[record.strategy] = (stats.strategiesUsed[record.strategy] || 0) + 1
        })

        // 最近的恢复记录
        stats.recentRecoveries = this.recoveryHistory
            .slice(-5)
            .map(record => ({
                errorType: record.errorType,
                strategy: record.strategy,
                status: record.status,
                timestamp: record.timestamp
            }))

        return stats
    }

    /**
     * 检查系统健康状态
     */
    checkSystemHealth() {
        const health = {
            status: 'healthy',
            issues: [],
            recommendations: []
        }

        try {
            // 检查存储可用性
            wx.getStorageSync('test_key')
            wx.setStorageSync('test_key', 'test_value')
            wx.removeStorageSync('test_key')
        } catch (error) {
            health.status = 'warning'
            health.issues.push('本地存储异常')
            health.recommendations.push('重启应用或清理存储')
        }

        // 检查备份数据
        if (!this.backupData) {
            health.issues.push('缺少备份数据')
            health.recommendations.push('创建数据备份')
        }

        // 检查恢复历史
        const recentFailures = this.recoveryHistory
            .filter(record => record.status === 'failed' && Date.now() - record.timestamp < 60 * 60 * 1000)
            .length

        if (recentFailures > 3) {
            health.status = 'critical'
            health.issues.push('频繁恢复失败')
            health.recommendations.push('检查系统稳定性')
        }

        return health
    }

    /**
     * 销毁恢复管理器
     */
    destroy() {
        this.backupData = null
        this.recoveryHistory = []
        console.log('星座恢复管理器已销毁')
    }
}

// 创建全局实例
const constellationRecovery = new ConstellationRecovery()

export default constellationRecovery
