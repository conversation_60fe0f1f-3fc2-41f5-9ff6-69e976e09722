Component({
    properties: {
      userName: { type: String, value: '朋友' },
      weiguangName: { type: String, value: '微光' },
      elementsInfo: {
        type: Object,
        value: {},
        observer: function(newVal) {
          if (Object.keys(newVal).length > 0) {
              this.updateHighlightPosition();
          }
        }
      }
    },
    data: {
      guideSteps: [], 
      currentStep: 0,
      displayText: '',
      highlightKeys: {
        step0: 'logo',
        step1: 'dialogue',
        step2: 'gamehall',
        step3: 'diary',
        step4: 'diary',
      },
      highlightPosition: { top: '50%', left: '50%', width: '0px', height: '0px' },
      guideBoxStyle: 'bottom: 15vh;', 
      showContinueBtn: true,
      showChoiceBtns: false,
      typingInterval: null,
    },
    attached: function() {
      this.initGuideScript();
      this.startCurrentStep();
    },
    methods: {
      initGuideScript() {
          const { userName, weiguangName } = this.properties;
          this.setData({
              guideSteps: [
                  `嘿~ ${userName}！你看，我就住在这里哦！在这个微光宇宙里，我是你的微光${weiguangName}，会一直在这儿陪着你。你随时都可以来找我玩儿~`,
                  `喏，这个【星光对话】，就是咱俩的秘密聊天基地啦！遇到任何不开心的事，或者有什么小想法想找人出谋划策，甚至是只想找个地方发发呆…都可以来这里找我！我超会聊的！`,
                  `然后是这里！【微光游戏厅】！这个地方可有意思了~里面有各种各样好玩又神奇的心理小游戏，保证你一边玩到停不下来，一边又能“哇！”地一声发现内心的小秘密。超多游戏可以选哦！`,
                  `如果你想听故事，那就在这个【星轨日记】里找一找…里面藏着一个【月光故事馆】，装的都是专门写给成年人的童话故事。不知道…你会不会喜欢呢？睡前听一-听，也许能伴你安然入睡。`,
                  `对了！还是在这个【星轨日记】里，还有一个叫【未寄信笺】的好玩板块，是我给你藏的一个小彩蛋哦！有时间的话，记得一定要去发现它！`,
                  `好啦！我的世界都向你介绍完啦。那么现在，你想从哪里，开始我们的第一次探索呢？`
              ]
          });
      },
      startCurrentStep() {
        const step = this.data.currentStep;
        this.updateHighlightPosition();
        if (step === this.data.guideSteps.length - 1) {
          this.setData({ 
              displayText: this.data.guideSteps[step],
              showContinueBtn: false, 
              showChoiceBtns: true 
          });
        } else {
          this.setData({ showContinueBtn: true, showChoiceBtns: false });
          this.typewriterEffect(this.data.guideSteps[step]);
        }
      },
      typewriterEffect(fullText) {
        if (this.data.typingInterval) clearInterval(this.data.typingInterval);
        this.setData({ displayText: '' });
        let i = 0;
        const interval = setInterval(() => {
          if (i < fullText.length) {
            this.setData({ displayText: fullText.substring(0, i + 1) });
            i++;
          } else {
            clearInterval(interval);
            this.setData({ typingInterval: null });
          }
        }, 80);
        this.setData({ typingInterval: interval });
      },
      handleNextStep() {
        if (this.data.currentStep < this.data.guideSteps.length - 1) {
          this.setData({ currentStep: this.data.currentStep + 1 });
          this.startCurrentStep();
        }
      },
      handleChoice(e) {
        this.triggerEvent('guideFinished');
      },
      updateHighlightPosition() {
          const key = this.data.highlightKeys[`step${this.data.currentStep}`];
          const rect = this.properties.elementsInfo[key];
          if (rect) {
              const screenHeight = wx.getSystemInfoSync().windowHeight;
              if ((rect.top + rect.height / 2) < screenHeight / 2) {
                  this.setData({ guideBoxStyle: 'bottom: 15vh; top: auto;' });
              } else {
                  this.setData({ guideBoxStyle: 'top: 15vh; bottom: auto;' });
              }
              this.setData({
                  highlightPosition: {
                      top: `${rect.top}px`,
                      left: `${rect.left}px`,
                      width: `${rect.width}px`,
                      height: `${rect.height}px`,
                  }
              });
          } else {
              this.setData({
                  highlightPosition: { top: '50%', left: '50%', width: '0px', height: '0px' },
                  guideBoxStyle: 'bottom: 15vh; top: auto;'
              });
          }
      }
    }
  });