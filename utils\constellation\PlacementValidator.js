/**
 * 星星放置验证器
 * 负责验证星星放置的有效性和合规性
 */

class PlacementValidator {
    constructor() {
        this.validationRules = {
            minDistance: 50, // 星星之间的最小距离（像素）
            maxDistance: 300, // 星星之间的最大距离（像素）
            screenMargin: 20, // 屏幕边缘的安全边距
            maxStarsPerOrbit: 8, // 每个轨道最多星星数量
            minOrbitRadius: 80, // 最小轨道半径
            maxOrbitRadius: 200 // 最大轨道半径
        }
    }

    /**
     * 验证星星放置位置是否有效
     */
    validatePlacement(position, existingStars = [], screenBounds = {}) {
        const validationResult = {
            isValid: true,
            errors: [],
            warnings: []
        }

        try {
            // 验证位置格式
            if (!this.validatePositionFormat(position)) {
                validationResult.isValid = false
                validationResult.errors.push('位置格式无效')
                return validationResult
            }

            // 验证屏幕边界
            if (!this.validateScreenBounds(position, screenBounds)) {
                validationResult.isValid = false
                validationResult.errors.push('位置超出屏幕边界')
            }

            // 验证与其他星星的距离
            const distanceValidation = this.validateStarDistance(position, existingStars)
            if (!distanceValidation.isValid) {
                validationResult.isValid = false
                validationResult.errors.push(...distanceValidation.errors)
            }

            // 验证轨道约束
            const orbitValidation = this.validateOrbitConstraints(position, existingStars)
            if (!orbitValidation.isValid) {
                validationResult.warnings.push(...orbitValidation.warnings)
            }

            return validationResult

        } catch (error) {
            console.error('验证星星放置失败:', error)
            return {
                isValid: false,
                errors: ['验证过程发生错误'],
                warnings: []
            }
        }
    }

    /**
     * 验证位置格式
     */
    validatePositionFormat(position) {
        if (!position || typeof position !== 'object') {
            return false
        }

        if (typeof position.x !== 'number' || typeof position.y !== 'number') {
            return false
        }

        if (isNaN(position.x) || isNaN(position.y)) {
            return false
        }

        if (position.x < 0 || position.x > 1 || position.y < 0 || position.y > 1) {
            return false
        }

        return true
    }

    /**
     * 验证屏幕边界
     */
    validateScreenBounds(position, screenBounds) {
        if (!screenBounds.width || !screenBounds.height) {
            return true // 如果没有边界信息，跳过验证
        }

        const margin = this.validationRules.screenMargin
        const pixelX = position.x * screenBounds.width
        const pixelY = position.y * screenBounds.height

        return pixelX >= margin && 
               pixelX <= screenBounds.width - margin &&
               pixelY >= margin && 
               pixelY <= screenBounds.height - margin
    }

    /**
     * 验证星星间距离
     */
    validateStarDistance(position, existingStars) {
        const result = {
            isValid: true,
            errors: []
        }

        for (const star of existingStars) {
            if (!star.starPosition) continue

            const distance = this.calculateDistance(position, star.starPosition)
            
            if (distance < this.validationRules.minDistance) {
                result.isValid = false
                result.errors.push(`与现有星星距离过近: ${distance.toFixed(1)}px`)
            }
        }

        return result
    }

    /**
     * 验证轨道约束
     */
    validateOrbitConstraints(position, existingStars) {
        const result = {
            isValid: true,
            warnings: []
        }

        // 如果是第一颗星，不需要轨道约束
        if (existingStars.length === 0) {
            return result
        }

        // 检查是否在合理的轨道范围内
        const centerStar = this.findCenterStar(existingStars)
        if (centerStar) {
            const distance = this.calculateDistance(position, centerStar.starPosition)
            
            if (distance < this.validationRules.minOrbitRadius) {
                result.warnings.push('位置可能过于接近中心星星')
            }
            
            if (distance > this.validationRules.maxOrbitRadius) {
                result.warnings.push('位置可能距离中心星星过远')
            }
        }

        return result
    }

    /**
     * 计算两点间距离（像素）
     */
    calculateDistance(pos1, pos2, screenBounds = { width: 375, height: 667 }) {
        const x1 = pos1.x * screenBounds.width
        const y1 = pos1.y * screenBounds.height
        const x2 = pos2.x * screenBounds.width
        const y2 = pos2.y * screenBounds.height

        return Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2))
    }

    /**
     * 查找中心星星（第一颗星或最早的星星）
     */
    findCenterStar(stars) {
        if (stars.length === 0) return null

        // 按创建时间排序，返回最早的星星
        const sortedStars = stars.sort((a, b) => {
            const timeA = new Date(a.createTime || a.timestamp || 0).getTime()
            const timeB = new Date(b.createTime || b.timestamp || 0).getTime()
            return timeA - timeB
        })

        return sortedStars[0]
    }

    /**
     * 获取推荐的放置位置
     */
    getRecommendedPositions(existingStars, screenBounds, count = 3) {
        const recommendations = []

        try {
            if (existingStars.length === 0) {
                // 第一颗星推荐在屏幕中心附近
                recommendations.push(
                    { x: 0.5, y: 0.4, reason: '屏幕中心位置' },
                    { x: 0.4, y: 0.5, reason: '中心偏左位置' },
                    { x: 0.6, y: 0.5, reason: '中心偏右位置' }
                )
            } else {
                // 后续星星推荐在轨道上
                const centerStar = this.findCenterStar(existingStars)
                if (centerStar && centerStar.starPosition) {
                    const orbitPositions = this.generateOrbitPositions(
                        centerStar.starPosition, 
                        existingStars,
                        count
                    )
                    recommendations.push(...orbitPositions)
                }
            }

            // 验证推荐位置
            return recommendations.filter(pos => {
                const validation = this.validatePlacement(pos, existingStars, screenBounds)
                return validation.isValid
            }).slice(0, count)

        } catch (error) {
            console.error('生成推荐位置失败:', error)
            return []
        }
    }

    /**
     * 生成轨道位置
     */
    generateOrbitPositions(centerPosition, existingStars, count) {
        const positions = []
        const radius = 0.15 // 轨道半径（相对屏幕尺寸）
        const existingAngles = this.getExistingAngles(centerPosition, existingStars)

        for (let i = 0; i < count; i++) {
            let angle = (i * 2 * Math.PI) / count
            
            // 避免与现有星星角度冲突
            while (this.isAngleConflict(angle, existingAngles)) {
                angle += Math.PI / 6 // 增加30度
            }

            const x = centerPosition.x + Math.cos(angle) * radius
            const y = centerPosition.y + Math.sin(angle) * radius

            // 确保位置在屏幕范围内
            if (x >= 0.1 && x <= 0.9 && y >= 0.1 && y <= 0.9) {
                positions.push({
                    x: x,
                    y: y,
                    reason: `轨道位置 ${Math.round(angle * 180 / Math.PI)}°`
                })
            }
        }

        return positions
    }

    /**
     * 获取现有星星的角度
     */
    getExistingAngles(centerPosition, existingStars) {
        return existingStars
            .filter(star => star.starPosition)
            .map(star => {
                const dx = star.starPosition.x - centerPosition.x
                const dy = star.starPosition.y - centerPosition.y
                return Math.atan2(dy, dx)
            })
    }

    /**
     * 检查角度是否冲突
     */
    isAngleConflict(angle, existingAngles, threshold = Math.PI / 4) {
        return existingAngles.some(existingAngle => {
            const diff = Math.abs(angle - existingAngle)
            return diff < threshold || diff > (2 * Math.PI - threshold)
        })
    }

    /**
     * 设置验证规则
     */
    setValidationRules(rules) {
        Object.assign(this.validationRules, rules)
    }

    /**
     * 获取验证规则
     */
    getValidationRules() {
        return { ...this.validationRules }
    }
}

// 创建全局实例
const placementValidator = new PlacementValidator()

export default placementValidator
