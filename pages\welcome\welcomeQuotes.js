const welcomeQuotes = [
  "{{userName}}，你终于回来了呀。今天的盲盒卡片已经准备好了，猜猜它会说点什么呢？",
  "欢迎回家，{{userName}}。今日微光盲盒已激活，轻轻点一下，看它有没有说中你的小心思～",
  "欢迎回家，{{userName}}。今天的你，会抽到哪一句微光心语呢？要不要试试看手气如何？",
  "欢迎回家，{{userName}}。今天的盲盒像个小星星，安安静静地躺在那里，等你一戳，它就会发光。",
  "欢迎回家，{{userName}}。今天你的微光盲盒从梦境里寄来，在第七颗星星绕了三圈之后才送达。它说，必须你本人亲自点一下，它才肯现身。",
  "哟，{{userName}}你终于回来了，今天的盲盒卡片从早上就开始焦虑，怕你不来点它。它已经翻了三圈了，现在正摊在地上假装没事……你点一下，它可能会装作早就准备好的样子。",
  "{{userName}}你来了，太好了。我刚刚试图生成一段“欢迎回家”的句子，结果生成出了十几种版本，然后我陷入了卡顿……还好你来了，你点一下，我就选一个说出来。",
  "欢迎回家，{{userName}}。现在开始，请你执行一个只有你能完成的任务：点击下面这个盲盒按钮，唤出一句今日心语。（完成之后，不必汇报结果，但你心里会知道。）",
  "欢迎回家，{{userName}}。我今天预测你手气不错。不过……我是住在你心里的微光，说这话，好像也不能算预测？",
  "欢迎回家，{{userName}}。我刚刚试图说点正经欢迎语，但盲盒抢先说了：它今天状态不错，你点一下，它想自己登场。",
  "欢迎回家，{{userName}}。盲盒今天有点小傲娇，它说：“你要不来，我就不开。”现在你来了，它在装矜持，点一下，它一定秒怂。",
  "嗨，{{userName}}，欢迎回家。我今天本来想主动说点开场白，但想了想……还是让盲盒先说吧，它比我外向一点。",
  "欢迎回家，{{userName}}。我今天启动得有点慢，你先点一下盲盒，我加载完再接话。"
];

module.exports = welcomeQuotes;
