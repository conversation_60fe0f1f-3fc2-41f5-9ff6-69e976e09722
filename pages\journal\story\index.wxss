page {
    width: 100%;
    height: 100%;
  }
  
  .container {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow: hidden;
  }
  
  .background-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    opacity: 0.7;
  }
  
  .overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.3);
    z-index: 2;
  }
  
  .custom-nav {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    box-sizing: content-box;
    z-index: 3;
    flex-shrink: 0;
  }
  .nav-back {
    position: absolute;
    left: 20rpx;
    bottom: 0;
    height: 100%;
    width: 88rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  /* 【重要修改】用纯CSS画一个返回箭头 */
  .back-icon {
    width: 22rpx; /* 调整箭头的大小 */
    height: 22rpx;
    border: solid #E0E0E0; /* 箭头的颜色 */
    border-width: 0 0 4rpx 4rpx; /* 只显示左边和下边的边框，4rpx是箭头的粗细 */
    transform: rotate(45deg); /* 将这个L形状旋转45度，就变成了箭头 */
  }
  
  .nav-title {
    color: #E0E0E0;
    font-size: 34rpx;
    font-weight: 500;
  }
  .main-title-container {
    width: 100%;
    padding: 60rpx 0;
    display: flex;
    justify-content: center;
    z-index: 3;
    flex-shrink: 0;
  }
  .main-title {
    font-size: 42rpx;
    color: #F0E6D2;
    font-weight: 300;
    letter-spacing: 4rpx;
    text-shadow: 0 0 12rpx rgba(240, 230, 210, 0.6);
  }
  
  .character-scroll {
    width: 100%;
    flex: 1;
    overflow-y: auto;
    z-index: 3;
  }
  
  .character-list {
    width: 100%;
    padding: 0 40rpx 40rpx 40rpx;
    box-sizing: border-box;
  }
  
  .character-card-new {
    width: 100%;
    height: 320rpx;
    margin-bottom: 40rpx;
    position: relative;
    display: flex;
    align-items: center;
    background-color: rgba(30, 30, 30, 0.5);
    border-radius: 24rpx;
    border: 1px solid rgba(255, 215, 0, 0.2);
    transition: all 0.3s ease;
    transform: translateY(50px);
    opacity: 0;
    overflow: visible;
  }
  
  .character-card-new:active {
    transform: scale(0.98);
    border-color: rgba(255, 215, 0, 0.4);
  }
  
  .card-glow-new {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 120%;
    height: 120%;
    background-image: radial-gradient(circle, rgba(255, 215, 0, 0.1) 0%, rgba(255, 215, 0, 0) 70%);
    animation: breathe 6s infinite ease-in-out;
    transform: translate(-50%, -50%);
    z-index: 1;
  }
  
  @keyframes breathe {
    0%, 100% { transform: translate(-50%, -50%) scale(1); }
    50% { transform: translate(-50%, -50%) scale(1.05); }
  }
  
  .text-content {
    position: relative;
    z-index: 3;
    padding-left: 40rpx;
    width: 55%;
  }
  
  .character-name-new {
    font-size: 40rpx;
    font-weight: bold;
    color: #F0E6D2;
  }
  
  .divider {
    width: 60rpx;
    height: 2rpx;
    background-color: rgba(255, 215, 0, 0.4);
    margin: 20rpx 0;
  }
  
  .character-tagline-new {
    font-size: 26rpx;
    color: #B0B0B0;
    line-height: 1.5;
  }
  
  .character-image-new {
    position: absolute;
    right: -30rpx;
    bottom: 0;
    width: 300rpx;
    height: 300rpx;
    z-index: 2;
    filter: drop-shadow(0 10rpx 20rpx rgba(0,0,0,0.4));
  }
  