const cloud = require('wx-server-sdk');

cloud.init({
    env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
    const { type, charId, chapId } = event;
    const wxContext = cloud.getWXContext();
    const userId = wxContext.OPENID;

    try {
        switch (type) {
            case 'getCharacterList':
                // 获取所有角色列表
                return await getCharacterList();

            case 'getCharacterDetail':
                // 获取角色详情和章节列表
                return await getCharacterDetail(charId, userId);

            case 'getChapterContent':
                // 获取具体章节内容
                return await getChapterContent(charId, chapId, userId);

            default:
                return { success: false, error: '未知的请求类型' };
        }
    } catch (error) {
        console.error('getStoryData error:', error);
        return { success: false, error: error.message };
    }
};

// 获取角色列表
async function getCharacterList() {
    try {
        const res = await db.collection('stories')
            .where({
                type: 'character',
                isActive: true
            })
            .orderBy('order', 'asc')
            .get();

        const characters = res.data.map(char => ({
            id: char._id,
            name: char.name,
            image: char.image,
            tagline: char.tagline,
            description: char.description
        }));

        return { success: true, data: characters };
    } catch (error) {
        console.error('获取角色列表失败:', error);
        return { success: false, error: error.message };
    }
}

// 获取角色详情和章节列表（包含用户购买状态）
async function getCharacterDetail(charId, userId) {
    try {
        const res = await db.collection('stories')
            .doc(charId)
            .get();

        if (!res.data) {
            return { success: false, error: '角色不存在' };
        }

        const character = res.data;

        // 获取用户购买记录
        let userPurchases = [];
        if (userId) {
            try {
                // 直接查询用户集合，云数据库会自动匹配当前用户的 _openid
                const userRes = await db.collection('users').get();
                if (userRes.data.length > 0) {
                    userPurchases = userRes.data[0].purchases || [];
                }
            } catch (error) {
                console.log('获取用户购买记录失败，使用默认值:', error);
            }
        }

        // 提取章节信息并添加解锁状态
        const chapters = character.chapters.map(chapter => {
            let status = 'locked';
            if (chapter.isFree) {
                status = 'free';
            } else if (userPurchases.includes(`${charId}_${chapter.id}`)) {
                status = 'unlocked';
            }

            return {
                id: chapter.id,
                title: chapter.title,
                isFree: chapter.isFree,
                cost: chapter.cost,
                status: status
            };
        });

        const result = {
            id: character._id,
            name: character.name,
            image: character.image,
            tagline: character.tagline,
            description: character.description,
            chapters: chapters
        };

        return { success: true, data: result };
    } catch (error) {
        console.error('获取角色详情失败:', error);
        return { success: false, error: error.message };
    }
}

// 获取章节内容（需要权限验证）
async function getChapterContent(charId, chapId, userId) {
    try {
        const res = await db.collection('stories')
            .doc(charId)
            .get();

        if (!res.data) {
            return { success: false, error: '角色不存在' };
        }

        const character = res.data;
        const chapter = character.chapters.find(ch => ch.id === chapId);

        if (!chapter) {
            return { success: false, error: '章节不存在' };
        }

        // 检查访问权限
        let hasAccess = false;

        if (chapter.isFree) {
            // 免费章节直接允许访问
            hasAccess = true;
        } else if (userId) {
            // 付费章节需要检查购买记录
            try {
                // 直接查询用户集合，云数据库会自动匹配当前用户的 _openid
                const userRes = await db.collection('users').get();
                if (userRes.data.length > 0) {
                    const userPurchases = userRes.data[0].purchases || [];
                    hasAccess = userPurchases.includes(`${charId}_${chapId}`);
                }
            } catch (error) {
                console.log('检查用户购买记录失败:', error);
            }
        }

        if (!hasAccess) {
            return {
                success: false,
                error: '章节未解锁',
                needPurchase: true,
                cost: chapter.cost
            };
        }

        const result = {
            title: chapter.content.title,
            content: chapter.content.text,
            audioUrl: chapter.content.audioUrl
        };

        return { success: true, data: result };
    } catch (error) {
        console.error('获取章节内容失败:', error);
        return { success: false, error: error.message };
    }
}