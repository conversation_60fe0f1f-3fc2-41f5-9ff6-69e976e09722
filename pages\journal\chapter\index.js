const app = getApp();

// 在真实项目中，这些数据应该从服务器获取
const cloudStoragePrefix = 'cloud://cloudbase-8gji862jcfb501e7.636c-cloudbase-8gji862jcfb501e7-1365531166';

const allCharacterData = {
  'pupu': {
    name: '熊猫噗噗',
    image: `${cloudStoragePrefix}/images/story_icon_pupu.png`,
    tagline: '寻找安宁与勇气的烘焙师',
    excerpt: '在所有关切的声音里，他低下头，用自己圆滚滚的身体，轻轻地、却很坚决地，挤开了围拢的乡亲们。',
    chapters: [
      { id: 1, title: '竹林里的邀请函' }, { id: 2, title: '叮咚声里的南瓜梦' },
      { id: 3, title: '第一块自由饼干' }, { id: 4, title: '噗噗的闪闪发亮愿望单' },
      { id: 5, title: '噗噗的"超级大冒险"' }, { id: 6, title: '竹叶本子里的知音' },
      { id: 7, title: '魔法厨房的诞生' }, { id: 8, title: '奇怪的奇奇' },
      { id: 9, title: '甜甜魔法好像失效了' }, { id: 10, title: '一个拥抱的重量' }
    ]
  },
  'tuantuan': {
    name: '橘猫团团',
    image: `${cloudStoragePrefix}/story_icon_tuantuan.png`,
    tagline: '描绘内心星空的小小创作者',
    excerpt: '"我……我不想再抓鱼了。"团团对着自己的影子，用一种很轻、但又无比坚定的声音，下了她猫生中第一个决定。',
    chapters: [
      { id: 1, title: '小橘猫团团与第一颗光点' }, { id: 2, title: '熊猫噗噗与一间属于自己的小屋' },
      { id: 3, title: '会打呼噜的云和长耳朵的鞋' }, { id: 4, title: '蜘蛛姐妹的"美梦定制"' },
      { id: 5, title: '一场惊喜的暖屋派对' }, { id: 6, title: '藏在风里的悄悄话' },
      { id: 7, title: '静心湖的倒影与遥远的距离' }, { id: 8, title: '獾爷爷的"问问看"小屋' },
      { id: 9, title: '忧郁小雨云' }, { id: 10, title: '星光图书馆的委托单' }
    ]
  },
  'lili': {
    name: '狐狸栗栗',
    image: `${cloudStoragePrefix}/story_icon_lili.png`,
    tagline: '收集世间美好的杂货铺老板娘',
    excerpt: '她看着玻璃里的那个自己，第一次，没有立刻去想明天那堆积如山的事情。一股深深的疲惫感，像涨潮的海水，悄悄地漫了上来。',
    chapters: [
      { id: 1, title: '小狐狸栗栗与停不下来的星光' }, { id: 2, title: '橡树屋里的第一夜' },
      { id: 3, title: '面包与蝴蝶的难题' }, { id: 4, title: '心里的毛线团' },
      { id: 5, title: '树洞诊所与缓慢的乌龟医生' }, { id: 6, title: '一次"不及格"的作业' },
      { id: 7, title: '一场关于"慢"的、失败的模仿' }, { id: 8, title: '一次"无用"的收集任务' },
      { id: 9, title: '一张全新的"喜欢"清单' }, { id: 10, title: '"万事不缺"杂货铺' }
    ]
  },
  'qiqi': {
    name: '小狗奇奇',
    image: `${cloudStoragePrefix}/story_icon_qiqi.png`,
    tagline: '传递温暖与快乐的白日梦想家',
    excerpt: '他长长地舒了一口气，那口气，仿佛把心里所有的吵闹，都一起吐了出去。然后，他第一次，允许自己的嘴角，不用弯得那么高了。',
    chapters: [
      { id: 1, title: '最喧闹的派对与最安静的邀请函' }, { id: 2, title: '山羊婆婆与三个问题' },
      { id: 3, title: '小邮局与会发光的地图' }, { id: 4, title: '情绪果园与慢吞吞的"呼呼"' },
      { id: 5, title: '一颗光点，与一张软乎乎的梦' }, { id: 6, title: '一颗光点，与一张会拥抱的床' },
      { id: 7, title: '邮差小狗的梦' }, { id: 8, title: '被原谅的约定，与一个响亮的答案' },
      { id: 9, title: '一个噗噗冒泡的派对梦' }, { id: 10, title: '心挨在一起的派对' }
    ]
  }
};

// 【新增】模拟全局的用户数据
// 在真实项目中，这些数据应由全局状态管理或从服务器获取
let userProfile = {
  lightPoints: 10, // 假设用户有10个光点
  unlockedChapters: { // 记录用户已解锁的章节
    pupu: [],
    tuantuan: [],
    lili: [],
    qiqi: []
  }
};


Page({
  data: {
    menuButtonInfo: {},
    characterId: '',
    characterInfo: {},
    chaptersWithStatus: [],
    characterImageParallax: 0,
    showUnlockModal: false,
    selectedChapter: null,
  },

  onLoad: function (options) {
    const characterId = options.id;
    this.setData({
      menuButtonInfo: app.globalData.menuButtonInfo,
      characterId: characterId,
    });
    this.loadCharacterDetail(characterId);
  },

  // 加载角色详情和章节列表
  loadCharacterDetail: function (characterId) {
    wx.showLoading({
      title: '加载中...'
    });

    wx.cloud.callFunction({
      name: 'getStoryData',
      data: {
        type: 'getCharacterDetail',
        charId: characterId
      }
    }).then(res => {
      wx.hideLoading();
      if (res.result && res.result.success) {
        const characterData = res.result.data;

        // 章节状态已经在云函数中处理好了
        // free: 免费章节, unlocked: 已购买章节, locked: 未购买付费章节
        this.setData({
          characterInfo: characterData,
          chaptersWithStatus: characterData.chapters
        });
      } else {
        wx.showToast({
          title: '加载失败',
          icon: 'none'
        });
        console.error('获取角色详情失败:', res.result);
      }
    }).catch(err => {
      wx.hideLoading();
      wx.showToast({
        title: '网络错误',
        icon: 'none'
      });
      console.error('调用云函数失败:', err);
    });
  },

  onPageScroll: function (e) {
    const scrollTop = e.scrollTop;
    const parallaxValue = scrollTop * 0.3;
    this.setData({
      characterImageParallax: parallaxValue
    });
  },

  onChapterTap: function (e) {
    const chapter = e.currentTarget.dataset.chapter;

    if (chapter.status === 'locked') {
      // 未购买的付费章节，显示购买弹窗
      this.setData({
        showUnlockModal: true,
        selectedChapter: chapter
      });
    } else {
      // 免费章节(free)或已购买章节(unlocked)，直接进入阅读
      this.navigateToReader(chapter.id);
    }
  },

  // 【重要修改】完善解锁流程
  confirmUnlock: function () {
    const chapterToUnlock = this.data.selectedChapter;
    const cost = chapterToUnlock.cost || 2;

    wx.showLoading({ title: '购买中...' });

    // 调用购买云函数
    wx.cloud.callFunction({
      name: 'userPurchase',
      data: {
        action: 'purchaseChapter',
        charId: this.data.characterId,
        chapId: chapterToUnlock.id,
        cost: cost
      }
    }).then(res => {
      wx.hideLoading();
      if (res.result.success) {
        this.hideModal();
        wx.showToast({
          title: '解锁成功！',
          icon: 'success'
        });

        // 刷新光点余额显示
        this.refreshUserPoints();

        // 刷新章节列表状态
        this.loadCharacterDetail(this.data.characterId);

        // 延迟跳转到阅读页面
        setTimeout(() => {
          this.navigateToReader(chapterToUnlock.id);
        }, 800);
      } else {
        wx.showToast({
          title: res.result.error || '购买失败',
          icon: 'none'
        });
        this.hideModal();
      }
    }).catch(err => {
      wx.hideLoading();
      wx.showToast({
        title: '网络错误',
        icon: 'none'
      });
      this.hideModal();
      console.error('购买章节失败:', err);
    });
  },

  // 刷新用户光点余额
  refreshUserPoints: function () {
    const userPointsComponent = this.selectComponent('.nav-points');
    if (userPointsComponent) {
      userPointsComponent.refreshPoints();
    }
  },

  hideModal: function () {
    this.setData({ showUnlockModal: false });
  },

  navigateToReader: function (chapterId) {
    const characterId = this.data.characterId;
    wx.navigateTo({
      url: `/pages/journal/reader/index?charId=${characterId}&chapId=${chapterId}`
    });
  },

  navigateBack: function () {
    wx.navigateBack();
  },

  // 光点点击事件
  onPointsTap: function (e) {
    const points = e.detail.points;
    wx.showModal({
      title: '光点余额',
      content: `当前拥有 ${points} 个光点\n\n光点可用于解锁付费章节，通过完成任务或购买获得更多光点。`,
      showCancel: false,
      confirmText: '知道了'
    });
  }
});