Page({
    data: {
      lightName: '',
      userName: '',
      showLightName: true,
      showGuide: false,
      guideElementsInfo: {},
      showHelpButton: false // 控制帮助按钮显示
    },
  
    onLoad() {
      console.log("【微光调试】路标1：页面开始加载 (onLoad triggered)");
  
      wx.cloud.callFunction({
        name: 'user',
        data: { type: 'getUserInfo' }
      }).then(res => {
        console.log("【微光调试】路标2：云函数调用成功！收到的数据是：", res);
  
        if (res.result && res.result.success && res.result.data) {
          const { userName = '朋友', lightName = '微光' } = res.result.data;
          this.setData({ userName, lightName });
        } else {
          this.setData({ userName: '朋友', lightName: '微光' });
        }
  
        console.log("【微光调试】路标3：用户信息已设置，等待页面渲染完成...");
  
      }).catch(err => {
        console.error("【微光调试】！！！云函数调用失败了！错误信息：", err);
        console.log("【微光调试】云函数失败，但我们仍然会尝试启动引导页...");
        this.setData({ userName: '朋友', lightName: '微光' });
      });
    },
  
    onReady() {
      console.log("【微光调试】路标3.5：页面初次渲染完成 (onReady triggered)");
  
      // 检查是否是第一次进入
      const hasSeenGuide = wx.getStorageSync('hasSeenWelcomeGuide');
  
      if (!hasSeenGuide) {
        // 第一次进入，显示欢迎引导
        setTimeout(() => {
          this.calculateGuidePositions();
        }, 100);
      } else {
        // 不是第一次进入，显示帮助按钮
        this.setData({ showHelpButton: true });
      }
    },
  
    calculateGuidePositions() {
      console.log("【微光调试】路标4：开始计算元素位置...");
      const query = wx.createSelectorQuery();
      query.select('.main-logo').boundingClientRect();
      query.select('.icon-block[data-target="dialogue"]').boundingClientRect();
      query.select('.icon-block[data-target="gamehall"]').boundingClientRect();
      query.select('.icon-block[data-target="diary"]').boundingClientRect();
      query.exec((res) => {
        console.log("【微光调试】路标5：元素位置计算完成！结果是：", res);
        if (res && res.length >= 4 && res.every(item => item)) {
          const info = {
            logo: res[0],
            dialogue: res[1],
            gamehall: res[2],
            diary: res[3],
          };
          this.setData({
            guideElementsInfo: info,
            showGuide: true
          });
          console.log("【微光调试】路标6：引导页已经设置为显示！(showGuide: true)");
        } else {
          console.error("【微光调试】！！！未能获取到全部引导元素的位置信息。");
          this.setData({ showGuide: true });
          console.log("【微光调试】路标6：引导页被强制设置为显示！(showGuide: true)");
        }
      });
    },
  
    onGuideFinished() {
      this.setData({
        showGuide: false,
        showHelpButton: true // 引导完成后显示帮助按钮
      });
      // 标记用户已经看过欢迎引导
      wx.setStorageSync('hasSeenWelcomeGuide', true);
    },
  
    // 点击帮助按钮重新显示引导
    onHelpButtonTap() {
      this.setData({ showHelpButton: false });
      setTimeout(() => {
        this.calculateGuidePositions();
      }, 100);
    },
  
    toggleLightName() {
      this.setData({ showLightName: !this.data.showLightName });
    },
  
    goDialogue() {
      wx.navigateTo({ url: '/pages/dialogue/index' });
    },
  
    goGamehall() {
      wx.navigateTo({ url: '/pages/gamehall/index' });
    },
  
    goDiary() {
      console.log("点击了星轨日记按钮");
      wx.navigateTo({
        url: '/pages/journal/index/index'
      });
    }
  });