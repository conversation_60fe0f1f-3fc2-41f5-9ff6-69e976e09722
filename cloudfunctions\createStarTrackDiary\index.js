const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

exports.main = async (event, context) => {
  const { 
    openid, 
    aiSummary, 
    emotionWord, 
    tomorrowMessage, 
    dialogueContent,
    dialogueTheme
  } = event

  try {
    // 检查用户VIP状态
    const userResult = await db.collection('users').where({
      openid: openid
    }).get()

    const isVip = userResult.data[0]?.isVip || false

    // 写入星轨日记
    const result = await db.collection('star_track_diary').add({
      data: {
        openid,
        aiSummary,
        emotionWord,
        tomorrowMessage,
        dialogueContent,
        dialogueTheme,
        isVip,
        createTime: db.serverDate()
      }
    })

    return {
      success: true,
      _id: result._id
    }
  } catch (error) {
    console.error('创建星轨日记失败', error)
    return {
      success: false,
      error: error.message
    }
  }
} 