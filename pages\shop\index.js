Page({
  data: {
    products: [],
    loading: false,
    imageUrls: {}
  },

  onLoad: async function() {
    // 获取云存储图片URL
    const db = wx.cloud.database()
    const { data } = await db.collection('resources')
      .where({ type: 'images' })
      .orderBy('updateTime', 'desc')
      .limit(1)
      .get()

    if (data && data[0]) {
      this.setData({ imageUrls: data[0].urls })
    }

    // 设置商品列表
    this.setData({
      products: [
        {
          id: 'point_12',
          name: '12光点充值包',
          price: 1.00,
          points: 12,
          image: this.data.imageUrls['/shop/gift.png'] || '/shop/gift.png'
        },
        {
          id: 'point_30',
          name: '30光点充值包',
          price: 28.00,
          points: 30,
          image: this.data.imageUrls['/shop/jar.png'] || '/shop/jar.png'
        },
        {
          id: 'point_50',
          name: '50光点充值包',
          price: 45.00,
          points: 50,
          image: this.data.imageUrls['/shop/box.png'] || '/shop/box.png'
        },
        {
          id: 'vip_month',
          name: '月度会员',
          price: 68.00,
          type: 'vip',
          image: this.data.imageUrls['/shop/vip.png'] || '/shop/vip.png'
        }
      ]
    })
  },

  async handleBuy(e) {
    if (this.data.loading) return

    const { id } = e.currentTarget.dataset
    const product = this.data.products.find(p => p.id === id)
    
    if (!product) {
      wx.showToast({
        title: '商品不存在',
        icon: 'none'
      })
      return
    }

    this.setData({ loading: true })

    try {
      // 调用支付云函数
      const { result } = await wx.cloud.callFunction({
        name: 'payment',
        data: {
          productId: product.id
        }
      })

      if (result.code !== 0) {
        throw new Error(result.msg || '创建订单失败')
      }

      // 发起支付
      await wx.requestPayment({
        ...result.data,
        success: () => {
          wx.showToast({
            title: '支付成功',
            icon: 'success'
          })
        },
        fail: (err) => {
          console.error('支付失败:', err)
          wx.showToast({
            title: '支付失败',
            icon: 'none'
          })
        }
      })
    } catch (err) {
      console.error('购买失败:', err)
      wx.showToast({
        title: err.message || '购买失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  }
}) 