<!-- 微光星迹页面 -->
<view class="star-sky-container">
  <!-- 星空背景 -->
  <view class="star-background">
    <!-- 背景星星装饰 -->
    <view class="bg-star bg-star-1"></view>
    <view class="bg-star bg-star-2"></view>
    <view class="bg-star bg-star-3"></view>
    <view class="bg-star bg-star-4"></view>
    <view class="bg-star bg-star-5"></view>
    <view class="bg-star bg-star-6"></view>
    <view class="bg-star bg-star-7"></view>
    <view class="bg-star bg-star-8"></view>
  </view>

  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">微光星迹</text>
    <text class="page-subtitle">那些走过的路，都会在星图上发光</text>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{isLoading}}" class="loading-container">
    <view class="loading-star"></view>
    <text class="loading-text">正在寻找你的星光轨迹...</text>
  </view>

  <!-- 星星放置模式 -->
  <view wx:if="{{isPlacementMode}}" class="placement-mode-container">
    <!-- 放置指引 -->
    <view class="placement-instructions">
      <view class="instruction-icon">⭐</view>
      <text class="instruction-text">{{placementInstructions}}</text>
      <text class="instruction-subtitle">{{isFirstStar ? '点击屏幕任意位置放置你的第一颗星星' : '拖拽星星到合适的轨道位置'}}</text>
    </view>

    <!-- 可交互的放置区域 -->
    <view 
      class="placement-area"
      bindtap="onStarPlacementTap"
      bindtouchstart="onPlacementTouchStart"
      bindtouchmove="onPlacementTouchMove"
      bindtouchend="onPlacementTouchEnd"
    >
      <!-- 显示现有星星（半透明） -->
      <view 
        wx:for="{{starRecords}}" 
        wx:key="id"
        class="existing-star"
        style="left: {{item.starStyle.left}}%; top: {{item.starStyle.top}}%; opacity: 0.3;"
      >
        <view class="star-body">
          <view class="star-core"></view>
        </view>
      </view>

      <!-- 待放置的新星星（跟随手指移动） -->
      <view 
        wx:if="{{newStarData}}"
        class="new-star-preview {{starDragAnimation || ''}}"
        style="left: {{newStarPreviewPosition.x || 50}}%; top: {{newStarPreviewPosition.y || 50}}%;"
      >
        <view class="star-body new-star-body">
          <view class="star-core"></view>
          <view class="star-glow"></view>
        </view>
        
        <!-- 新星星的情感标识 -->
        <view wx:if="{{newStarData.emotionKeyword}}" class="new-star-emotion" style="color: {{getEmotionColor(newStarData.emotionKeyword)}}">
          {{newStarData.emotionKeyword}}
        </view>
      </view>

      <!-- 轨道提示线（非首颗星时显示） -->
      <view wx:if="{{!isFirstStar && starRecords.length > 0}}" class="orbit-guide">
        <view 
          class="orbit-circle"
          style="left: {{starRecords[starRecords.length - 1].starStyle.left}}%; top: {{starRecords[starRecords.length - 1].starStyle.top}}%;"
        ></view>
      </view>
    </view>

    <!-- 放置模式操作按钮 -->
    <view class="placement-actions">
      <button class="placement-btn cancel-btn" bindtap="exitPlacementMode">
        取消放置
      </button>
      <button class="placement-btn confirm-btn" bindtap="confirmCurrentPlacement" disabled="{{!canConfirmPlacement}}">
        确认位置
      </button>
    </view>
  </view>

  <!-- 星光记录区域 -->
  <view wx:elif="{{starRecords.length > 0}}" class="star-records-container">
    <!-- 性能优化指示器 -->
    <view wx:if="{{isProgressiveLoading}}" class="performance-indicator">
      <view class="loading-progress">
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{loadingProgress}}%"></view>
        </view>
        <text class="progress-text">加载中 {{loadingProgress.toFixed(0)}}%</text>
      </view>
    </view>

    <!-- 地图导航控制按钮 -->
    <view class="map-controls">
      <!-- 性能指标按钮（调试用） -->
      <view wx:if="{{performanceMetrics.totalStars > 0}}" class="performance-info">
        <button class="control-btn metrics-btn" bindtap="showPerformanceMetrics">
          <text class="btn-icon">📊</text>
          <text class="btn-text">性能</text>
        </button>
        <view class="metrics-summary">
          <text class="metric-item">{{visibleStars.length}}/{{starRecords.length}}</text>
        </view>
      </view>
      
      <!-- 缩放控制 -->
      <view class="zoom-controls">
        <button 
          class="zoom-btn zoom-in-btn {{canZoomIn ? '' : 'disabled'}}"
          bindtap="zoomIn"
          disabled="{{!canZoomIn}}"
        >
          +
        </button>
        <view class="zoom-level">{{zoomLevelText}}</view>
        <button 
          class="zoom-btn zoom-out-btn {{canZoomOut ? '' : 'disabled'}}"
          bindtap="zoomOut"
          disabled="{{!canZoomOut}}"
        >
          -
        </button>
      </view>
      
      <!-- 其他控制按钮 -->
      <view class="other-controls">
        <button class="control-btn fit-all-btn" bindtap="zoomToFitAllStars">
          <text class="btn-icon">⌂</text>
          <text class="btn-text">全览</text>
        </button>
        <button class="control-btn reset-btn" bindtap="resetZoom">
          <text class="btn-icon">↻</text>
          <text class="btn-text">重置</text>
        </button>
      </view>
    </view>

    <!-- 可缩放平移的地图容器 -->
    <view 
      class="star-map-viewport"
      bindtouchstart="onMapTouchStart"
      bindtouchmove="onMapTouchMove"
      bindtouchend="onMapTouchEnd"
    >
      <view 
        class="star-map-content"
        style="transform: {{mapTransform}}; transform-origin: center center;"
      >
        <!-- 每颗星星代表一次对话 -->
        <view 
          wx:for="{{starRecords}}" 
          wx:key="id"
          class="dialogue-star {{item.animationClass || ''}} {{item.hoverAnimation || ''}}"
          style="left: {{item.starStyle.left}}%; top: {{item.starStyle.top}}%; transform: scale({{item.starStyle.size}}); opacity: {{item.starStyle.brightness}}; animation-delay: {{item.starStyle.animationDelay}}s;"
          bindtap="onStarTap"
          bindtouchstart="onStarTouchStart"
          bindtouchend="onStarTouchEnd"
          data-index="{{index}}"
          data-emotion="{{item.emotionKeyword}}"
        >
          <!-- 星星主体 -->
          <view class="star-body">
            <view class="star-core"></view>
            <view class="star-glow"></view>
          </view>
          
          <!-- 星星信息提示 -->
          <view class="star-info">
            <text class="star-theme">{{item.theme}}</text>
            <text class="star-time">{{formatTime(item.createTime)}}</text>
            <view wx:if="{{item.emotionKeyword}}" class="star-emotion" style="color: {{getEmotionColor(item.emotionKeyword)}}">
              {{item.emotionKeyword}}
            </view>
          </view>
        </view>

        <!-- 轨道路径动画 -->
        <view wx:if="{{orbitPathAnimation && orbitPathAnimation.show}}" 
              class="orbit-path {{orbitPathAnimation.animationClass}}"
              style="left: {{orbitPathAnimation.centerX}}%; top: {{orbitPathAnimation.centerY}}%; width: {{orbitPathAnimation.radius * 2}}rpx; height: {{orbitPathAnimation.radius * 2}}rpx;">
        </view>

        <!-- 放置确认脉冲效果 -->
        <view wx:if="{{placementPulseEffect && placementPulseEffect.show}}" 
              class="placement-pulse-effect"
              style="left: {{placementPulseEffect.left}}%; top: {{placementPulseEffect.top}}%;">
        </view>

        <!-- 星星连线 -->
        <view wx:for="{{starConnections}}" wx:key="id" 
              class="star-connection-line {{item.animationClass || ''}}"
              style="left: {{item.centerX}}%; top: {{item.centerY}}%; width: {{item.width}}%; transform: translateX(-50%) translateY(-50%) rotate({{item.angle}}deg); opacity: {{item.type === 'closing' ? 0.6 : 0.8}};">
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view wx:else class="empty-state">
    <view class="empty-star"></view>
    <text class="empty-title">还没有星光轨迹</text>
    <text class="empty-subtitle">开始你的第一次星光对话吧</text>
    <button class="start-dialogue-btn" bindtap="navigateToDialogue">开始对话</button>
  </view>

  <!-- 新的星星信息卡片系统 -->
  <view wx:if="{{showInfoCard && starInfoCard}}" class="info-card-modal" bindtap="closeDetail">
    <view class="info-card-content" catchtap="">
      <!-- 卡片头部 -->
      <view class="info-card-header">
        <view class="card-title-area">
          <text class="card-theme">{{starInfoCard.title}}</text>
          <text class="card-date">{{starInfoCard.date}}</text>
        </view>
        <view class="card-close-btn" bindtap="closeDetail">×</view>
      </view>

      <!-- 情感关键词 -->
      <view wx:if="{{starInfoCard.emotion}}" class="card-emotion-section">
        <view class="card-emotion-tag" style="background-color: {{starInfoCard.emotionColor}}20; color: {{starInfoCard.emotionColor}};">
          {{starInfoCard.emotion}}
        </view>
      </view>

      <!-- 内容预览 -->
      <view wx:if="{{starInfoCard.contentPreview}}" class="card-content-section">
        <text class="card-content-preview">{{starInfoCard.contentPreview}}</text>
      </view>

      <!-- 操作按钮区域 -->
      <view class="card-actions">
        <!-- 查看日记按钮 -->
        <button 
          wx:if="{{starInfoCard.hasGeneratedDiary}}" 
          class="card-action-btn diary-btn"
          data-action="viewDiary"
          bindtap="handleCardInteraction"
        >
          查看日记
        </button>
        
        <!-- 展开详情按钮 -->
        <button 
          class="card-action-btn expand-btn"
          data-action="expand"
          bindtap="handleCardInteraction"
        >
          查看详情
        </button>
      </view>

      <!-- 展开的详细内容 -->
      <view wx:if="{{starInfoCard.expanded}}" class="card-expanded-content">
        <!-- 对话总结 -->
        <view wx:if="{{starInfoCard.fullSummary}}" class="card-detail-section">
          <view class="card-section-title">对话总结</view>
          <view class="card-section-content summary-content">
            {{starInfoCard.fullSummary}}
          </view>
        </view>

        <!-- 三问仪式 -->
        <view wx:if="{{starInfoCard.questions && starInfoCard.questions.length > 0}}" class="card-detail-section">
          <view class="card-section-title">三问仪式</view>
          <view class="card-questions-container">
            <view wx:for="{{starInfoCard.questions}}" wx:key="index" class="card-question-item">
              <view class="card-question-text">{{item}}</view>
              <view wx:if="{{starInfoCard.answers[index]}}" class="card-answer-text">
                {{starInfoCard.answers[index]}}
              </view>
            </view>
          </view>
        </view>

        <!-- 明日寄语 -->
        <view wx:if="{{starInfoCard.tomorrowMessage}}" class="card-detail-section">
          <view class="card-section-title">明日寄语</view>
          <view class="card-section-content tomorrow-message">
            {{starInfoCard.tomorrowMessage}}
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 保持向后兼容的详情弹窗 -->
  <view wx:if="{{showDetail && selectedRecord && !showInfoCard}}" class="detail-modal" bindtap="closeDetail">
    <view class="detail-content" catchtap="">
      <!-- 详情头部 -->
      <view class="detail-header">
        <view class="detail-title-area">
          <text class="detail-theme">{{selectedRecord.theme}}</text>
          <text class="detail-time">{{formatTime(selectedRecord.createTime)}}</text>
        </view>
        <view class="close-btn" bindtap="closeDetail">×</view>
      </view>

      <!-- 对话总结 -->
      <view wx:if="{{selectedRecord.summary}}" class="detail-section">
        <view class="section-title">对话总结</view>
        <view class="section-content summary-content">
          {{selectedRecord.summary}}
        </view>
      </view>

      <!-- 三问仪式 -->
      <view wx:if="{{selectedRecord.questions.length > 0}}" class="detail-section">
        <view class="section-title">三问仪式</view>
        <view class="questions-container">
          <view wx:for="{{selectedRecord.questions}}" wx:key="index" class="question-item">
            <view class="question-text">{{item}}</view>
            <view wx:if="{{selectedRecord.answers[index]}}" class="answer-text">
              {{selectedRecord.answers[index]}}
            </view>
          </view>
        </view>
      </view>

      <!-- 明日寄语 -->
      <view wx:if="{{selectedRecord.tomorrowMessage}}" class="detail-section">
        <view class="section-title">明日寄语</view>
        <view class="section-content tomorrow-message">
          {{selectedRecord.tomorrowMessage}}
        </view>
      </view>

      <!-- 情感关键词 -->
      <view wx:if="{{selectedRecord.emotionKeyword}}" class="detail-section">
        <view class="section-title">情感关键词</view>
        <view class="emotion-tag" style="background-color: {{getEmotionColor(selectedRecord.emotionKeyword)}}20; color: {{getEmotionColor(selectedRecord.emotionKeyword)}};">
          {{selectedRecord.emotionKeyword}}
        </view>
      </view>
    </view>
  </view>
</view>