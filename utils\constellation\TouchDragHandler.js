/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON> touch and drag interactions for star placement
 * Implements basic drag functionality with throttling and haptic feedback
 */

const { INTERACTION_CONFIG, ANIMATION_CONFIG } = require('./config/ConstellationConfig');

class TouchDragHandler {
    constructor() {
        this.isDragging = false;
        this.currentStarId = null;
        this.dragStartPosition = null;
        this.lastTouchTime = 0;
        this.touchThrottleMs = ANIMATION_CONFIG.TOUCH_THROTTLE_MS;
        this.minDragDistance = INTERACTION_CONFIG.TOUCH.MIN_DRAG_DISTANCE;
        this.hapticIntensity = INTERACTION_CONFIG.TOUCH.HAPTIC_INTENSITY;

        // Event listeners storage for cleanup
        this.eventListeners = new Map();

        // Drag state tracking
        this.dragState = {
            startX: 0,
            startY: 0,
            currentX: 0,
            currentY: 0,
            deltaX: 0,
            deltaY: 0,
            totalDistance: 0,
            startTime: 0,
            lastMoveTime: 0
        };

        // Callbacks for drag events
        this.callbacks = {
            onDragStart: null,
            onDragMove: null,
            onDragEnd: null
        };
    }

    /**
     * Initialize the touch drag handler
     * @param {HTMLElement} containerElement - The container element to attach events to
     * @param {Object} callbacks - Callback functions for drag events
     */
    initialize(containerElement = null, callbacks = {}) {
        try {
            this.callbacks = { ...this.callbacks, ...callbacks };

            if (containerElement) {
                this.attachEventListeners(containerElement);
            }

            console.log('TouchDragHandler initialized successfully');
            return { success: true };
        } catch (error) {
            console.error('Failed to initialize TouchDragHandler:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Attach event listeners to container element
     * @param {HTMLElement} containerElement - The container element
     */
    attachEventListeners(containerElement) {
        // Touch events for mobile
        const touchStartHandler = this._createThrottledHandler((e) => this._handleTouchStart(e));
        const touchMoveHandler = this._createThrottledHandler((e) => this._handleTouchMove(e));
        const touchEndHandler = (e) => this._handleTouchEnd(e);

        // Mouse events for desktop
        const mouseDownHandler = this._createThrottledHandler((e) => this._handleMouseDown(e));
        const mouseMoveHandler = this._createThrottledHandler((e) => this._handleMouseMove(e));
        const mouseUpHandler = (e) => this._handleMouseUp(e);

        // Store event listeners for cleanup
        this.eventListeners.set('touchstart', touchStartHandler);
        this.eventListeners.set('touchmove', touchMoveHandler);
        this.eventListeners.set('touchend', touchEndHandler);
        this.eventListeners.set('mousedown', mouseDownHandler);
        this.eventListeners.set('mousemove', mouseMoveHandler);
        this.eventListeners.set('mouseup', mouseUpHandler);

        // Attach listeners
        containerElement.addEventListener('touchstart', touchStartHandler, { passive: false });
        containerElement.addEventListener('touchmove', touchMoveHandler, { passive: false });
        containerElement.addEventListener('touchend', touchEndHandler, { passive: false });
        containerElement.addEventListener('mousedown', mouseDownHandler);
        containerElement.addEventListener('mousemove', mouseMoveHandler);
        containerElement.addEventListener('mouseup', mouseUpHandler);

        // Prevent context menu on long press
        containerElement.addEventListener('contextmenu', (e) => e.preventDefault());
    }

    /**
     * Remove event listeners for cleanup
     * @param {HTMLElement} containerElement - The container element
     */
    removeEventListeners(containerElement) {
        if (!containerElement) return;

        this.eventListeners.forEach((handler, eventType) => {
            containerElement.removeEventListener(eventType, handler);
        });

        this.eventListeners.clear();
    }

    /**
     * Handle drag start event
     * @param {string} starId - ID of the star being dragged
     * @param {Object} touchPoint - Touch/mouse position {x, y}
     * @param {Object} options - Additional options
     */
    onDragStart(starId, touchPoint, options = {}) {
        try {
            if (this.isDragging) {
                console.warn('Drag already in progress, ignoring new drag start');
                return { success: false, error: 'Drag already in progress' };
            }

            // Validate input
            if (!starId || !touchPoint || typeof touchPoint.x !== 'number' || typeof touchPoint.y !== 'number') {
                throw new Error('Invalid starId or touchPoint provided');
            }

            // Initialize drag state
            this.isDragging = true;
            this.currentStarId = starId;
            this.dragStartPosition = { ...touchPoint };

            this.dragState = {
                startX: touchPoint.x,
                startY: touchPoint.y,
                currentX: touchPoint.x,
                currentY: touchPoint.y,
                deltaX: 0,
                deltaY: 0,
                totalDistance: 0,
                startTime: Date.now(),
                lastMoveTime: Date.now()
            };

            // Provide haptic feedback
            this._provideFeedback('start');

            // Call callback if provided
            if (this.callbacks.onDragStart) {
                this.callbacks.onDragStart(starId, touchPoint, {
                    ...options,
                    dragState: { ...this.dragState }
                });
            }

            console.log(`Drag started for star ${starId} at position`, touchPoint);
            return {
                success: true,
                starId: starId,
                startPosition: { ...touchPoint },
                dragState: { ...this.dragState }
            };

        } catch (error) {
            console.error('Error in onDragStart:', error);
            this._resetDragState();
            return { success: false, error: error.message };
        }
    }

    /**
     * Handle drag move event
     * @param {string} starId - ID of the star being dragged
     * @param {Object} touchPoint - Current touch/mouse position {x, y}
     * @param {Object} constraints - Movement constraints (optional)
     */
    onDragMove(starId, touchPoint, constraints = null) {
        try {
            if (!this.isDragging || this.currentStarId !== starId) {
                return { success: false, error: 'No active drag for this star' };
            }

            // Validate input
            if (!touchPoint || typeof touchPoint.x !== 'number' || typeof touchPoint.y !== 'number') {
                throw new Error('Invalid touchPoint provided');
            }

            // Check throttling
            const currentTime = Date.now();
            if (currentTime - this.lastTouchTime < this.touchThrottleMs) {
                return { success: false, error: 'Touch event throttled' };
            }
            this.lastTouchTime = currentTime;

            // Update drag state
            const previousX = this.dragState.currentX;
            const previousY = this.dragState.currentY;

            this.dragState.currentX = touchPoint.x;
            this.dragState.currentY = touchPoint.y;
            this.dragState.deltaX = touchPoint.x - this.dragState.startX;
            this.dragState.deltaY = touchPoint.y - this.dragState.startY;
            this.dragState.lastMoveTime = currentTime;

            // Calculate movement distance
            const moveDistance = Math.sqrt(
                Math.pow(touchPoint.x - previousX, 2) +
                Math.pow(touchPoint.y - previousY, 2)
            );
            this.dragState.totalDistance += moveDistance;

            // Apply constraints if provided
            let constrainedPosition = { ...touchPoint };
            if (constraints) {
                if (constraints.type === 'orbit' && constraints.orbitCenter && constraints.orbitRadius) {
                    // Apply orbital constraint
                    constrainedPosition = this.constrainToOrbit(touchPoint, constraints.orbitCenter, constraints.orbitRadius);
                } else if (typeof constraints.constrainPosition === 'function') {
                    // Apply custom constraint function
                    constrainedPosition = constraints.constrainPosition(touchPoint);
                }
            }

            // Validate drag boundaries
            const boundaryCheck = this._validateDragBoundaries(constrainedPosition, constraints);
            if (!boundaryCheck.valid) {
                // Use fallback position if boundary check fails
                constrainedPosition = boundaryCheck.fallbackPosition || constrainedPosition;
            }

            // Call callback if provided
            if (this.callbacks.onDragMove) {
                this.callbacks.onDragMove(starId, constrainedPosition, {
                    originalPosition: touchPoint,
                    constraints: constraints,
                    dragState: { ...this.dragState },
                    moveDistance: moveDistance,
                    boundaryCheck: boundaryCheck
                });
            }

            return {
                success: true,
                starId: starId,
                position: constrainedPosition,
                originalPosition: touchPoint,
                dragState: { ...this.dragState },
                moveDistance: moveDistance,
                boundaryCheck: boundaryCheck
            };

        } catch (error) {
            console.error('Error in onDragMove:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Handle drag end event
     * @param {string} starId - ID of the star being dragged
     * @param {Object} finalPosition - Final position {x, y}
     * @param {Object} options - Additional options
     */
    onDragEnd(starId, finalPosition, options = {}) {
        try {
            if (!this.isDragging || this.currentStarId !== starId) {
                return { success: false, error: 'No active drag for this star' };
            }

            // Validate input
            if (!finalPosition || typeof finalPosition.x !== 'number' || typeof finalPosition.y !== 'number') {
                throw new Error('Invalid finalPosition provided');
            }

            // Calculate final drag statistics
            const dragDuration = Date.now() - this.dragState.startTime;
            const totalDistance = this.dragState.totalDistance;
            const finalDelta = {
                x: finalPosition.x - this.dragState.startX,
                y: finalPosition.y - this.dragState.startY
            };
            const finalDistance = Math.sqrt(finalDelta.x * finalDelta.x + finalDelta.y * finalDelta.y);

            // Determine if this was a significant drag
            const isSignificantDrag = finalDistance >= this.minDragDistance;

            // Provide haptic feedback
            this._provideFeedback('end');

            // Prepare drag summary
            const dragSummary = {
                starId: starId,
                startPosition: { ...this.dragStartPosition },
                finalPosition: { ...finalPosition },
                totalDistance: totalDistance,
                finalDistance: finalDistance,
                duration: dragDuration,
                isSignificantDrag: isSignificantDrag,
                dragState: { ...this.dragState }
            };

            // Call callback if provided
            if (this.callbacks.onDragEnd) {
                this.callbacks.onDragEnd(starId, finalPosition, {
                    ...options,
                    dragSummary: dragSummary
                });
            }

            // Reset drag state
            this._resetDragState();

            console.log(`Drag ended for star ${starId}`, dragSummary);
            return {
                success: true,
                dragSummary: dragSummary
            };

        } catch (error) {
            console.error('Error in onDragEnd:', error);
            this._resetDragState();
            return { success: false, error: error.message };
        }
    }

    /**
     * Set callback functions for drag events
     * @param {Object} callbacks - Callback functions
     */
    setCallbacks(callbacks) {
        this.callbacks = { ...this.callbacks, ...callbacks };
    }

    /**
     * Get current drag state
     * @returns {Object} Current drag state
     */
    getDragState() {
        return {
            isDragging: this.isDragging,
            currentStarId: this.currentStarId,
            dragStartPosition: this.dragStartPosition ? { ...this.dragStartPosition } : null,
            dragState: { ...this.dragState }
        };
    }

    /**
     * Check if currently dragging
     * @returns {boolean} True if dragging
     */
    isDragActive() {
        return this.isDragging;
    }

    /**
     * Cancel current drag operation
     */
    cancelDrag() {
        if (this.isDragging) {
            console.log(`Cancelling drag for star ${this.currentStarId}`);
            this._resetDragState();
            return { success: true, message: 'Drag cancelled' };
        }
        return { success: false, message: 'No active drag to cancel' };
    }

    /**
     * Constrain touch point to circular orbit
     * @param {Object} touchPoint - Current touch position {x, y}
     * @param {Object} orbitCenter - Center of the orbit {x, y}
     * @param {number} orbitRadius - Radius of the orbit in pixels
     * @returns {Object} Constrained position on the orbit
     */
    constrainToOrbit(touchPoint, orbitCenter, orbitRadius) {
        try {
            // Validate inputs
            if (!touchPoint || typeof touchPoint.x !== 'number' || typeof touchPoint.y !== 'number') {
                throw new Error('Invalid touchPoint provided');
            }
            if (!orbitCenter || typeof orbitCenter.x !== 'number' || typeof orbitCenter.y !== 'number') {
                throw new Error('Invalid orbitCenter provided');
            }
            if (typeof orbitRadius !== 'number' || orbitRadius <= 0) {
                throw new Error('Invalid orbitRadius provided');
            }

            // Calculate vector from orbit center to touch point
            const dx = touchPoint.x - orbitCenter.x;
            const dy = touchPoint.y - orbitCenter.y;

            // Calculate angle from center to touch point
            const angle = Math.atan2(dy, dx);

            // Calculate constrained position on the orbit circle
            const constrainedX = orbitCenter.x + orbitRadius * Math.cos(angle);
            const constrainedY = orbitCenter.y + orbitRadius * Math.sin(angle);

            // Calculate distance from center for debugging/validation
            const distanceFromCenter = Math.sqrt(dx * dx + dy * dy);

            return {
                x: constrainedX,
                y: constrainedY,
                angle: angle,
                orbitInfo: {
                    center: { ...orbitCenter },
                    radius: orbitRadius,
                    originalDistance: distanceFromCenter,
                    angleRadians: angle,
                    angleDegrees: (angle * 180) / Math.PI
                }
            };

        } catch (error) {
            console.error('Error in constrainToOrbit:', error);
            // Return original position as fallback
            return {
                x: touchPoint.x,
                y: touchPoint.y,
                angle: 0,
                orbitInfo: null,
                error: error.message
            };
        }
    }

    /**
     * Generate orbital path points for visualization
     * @param {Object} orbitCenter - Center of the orbit {x, y}
     * @param {number} orbitRadius - Radius of the orbit in pixels
     * @param {number} segments - Number of segments for the path (default: 64)
     * @returns {Object} Orbital path data
     */
    generateOrbitPath(orbitCenter, orbitRadius, segments = 64) {
        try {
            // Validate inputs
            if (!orbitCenter || typeof orbitCenter.x !== 'number' || typeof orbitCenter.y !== 'number') {
                throw new Error('Invalid orbitCenter provided');
            }
            if (typeof orbitRadius !== 'number' || orbitRadius <= 0) {
                throw new Error('Invalid orbitRadius provided');
            }
            if (typeof segments !== 'number' || segments < 8) {
                segments = 64; // Default to 64 segments
            }

            const points = [];
            const angleStep = (2 * Math.PI) / segments;

            // Generate points around the circle
            for (let i = 0; i <= segments; i++) {
                const angle = i * angleStep;
                const x = orbitCenter.x + orbitRadius * Math.cos(angle);
                const y = orbitCenter.y + orbitRadius * Math.sin(angle);

                points.push({
                    x: x,
                    y: y,
                    angle: angle,
                    index: i
                });
            }

            return {
                success: true,
                points: points,
                center: { ...orbitCenter },
                radius: orbitRadius,
                segments: segments,
                circumference: 2 * Math.PI * orbitRadius,
                pathData: this._generateSVGPathData(points)
            };

        } catch (error) {
            console.error('Error generating orbit path:', error);
            return {
                success: false,
                error: error.message,
                points: [],
                center: orbitCenter,
                radius: orbitRadius
            };
        }
    }

    /**
     * Show orbital path visualization
     * @param {Object} orbitCenter - Center of the orbit {x, y}
     * @param {number} orbitRadius - Radius of the orbit in pixels
     * @param {Object} options - Visualization options
     * @returns {Object} Path visualization result
     */
    showOrbitPath(orbitCenter, orbitRadius, options = {}) {
        try {
            const defaultOptions = {
                strokeColor: '#ffffff',
                strokeWidth: 2,
                strokeOpacity: 0.6,
                strokeDashArray: '5,5',
                animationDuration: 2000,
                fadeOut: true,
                className: 'orbit-path'
            };

            const config = { ...defaultOptions, ...options };
            const pathData = this.generateOrbitPath(orbitCenter, orbitRadius);

            if (!pathData.success) {
                throw new Error(`Failed to generate orbit path: ${pathData.error}`);
            }

            // Create or update orbit path element
            const pathElement = this._createOrbitPathElement(pathData, config);

            // Apply animation if requested
            if (config.animationDuration > 0) {
                this._animateOrbitPath(pathElement, config);
            }

            return {
                success: true,
                pathElement: pathElement,
                pathData: pathData,
                config: config
            };

        } catch (error) {
            console.error('Error showing orbit path:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Hide orbital path visualization
     * @param {boolean} animate - Whether to animate the hiding
     */
    hideOrbitPath(animate = true) {
        try {
            const pathElements = document.querySelectorAll('.orbit-path');

            pathElements.forEach(element => {
                if (animate) {
                    // Fade out animation
                    element.style.transition = 'opacity 0.3s ease-out';
                    element.style.opacity = '0';

                    setTimeout(() => {
                        if (element.parentNode) {
                            element.parentNode.removeChild(element);
                        }
                    }, 300);
                } else {
                    // Remove immediately
                    if (element.parentNode) {
                        element.parentNode.removeChild(element);
                    }
                }
            });

            return { success: true, message: 'Orbit path hidden' };

        } catch (error) {
            console.error('Error hiding orbit path:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Create throttled event handler
     * @param {Function} handler - Original handler function
     * @returns {Function} Throttled handler
     */
    _createThrottledHandler(handler) {
        let lastCallTime = 0;

        return (event) => {
            const currentTime = Date.now();
            if (currentTime - lastCallTime >= this.touchThrottleMs) {
                lastCallTime = currentTime;
                return handler(event);
            }
        };
    }

    /**
     * Handle touch start event
     * @param {TouchEvent} event - Touch event
     */
    _handleTouchStart(event) {
        if (event.touches.length === 1) {
            event.preventDefault();
            const touch = event.touches[0];
            const touchPoint = {
                x: touch.clientX,
                y: touch.clientY
            };

            // Find star element under touch
            const starElement = this._findStarElementUnderPoint(touchPoint, event.target);
            if (starElement) {
                const starId = starElement.dataset.starId || 'unknown';
                this.onDragStart(starId, touchPoint, { eventType: 'touch' });
            }
        }
    }

    /**
     * Handle touch move event
     * @param {TouchEvent} event - Touch event
     */
    _handleTouchMove(event) {
        if (this.isDragging && event.touches.length === 1) {
            event.preventDefault();
            const touch = event.touches[0];
            const touchPoint = {
                x: touch.clientX,
                y: touch.clientY
            };

            this.onDragMove(this.currentStarId, touchPoint);
        }
    }

    /**
     * Handle touch end event
     * @param {TouchEvent} event - Touch event
     */
    _handleTouchEnd(event) {
        if (this.isDragging) {
            event.preventDefault();
            const touch = event.changedTouches[0];
            const finalPosition = {
                x: touch.clientX,
                y: touch.clientY
            };

            this.onDragEnd(this.currentStarId, finalPosition, { eventType: 'touch' });
        }
    }

    /**
     * Handle mouse down event
     * @param {MouseEvent} event - Mouse event
     */
    _handleMouseDown(event) {
        const touchPoint = {
            x: event.clientX,
            y: event.clientY
        };

        // Find star element under mouse
        const starElement = this._findStarElementUnderPoint(touchPoint, event.target);
        if (starElement) {
            const starId = starElement.dataset.starId || 'unknown';
            this.onDragStart(starId, touchPoint, { eventType: 'mouse' });
        }
    }

    /**
     * Handle mouse move event
     * @param {MouseEvent} event - Mouse event
     */
    _handleMouseMove(event) {
        if (this.isDragging) {
            const touchPoint = {
                x: event.clientX,
                y: event.clientY
            };

            this.onDragMove(this.currentStarId, touchPoint);
        }
    }

    /**
     * Handle mouse up event
     * @param {MouseEvent} event - Mouse event
     */
    _handleMouseUp(event) {
        if (this.isDragging) {
            const finalPosition = {
                x: event.clientX,
                y: event.clientY
            };

            this.onDragEnd(this.currentStarId, finalPosition, { eventType: 'mouse' });
        }
    }

    /**
     * Find star element under the given point
     * @param {Object} point - Point coordinates {x, y}
     * @param {HTMLElement} targetElement - Target element from event
     * @returns {HTMLElement|null} Star element or null
     */
    _findStarElementUnderPoint(point, targetElement) {
        // First check if the target element itself is a star
        if (targetElement && targetElement.dataset && targetElement.dataset.starId) {
            return targetElement;
        }

        // Check parent elements
        let element = targetElement;
        while (element && element.parentElement) {
            if (element.dataset && element.dataset.starId) {
                return element;
            }
            element = element.parentElement;
        }

        // Use document.elementFromPoint as fallback
        const elementAtPoint = document.elementFromPoint(point.x, point.y);
        if (elementAtPoint && elementAtPoint.dataset && elementAtPoint.dataset.starId) {
            return elementAtPoint;
        }

        return null;
    }

    /**
     * Provide haptic feedback
     * @param {string} feedbackType - Type of feedback ('start', 'end', 'error')
     */
    _provideFeedback(feedbackType) {
        try {
            // Check if haptic feedback is available
            if (typeof wx !== 'undefined' && wx.vibrateShort) {
                // WeChat Mini Program haptic feedback
                if (feedbackType === 'start') {
                    wx.vibrateShort({
                        type: 'light',
                        success: () => console.log('Haptic feedback: drag start'),
                        fail: (err) => console.warn('Haptic feedback failed:', err)
                    });
                } else if (feedbackType === 'end') {
                    wx.vibrateShort({
                        type: 'medium',
                        success: () => console.log('Haptic feedback: drag end'),
                        fail: (err) => console.warn('Haptic feedback failed:', err)
                    });
                }
            } else if (navigator.vibrate) {
                // Web Vibration API
                const duration = feedbackType === 'start' ? 50 : 100;
                navigator.vibrate(duration);
                console.log(`Haptic feedback: ${feedbackType} (${duration}ms)`);
            } else {
                console.log(`Haptic feedback not available: ${feedbackType}`);
            }
        } catch (error) {
            console.warn('Error providing haptic feedback:', error);
        }
    }

    /**
     * Validate drag boundaries to prevent invalid positions
     * @param {Object} position - Position to validate {x, y}
     * @param {Object} constraints - Constraint information
     * @returns {Object} Validation result
     */
    _validateDragBoundaries(position, constraints) {
        try {
            const errors = [];
            const warnings = [];
            let fallbackPosition = null;

            // Basic position validation
            if (!position || typeof position.x !== 'number' || typeof position.y !== 'number') {
                errors.push('Invalid position coordinates');
                fallbackPosition = { x: 0, y: 0 };
            }

            // Screen boundary validation (assuming screen coordinates)
            if (constraints && constraints.screenBounds) {
                const { screenBounds } = constraints;
                const margin = 10; // 10px margin from edges

                if (position.x < margin) {
                    errors.push('Position too close to left edge');
                    fallbackPosition = { ...position, x: margin };
                } else if (position.x > screenBounds.width - margin) {
                    errors.push('Position too close to right edge');
                    fallbackPosition = { ...position, x: screenBounds.width - margin };
                }

                if (position.y < margin) {
                    errors.push('Position too close to top edge');
                    fallbackPosition = { ...position, y: margin };
                } else if (position.y > screenBounds.height - margin) {
                    errors.push('Position too close to bottom edge');
                    fallbackPosition = { ...position, y: screenBounds.height - margin };
                }
            }

            // Orbital constraint validation
            if (constraints && constraints.type === 'orbit') {
                const { orbitCenter, orbitRadius } = constraints;
                if (orbitCenter && orbitRadius) {
                    const distance = Math.sqrt(
                        Math.pow(position.x - orbitCenter.x, 2) +
                        Math.pow(position.y - orbitCenter.y, 2)
                    );

                    const tolerance = 5; // 5px tolerance for orbital constraint
                    if (Math.abs(distance - orbitRadius) > tolerance) {
                        warnings.push('Position not precisely on orbit path');
                    }
                }
            }

            return {
                valid: errors.length === 0,
                errors: errors,
                warnings: warnings,
                fallbackPosition: fallbackPosition
            };

        } catch (error) {
            console.error('Error validating drag boundaries:', error);
            return {
                valid: false,
                errors: ['Boundary validation failed'],
                warnings: [],
                fallbackPosition: { x: 0, y: 0 }
            };
        }
    }

    /**
     * Generate SVG path data from points
     * @param {Array} points - Array of points with x, y coordinates
     * @returns {string} SVG path data string
     */
    _generateSVGPathData(points) {
        try {
            if (!points || points.length === 0) {
                return '';
            }

            let pathData = `M ${points[0].x} ${points[0].y}`;

            for (let i = 1; i < points.length; i++) {
                pathData += ` L ${points[i].x} ${points[i].y}`;
            }

            // Close the path for circular orbits
            pathData += ' Z';

            return pathData;

        } catch (error) {
            console.error('Error generating SVG path data:', error);
            return '';
        }
    }

    /**
     * Create orbit path element for visualization
     * @param {Object} pathData - Path data from generateOrbitPath
     * @param {Object} config - Visualization configuration
     * @returns {HTMLElement} SVG path element
     */
    _createOrbitPathElement(pathData, config) {
        try {
            // Remove existing orbit paths
            this.hideOrbitPath(false);

            // Create SVG container if it doesn't exist
            let svgContainer = document.querySelector('.orbit-visualization-svg');
            if (!svgContainer) {
                svgContainer = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
                svgContainer.classList.add('orbit-visualization-svg');
                svgContainer.style.position = 'absolute';
                svgContainer.style.top = '0';
                svgContainer.style.left = '0';
                svgContainer.style.width = '100%';
                svgContainer.style.height = '100%';
                svgContainer.style.pointerEvents = 'none';
                svgContainer.style.zIndex = '1000';

                // Find appropriate container (star map container)
                const container = document.querySelector('.star-map-container') ||
                    document.querySelector('.constellation-container') ||
                    document.body;
                container.appendChild(svgContainer);
            }

            // Create path element
            const pathElement = document.createElementNS('http://www.w3.org/2000/svg', 'path');
            pathElement.setAttribute('d', pathData.pathData);
            pathElement.setAttribute('stroke', config.strokeColor);
            pathElement.setAttribute('stroke-width', config.strokeWidth);
            pathElement.setAttribute('stroke-opacity', config.strokeOpacity);
            pathElement.setAttribute('fill', 'none');
            pathElement.classList.add(config.className);

            if (config.strokeDashArray) {
                pathElement.setAttribute('stroke-dasharray', config.strokeDashArray);
            }

            svgContainer.appendChild(pathElement);

            return pathElement;

        } catch (error) {
            console.error('Error creating orbit path element:', error);
            return null;
        }
    }

    /**
     * Animate orbit path visualization
     * @param {HTMLElement} pathElement - SVG path element
     * @param {Object} config - Animation configuration
     */
    _animateOrbitPath(pathElement, config) {
        try {
            if (!pathElement) return;

            // Fade in animation
            pathElement.style.opacity = '0';
            pathElement.style.transition = 'opacity 0.3s ease-in';

            // Trigger fade in
            setTimeout(() => {
                pathElement.style.opacity = config.strokeOpacity;
            }, 50);

            // Optional dash animation
            if (config.strokeDashArray && config.animationDuration > 0) {
                const pathLength = pathElement.getTotalLength();
                pathElement.style.strokeDasharray = `${pathLength} ${pathLength}`;
                pathElement.style.strokeDashoffset = pathLength;
                pathElement.style.transition = `stroke-dashoffset ${config.animationDuration}ms ease-in-out`;

                setTimeout(() => {
                    pathElement.style.strokeDashoffset = '0';
                }, 100);

                // Reset to original dash pattern after animation
                setTimeout(() => {
                    pathElement.style.strokeDasharray = config.strokeDashArray;
                    pathElement.style.strokeDashoffset = '0';
                }, config.animationDuration + 100);
            }

            // Auto fade out if configured
            if (config.fadeOut && config.animationDuration > 0) {
                setTimeout(() => {
                    this.hideOrbitPath(true);
                }, config.animationDuration + 2000);
            }

        } catch (error) {
            console.error('Error animating orbit path:', error);
        }
    }

    /**
     * Reset drag state to initial values
     */
    _resetDragState() {
        this.isDragging = false;
        this.currentStarId = null;
        this.dragStartPosition = null;
        this.lastTouchTime = 0;

        this.dragState = {
            startX: 0,
            startY: 0,
            currentX: 0,
            currentY: 0,
            deltaX: 0,
            deltaY: 0,
            totalDistance: 0,
            startTime: 0,
            lastMoveTime: 0
        };
    }

    /**
     * Cleanup resources
     */
    destroy() {
        this._resetDragState();
        this.eventListeners.clear();
        this.callbacks = {
            onDragStart: null,
            onDragMove: null,
            onDragEnd: null
        };

        console.log('TouchDragHandler destroyed');
    }
}

module.exports = TouchDragHandler;