{"description": "项目配置文件", "packOptions": {"ignore": [{"value": "cloudfunctions/", "type": "folder"}], "include": []}, "setting": {"urlCheck": false, "es6": true, "enhance": true, "postcss": true, "minified": true, "newFeature": true, "coverView": true, "nodeModules": true, "showShadowRootInWxmlPanel": true, "compileWorklet": false, "uglifyFileName": false, "uploadWithSourceMap": true, "packNpmManually": true, "packNpmRelationList": [{"packageJsonPath": "./package.json", "miniprogramNpmDistDir": "./"}], "minifyWXSS": true, "minifyWXML": true, "localPlugins": false, "disableUseStrict": false, "useCompilerPlugins": false, "condition": false, "swc": false, "disableSWC": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}}, "compileType": "miniprogram", "libVersion": "3.8.10", "appid": "wx4678da2fc1978a86", "projectname": "<PERSON><PERSON><PERSON>", "cloudfunctionRoot": "cloudfunctions/", "cloudfunctionTemplateRoot": "cloudfunctions/", "cloudbaseRoot": "cloudfunctions/", "envId": "cloudbase-8gji862jcfb501e7", "simulatorPluginLibVersion": {}, "editorSetting": {}, "condition": {}}