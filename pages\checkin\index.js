// pages/checkin/index.js
Page({
    data: {
      // 图片资源路径
      imagePaths: {
        background: 'cloud://cloudbase-8gji862jcfb501e7.636c-cloudbase-8gji862jcfb501e7-1365531166/images/checkin/checkin_bg_v2.webp',
        dim: 'cloud://cloudbase-8gji862jcfb501e7.636c-cloudbase-8gji862jcfb501e7-1365531166/images/checkin/star_dim.png',
        lit: 'cloud://cloudbase-8gji862jcfb501e7.636c-cloudbase-8gji862jcfb501e7-1365531166/images/checkin/star_lit.png',
        special: 'cloud://cloudbase-8gji862jcfb501e7.636c-cloudbase-8gji862jcfb501e7-1365531166/images/checkin/star_special.png'
      },
      
      // 水晶（星星）的数据列表
      // 【V2.3 最终精确校准】：严格遵循您的指示和最新截图，以第一、二颗星为基准，对后续所有星星的位置进行了最终的、像素级的精确计算和校准。
      starList: [
        { day: 1, position: { top: '1100rpx', left: '150rpx' }, isLit: false, isCurrent: true,  isSpecial: false, animationClass: '' }, // 基准点1，位置完美
        { day: 2, position: { top: '960rpx',  left: '230rpx' }, isLit: false, isCurrent: false, isSpecial: false, animationClass: '' }, // 基准点2，位置完美
        { day: 3, position: { top: '820rpx',  left: '350rpx' }, isLit: false, isCurrent: false, isSpecial: false, animationClass: '' }, // 精确校准
        { day: 4, position: { top: '680rpx',  left: '460rpx' }, isLit: false, isCurrent: false, isSpecial: false, animationClass: '' }, // 精确校准
        { day: 5, position: { top: '550rpx',  left: '350rpx' }, isLit: false, isCurrent: false, isSpecial: false, animationClass: '' }, // 精确校准
        { day: 6, position: { top: '420rpx',  left: '400rpx' }, isLit: false, isCurrent: false, isSpecial: false, animationClass: '' }, // 精确校准
        { day: 7, position: { top: '170rpx',  left: '550rpx' }, isLit: false, isCurrent: false, isSpecial: true,  animationClass: '' }  // 精确校准 (置于轨迹末端)
      ],
  
      // --- 页面状态变量 ---
      continuousDays: 0,
      buttonText: '点亮今日星星',
      isButtonDisabled: false,
      showRewardModal: false,
      rewardMessage: '',
      isShaking: false,
    },
  
    onLoad(options) {
      // 【后端对接点#1】：此处应替换为调用后端接口，获取用户真实的签到状态。
      this.updateStateByDay(1); 
    },
  
    handleCheckin() {
      this.setData({ isButtonDisabled: true });
      const currentDay = this.data.continuousDays + 1;
      const starIndex = this.data.starList.findIndex(s => s.day === currentDay);
      if (starIndex === -1) return;
  
      // 【后端对接点#2】：在前端触发动画之前，应先调用后端的“执行签到”接口。
      this.triggerAnimationAndShowReward(currentDay, starIndex);
    },
  
    triggerAnimationAndShowReward(currentDay, starIndex, backendReward) {
      let animationName = 'pop-out';
      if (currentDay === 7) {
        animationName = 'grand-prize';
        this.setData({ isShaking: true });
      }
      this.setData({
        [`starList[${starIndex}].animationClass`]: animationName
      });
  
      setTimeout(() => {
        const reward = this.generateReward(currentDay, backendReward);
        this.setData({
          showRewardModal: true,
          rewardMessage: reward.message,
          isShaking: false,
          [`starList[${starIndex}].animationClass`]: ''
        });
      }, 1500);
    },
  
    generateReward(day, backendReward) {
      // 【后端对接点#3】：在正式版本中，此函数应直接使用 backendReward 来生成 message。
      let message = '';
      if (day === 7) {
        message = '恭喜你，获得 光点 x 3 与 噗噗碎片 x 1';
      } else {
        const rand = Math.random();
        if (rand < 0.8) {
          message = '恭喜你，获得 光点 x 1';
        } else if (rand < 0.9) {
          message = '恭喜你，获得 故事馆章节券 x 1';
        } else {
          message = '恭喜你，获得 游戏厅8折券 x 1';
        }
      }
      return { message };
    },
  
    closeRewardModal() {
      this.setData({ showRewardModal: false });
      const currentDay = this.data.continuousDays + 1;
      const starIndex = this.data.starList.findIndex(s => s.day === currentDay);
  
      if (starIndex !== -1) {
        this.setData({
          [`starList[${starIndex}].isLit`]: true
        });
      }
  
      const nextDay = currentDay + 1;
      if (nextDay > 7) {
        setTimeout(() => this.resetCheckin(), 500);
      } else {
        this.updateStateByDay(nextDay);
      }
    },
  
    updateStateByDay(day) {
      const newStarList = this.data.starList.map(star => ({
        ...star,
        isLit: star.day < day,
        isCurrent: star.day === day,
        animationClass: ''
      }));
      this.setData({
        starList: newStarList,
        continuousDays: day - 1,
        buttonText: `点亮第 ${day} 天星星`,
        isButtonDisabled: false
      });
    },
  
    resetCheckin() {
      this.updateStateByDay(1);
      this.setData({ buttonText: '开启新一轮签到' });
    },
  
    closePage() {
      wx.navigateBack();
    },
  
    preventTouchMove() {}
  })
  