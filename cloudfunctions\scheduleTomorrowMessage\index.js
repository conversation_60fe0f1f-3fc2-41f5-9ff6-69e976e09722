const cloud = require('wx-server-sdk')

cloud.init({
    env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 错误类型定义
const ERROR_TYPES = {
    VALIDATION_ERROR: 'validation_error',
    DATABASE_ERROR: 'database_error',
    NETWORK_ERROR: 'network_error',
    SYSTEM_ERROR: 'system_error',
    TIMEOUT_ERROR: 'timeout_error'
}

// 重试配置
const RETRY_CONFIG = {
    maxRetries: 3,
    baseDelay: 1000,
    maxDelay: 8000,
    backoffFactor: 2
}

// 错误处理工具函数
function createErrorResponse(errorType, message, details = {}) {
    return {
        success: false,
        error: message,
        errorType,
        timestamp: new Date().toISOString(),
        details,
        retryable: isRetryableError(errorType)
    }
}

// 判断错误是否可重试
function isRetryableError(errorType) {
    const retryableErrors = [
        ERROR_TYPES.NETWORK_ERROR,
        ERROR_TYPES.TIMEOUT_ERROR,
        ERROR_TYPES.DATABASE_ERROR
    ]
    return retryableErrors.includes(errorType)
}

// 指数退避重试函数
async function retryWithBackoff(operation, options = {}) {
    const {
        maxRetries = RETRY_CONFIG.maxRetries,
        baseDelay = RETRY_CONFIG.baseDelay,
        maxDelay = RETRY_CONFIG.maxDelay,
        backoffFactor = RETRY_CONFIG.backoffFactor
    } = options

    let lastError

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
        try {
            return await operation()
        } catch (error) {
            lastError = error

            if (attempt === maxRetries) {
                throw error
            }

            // 计算延迟时间
            const delay = Math.min(baseDelay * Math.pow(backoffFactor, attempt), maxDelay)
            console.log(`消息调度失败，${delay}ms后进行第${attempt + 2}次重试:`, error.message)

            await new Promise(resolve => setTimeout(resolve, delay))
        }
    }

    throw lastError
}

// 记录错误到日志系统
async function logError(error, context, additionalData = {}) {
    try {
        const errorLog = {
            timestamp: new Date().toISOString(),
            context,
            error: {
                message: error.message,
                stack: error.stack,
                code: error.errCode || error.code
            },
            additionalData
        }

        console.error(`[消息调度错误日志] ${context}:`, errorLog)

        // 记录到分析系统
        try {
            await cloud.callFunction({
                name: 'tieringAnalytics',
                data: {
                    type: 'recordMessageScheduleError',
                    data: {
                        errorType: context,
                        errorMessage: error.message,
                        timestamp: new Date(),
                        additionalData
                    }
                }
            })
        } catch (analyticsError) {
            console.error('记录消息调度错误到分析系统失败:', analyticsError)
        }

    } catch (logError) {
        console.error('记录消息调度错误日志失败:', logError)
    }
}

// 创建集合的函数
async function ensureCollectionExists(collectionName) {
    try {
        await db.createCollection(collectionName)
        console.log(`集合 ${collectionName} 创建成功`)
    } catch (error) {
        if (error.errCode !== -501011) {
            console.error(`创建集合 ${collectionName} 失败`, error)
        }
    }
}

// 计算明天同一时间的投递时间
function calculateDeliveryTime(customTime = null) {
    const now = new Date()

    if (customTime) {
        // 如果提供了自定义时间，验证并使用
        const customDate = new Date(customTime)
        if (customDate > now) {
            return customDate
        }
        // 如果自定义时间已过期，则使用默认计算
    }

    // 计算明天同一时间
    const tomorrow = new Date(now)
    tomorrow.setDate(tomorrow.getDate() + 1)

    // 确保投递时间不会太早（至少在早上8点之后）
    if (tomorrow.getHours() < 8) {
        tomorrow.setHours(8, 0, 0, 0)
    }

    // 确保投递时间不会太晚（最晚在晚上22点之前）
    if (tomorrow.getHours() >= 22) {
        tomorrow.setHours(21, 30, 0, 0)
    }

    return tomorrow
}

// 验证投递时间是否合理
function validateDeliveryTime(deliveryTime) {
    const now = new Date()
    const maxFutureTime = new Date(now)
    maxFutureTime.setDate(maxFutureTime.getDate() + 7) // 最多7天后

    if (deliveryTime <= now) {
        return { valid: false, error: '投递时间不能是过去的时间' }
    }

    if (deliveryTime > maxFutureTime) {
        return { valid: false, error: '投递时间不能超过7天后' }
    }

    return { valid: true }
}

exports.main = async (event, context) => {
    const startTime = Date.now()
    let operationContext = 'scheduleTomorrowMessage'

    try {
        console.log('收到定时消息调度请求:', JSON.stringify(event))

        const {
            message,           // 留言内容
            sourceType = 'tomorrow_message',  // 消息来源类型
            sourceId,          // 关联的三问记录ID
            customDeliveryTime // 自定义投递时间（可选）
        } = event

        // 参数验证
        if (!message || message.trim().length === 0) {
            return createErrorResponse(ERROR_TYPES.VALIDATION_ERROR, '留言内容不能为空')
        }

        if (message.length > 100) {
            return createErrorResponse(ERROR_TYPES.VALIDATION_ERROR, '留言不超过100字')
        }

        const wxContext = cloud.getWXContext()
        const openid = wxContext.OPENID

        if (!openid) {
            return createErrorResponse(ERROR_TYPES.VALIDATION_ERROR, '用户身份验证失败')
        }

        console.log('当前用户 openid:', openid)

        // 使用重试机制执行数据库操作
        const result = await retryWithBackoff(async () => {
            // 确保集合存在
            await ensureCollectionExists('scheduled_messages')

            // 计算投递时间
            const deliveryTime = calculateDeliveryTime(customDeliveryTime)

            // 验证投递时间
            const timeValidation = validateDeliveryTime(deliveryTime)
            if (!timeValidation.valid) {
                throw new Error(timeValidation.error)
            }

            console.log('计算的投递时间:', deliveryTime)

            // 创建定时消息记录
            const messageRecord = await db.collection('scheduled_messages').add({
                data: {
                    openid,
                    message: message.trim(),
                    scheduledTime: deliveryTime,

                    // 投递状态
                    status: 'pending',
                    deliveredTime: null,
                    retryCount: 0,
                    maxRetries: 3,

                    // 关联信息
                    sourceType,
                    sourceId: sourceId || null,

                    // 时间戳
                    createTime: db.serverDate(),
                    updateTime: db.serverDate(),

                    // 错误处理相关
                    errorCount: 0,
                    lastError: null
                }
            })

            console.log('定时消息记录创建成功:', messageRecord)

            return {
                success: true,
                messageId: messageRecord._id,
                scheduledTime: deliveryTime,
                message: '留言已安排，将在明天同一时间为你送达',
                executionTime: Date.now() - startTime
            }
        })

        return result

    } catch (error) {
        console.error('创建定时消息失败:', error)

        // 记录错误到日志系统
        await logError(error, operationContext, {
            openid: cloud.getWXContext().OPENID,
            message: event.message?.substring(0, 50), // 限制长度
            sourceType: event.sourceType,
            executionTime: Date.now() - startTime
        })

        // 根据错误类型返回不同的响应
        if (error.message.includes('timeout')) {
            return createErrorResponse(ERROR_TYPES.TIMEOUT_ERROR, '操作超时，请重试', {
                executionTime: Date.now() - startTime
            })
        } else if (error.message.includes('network') || error.errCode === -1) {
            return createErrorResponse(ERROR_TYPES.NETWORK_ERROR, '网络连接失败，请检查网络后重试', {
                errorCode: error.errCode
            })
        } else if (error.message.includes('database') || error.message.includes('collection')) {
            return createErrorResponse(ERROR_TYPES.DATABASE_ERROR, '数据库操作失败，请稍后重试', {
                errorCode: error.errCode
            })
        } else if (error.message.includes('投递时间') || error.message.includes('留言')) {
            return createErrorResponse(ERROR_TYPES.VALIDATION_ERROR, error.message)
        } else {
            return createErrorResponse(ERROR_TYPES.SYSTEM_ERROR, '系统异常，请稍后重试', {
                errorCode: error.errCode,
                errorMessage: error.message
            })
        }

    } catch (outerError) {
        // 最外层错误处理 - 防止函数完全崩溃
        console.error('消息调度云函数执行发生严重错误:', outerError)

        await logError(outerError, 'scheduleTomorrowMessage_critical', {
            executionTime: Date.now() - startTime,
            originalContext: operationContext
        })

        return createErrorResponse(ERROR_TYPES.SYSTEM_ERROR, '系统发生严重错误，请联系技术支持', {
            errorCode: outerError.errCode || 'CRITICAL_ERROR',
            timestamp: new Date().toISOString()
        })
    }
}