@import "../../app.wxss";

.container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  z-index: 0;
}

.background {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: -1;
  opacity: 1;
  transition: opacity 2s ease-in-out;
  object-fit: cover;
  filter: brightness(0.85);
  transform: translateY(-15%);
}

.overlay {
  z-index: 10;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  min-height: 100vh;
  padding: 20px 24px 0;
}

.content-box {
  max-width: 480px;
  width: 100%;
  text-align: center;
  color: white;
  font-size: 17px;
  line-height: 2.2em;
  font-family: "Noto Serif SC", "Alibaba PuHuiTi", sans-serif;
}

.text-line {
  opacity: 0;
  animation: fadeIn 1s ease-in forwards;
  margin-bottom: 12px;
  text-shadow: 0 0 6px rgba(255, 255, 255, 0.3);
}

/* ✅ 更大更有承载感的输入框 */
.name-input {
  font-size: 15px;
  padding: 16px 20px;
  width: 95%;
  margin-top: 20px;
  border-radius: 14px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.08);
  color: white;
  backdrop-filter: blur(10px) brightness(1.2);
  -webkit-backdrop-filter: blur(10px) brightness(1.2);
  box-shadow: 0 0 8px rgba(255, 255, 255, 0.12), inset 0 0 5px rgba(255,255,255,0.08);
  outline: none;
  font-family: "Noto Serif SC", serif;
  transition: all 0.3s ease-in-out;
}

/* ✅ 更轻更淡的“没想好”按钮 */
.auto-button {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.4);
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 10px;
  padding: 5px 12px;
  margin-top: 12px;
  margin-bottom: 16px;
  backdrop-filter: blur(6px);
  -webkit-backdrop-filter: blur(6px);
  transition: all 0.3s ease-in-out;
  font-family: "Noto Serif SC", serif;
  display: inline-block;
}

/* ✅ ✅ ✅ 新增容器样式：把下一步单独包起来并居中 */
.next-button-wrapper {
  text-align: center;
  margin-top: 8px;
}

/* ✅ 更实、更稳、更居中的“下一步”按钮 */
.main-button {
  margin-top: 12px;
  padding: 10px 24px;
  font-size: 14px;
  background: rgba(255, 255, 255, 0.12);
  color: #ffffff;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 22px;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  font-family: "Noto Serif SC", serif;
  transition: all 0.3s ease-in-out;
  display: inline-block;
}

/* ✅ 浮现动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
