App({
  globalData: {
    menuButtonInfo: wx.getMenuButtonBoundingClientRect(),
    backgroundAudioManager: null
  },
  onLaunch() {
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力');
    } else {
      wx.cloud.init({
        env: 'cloudbase-8gji862jcfb501e7',  // 使用新的云环境ID
        traceUser: true
      });
    }

    // 初始化全局后台音频管理器
    this.globalData.backgroundAudioManager = wx.getBackgroundAudioManager();
    console.log('后台音频管理器已初始化');

    // 移除全局音频事件处理，由页面级别处理
  },



  onShow() {
    console.log('小程序切换到前台');
  },

  onHide() {
    console.log('小程序切换到后台，音频应该继续播放');
  }
});
