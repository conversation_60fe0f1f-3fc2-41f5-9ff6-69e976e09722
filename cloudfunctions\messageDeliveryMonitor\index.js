const cloud = require('wx-server-sdk')

cloud.init({
    env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 获取消息投递统计报告
async function getDeliveryReport(timeRange = 'today') {
    try {
        let startTime, endTime
        const now = new Date()

        switch (timeRange) {
            case 'today':
                startTime = new Date(now)
                startTime.setHours(0, 0, 0, 0)
                endTime = new Date(now)
                endTime.setHours(23, 59, 59, 999)
                break

            case 'week':
                startTime = new Date(now)
                startTime.setDate(now.getDate() - 7)
                startTime.setHours(0, 0, 0, 0)
                endTime = new Date(now)
                break

            case 'month':
                startTime = new Date(now)
                startTime.setDate(1)
                startTime.setHours(0, 0, 0, 0)
                endTime = new Date(now)
                break

            default:
                startTime = new Date(now)
                startTime.setHours(0, 0, 0, 0)
                endTime = new Date(now)
                endTime.setHours(23, 59, 59, 999)
        }

        // 获取基础统计
        const [totalMessages, deliveredMessages, failedMessages, pendingMessages] = await Promise.all([
            db.collection('scheduled_messages')
                .where({ createTime: _.gte(startTime).and(_.lte(endTime)) })
                .count(),
            db.collection('scheduled_messages')
                .where({
                    status: 'delivered',
                    deliveredTime: _.gte(startTime).and(_.lte(endTime))
                })
                .count(),
            db.collection('scheduled_messages')
                .where({
                    status: 'failed',
                    updateTime: _.gte(startTime).and(_.lte(endTime))
                })
                .count(),
            db.collection('scheduled_messages')
                .where({ status: 'pending' })
                .count()
        ])

        // 获取重试统计
        const retryMessages = await db.collection('scheduled_messages')
            .where({
                status: 'retry_pending',
                updateTime: _.gte(startTime).and(_.lte(endTime))
            })
            .count()

        // 计算成功率
        const totalProcessed = deliveredMessages.total + failedMessages.total
        const successRate = totalProcessed > 0 ? (deliveredMessages.total / totalProcessed * 100).toFixed(2) : 0

        // 获取投递渠道统计
        const channelStats = await getChannelStatistics(startTime, endTime)

        // 获取平均投递延迟
        const avgDelay = await getAverageDeliveryDelay(startTime, endTime)

        return {
            success: true,
            timeRange,
            period: {
                start: startTime.toISOString(),
                end: endTime.toISOString()
            },
            statistics: {
                total: totalMessages.total,
                delivered: deliveredMessages.total,
                failed: failedMessages.total,
                pending: pendingMessages.total,
                retrying: retryMessages.total,
                successRate: parseFloat(successRate)
            },
            channelStats,
            avgDelay
        }

    } catch (error) {
        console.error('获取投递报告失败:', error)
        return {
            success: false,
            error: error.message
        }
    }
}

// 获取投递渠道统计
async function getChannelStatistics(startTime, endTime) {
    try {
        const deliveredMessages = await db.collection('scheduled_messages')
            .where({
                status: 'delivered',
                deliveredTime: _.gte(startTime).and(_.lte(endTime))
            })
            .field({
                primaryChannel: true,
                deliveryChannels: true
            })
            .get()

        const channelCounts = {}

        deliveredMessages.data.forEach(msg => {
            const channel = msg.primaryChannel || 'unknown'
            channelCounts[channel] = (channelCounts[channel] || 0) + 1
        })

        return channelCounts

    } catch (error) {
        console.error('获取渠道统计失败:', error)
        return {}
    }
}

// 获取平均投递延迟
async function getAverageDeliveryDelay(startTime, endTime) {
    try {
        const deliveredMessages = await db.collection('scheduled_messages')
            .where({
                status: 'delivered',
                deliveredTime: _.gte(startTime).and(_.lte(endTime))
            })
            .field({
                scheduledTime: true,
                deliveredTime: true
            })
            .get()

        if (deliveredMessages.data.length === 0) {
            return { avgDelayMinutes: 0, count: 0 }
        }

        let totalDelay = 0
        deliveredMessages.data.forEach(msg => {
            const scheduled = new Date(msg.scheduledTime)
            const delivered = new Date(msg.deliveredTime)
            const delay = delivered.getTime() - scheduled.getTime()
            totalDelay += delay
        })

        const avgDelayMs = totalDelay / deliveredMessages.data.length
        const avgDelayMinutes = Math.round(avgDelayMs / (1000 * 60))

        return {
            avgDelayMinutes,
            count: deliveredMessages.data.length
        }

    } catch (error) {
        console.error('获取平均延迟失败:', error)
        return { avgDelayMinutes: 0, count: 0 }
    }
}

// 获取失败消息详情
async function getFailedMessages(limit = 10) {
    try {
        const failedMessages = await db.collection('scheduled_messages')
            .where({ status: 'failed' })
            .orderBy('updateTime', 'desc')
            .limit(limit)
            .field({
                openid: true,
                message: true,
                lastError: true,
                retryCount: true,
                updateTime: true,
                sourceType: true
            })
            .get()

        return {
            success: true,
            failedMessages: failedMessages.data
        }

    } catch (error) {
        console.error('获取失败消息失败:', error)
        return {
            success: false,
            error: error.message
        }
    }
}

// 重新调度失败的消息
async function rescheduleFailedMessage(messageId, newScheduledTime = null) {
    try {
        const updateData = {
            status: 'pending',
            retryCount: 0,
            lastError: null,
            updateTime: db.serverDate()
        }

        if (newScheduledTime) {
            updateData.scheduledTime = new Date(newScheduledTime)
        }

        const result = await db.collection('scheduled_messages')
            .doc(messageId)
            .update({ data: updateData })

        return {
            success: true,
            messageId,
            message: '消息已重新调度'
        }

    } catch (error) {
        console.error('重新调度消息失败:', error)
        return {
            success: false,
            error: error.message
        }
    }
}

// 清理过期消息
async function cleanupExpiredMessages(daysToKeep = 30) {
    try {
        const cutoffDate = new Date()
        cutoffDate.setDate(cutoffDate.getDate() - daysToKeep)

        // 删除已投递且超过保留期的消息
        const deliveredResult = await db.collection('scheduled_messages')
            .where({
                status: 'delivered',
                deliveredTime: _.lt(cutoffDate)
            })
            .remove()

        // 删除失败且超过保留期的消息
        const failedResult = await db.collection('scheduled_messages')
            .where({
                status: 'failed',
                updateTime: _.lt(cutoffDate)
            })
            .remove()

        const totalRemoved = deliveredResult.stats.removed + failedResult.stats.removed

        return {
            success: true,
            removedCount: totalRemoved,
            deliveredRemoved: deliveredResult.stats.removed,
            failedRemoved: failedResult.stats.removed
        }

    } catch (error) {
        console.error('清理过期消息失败:', error)
        return {
            success: false,
            error: error.message
        }
    }
}

exports.main = async (event, context) => {
    console.log('消息投递监控请求:', JSON.stringify(event))

    const {
        action = 'report',
        timeRange = 'today',
        messageId,
        newScheduledTime,
        limit = 10,
        daysToKeep = 30
    } = event

    try {
        switch (action) {
            case 'report':
                return await getDeliveryReport(timeRange)

            case 'failed':
                return await getFailedMessages(limit)

            case 'reschedule':
                if (!messageId) {
                    return {
                        success: false,
                        error: '缺少 messageId 参数'
                    }
                }
                return await rescheduleFailedMessage(messageId, newScheduledTime)

            case 'cleanup':
                return await cleanupExpiredMessages(daysToKeep)

            default:
                return {
                    success: false,
                    error: '不支持的操作类型',
                    supportedActions: ['report', 'failed', 'reschedule', 'cleanup']
                }
        }

    } catch (error) {
        console.error('消息投递监控操作失败:', error)
        return {
            success: false,
            error: error.message,
            errorCode: error.errCode
        }
    }
}