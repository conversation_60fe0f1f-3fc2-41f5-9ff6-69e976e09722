const cloud = require('wx-server-sdk')
const fetch = require('node-fetch')

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV })

const db = cloud.database()

exports.main = async (event, context) => {
  const { theme, summary } = event
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID

  console.log('generateDialogueSummary 接收到的参数:', { theme, summary, openid })

  try {
    // 从数据库获取最近保存的三问记录
    const threeQuestionsRes = await db.collection('three_questions_records')
      .where({ 
        openid: openid,
        theme: theme  // 增加主题匹配
      })
      .orderBy('createTime', 'desc')
      .limit(1)
      .get()

    console.log('第一次查询结果:', {
      dataLength: threeQuestionsRes.data.length,
      data: threeQuestionsRes.data
    })

    // 如果没有找到记录，尝试不带主题的查询
    if (threeQuestionsRes.data.length === 0) {
      const fallbackRes = await db.collection('three_questions_records')
        .where({ openid: openid })
        .orderBy('createTime', 'desc')
        .limit(1)
        .get()

      console.log('备选查询结果:', {
        dataLength: fallbackRes.data.length,
        data: fallbackRes.data
      })

      if (fallbackRes.data.length === 0) {
        console.error('未找到任何三问记录', { openid, theme })
        return {
          success: false,
          error: '未找到对应的三问记录'
        }
      }

      // 使用备选记录
      const threeQuestionsRecord = fallbackRes.data[0]
      const { questions, answers } = threeQuestionsRecord

      console.log('使用备选记录:', { questions, answers })

      // 调用AI生成情绪词和明天寄语
      const aiResponse = await generateEmotionAndMessage(theme, summary, questions, answers)

      return {
        success: true,
        threeQuestions: questions,
        threeAnswers: answers,
        emotionWord: aiResponse.emotionWord,
        tomorrowMessage: aiResponse.tomorrowMessage
      }
    }

    const threeQuestionsRecord = threeQuestionsRes.data[0]
    const { questions, answers } = threeQuestionsRecord

    console.log('使用主题匹配记录:', { questions, answers })

    // 调用AI生成情绪词和明天寄语
    const aiResponse = await generateEmotionAndMessage(theme, summary, questions, answers)

    return {
      success: true,
      threeQuestions: questions,
      threeAnswers: answers,
      emotionWord: aiResponse.emotionWord,
      tomorrowMessage: aiResponse.tomorrowMessage
    }
  } catch (error) {
    console.error('generateDialogueSummary 执行失败', error)
    return {
      success: false,
      error: error.message || '生成总结失败',
      errorStack: error.stack
    }
  }
}

async function generateEmotionAndMessage(theme, summary, questions, answers) {
  const OPENROUTER_API_KEY = '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'
  const OPENROUTER_API_URL = 'https://openrouter.ai/api/v1/chat/completions'
  const MODEL_NAME = 'google/gemini-2.5-flash'

  const prompt = `
基于以下对话主题和总结，以及用户对三个问题的回答，请帮我提取一个最能代表此次对话情感的词，并给出一句温暖、鼓舞人心的寄语。

对话主题：${theme}
对话总结：${summary}

三个问题和回答：
1. ${questions[0]}
   答：${answers[0]}

2. ${questions[1]}
   答：${answers[1]}

3. ${questions[2]}
   答：${answers[2]}

请按以下JSON格式返回：
{
  "emotionWord": "一个精准描述情感的词",
  "tomorrowMessage": "一句温暖的寄语，不超过20字"
}
`

  try {
    const response = await fetch(OPENROUTER_API_URL, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: MODEL_NAME,
        messages: [
          { role: 'system', content: '你是一个擅长情感分析和鼓励的AI助手。' },
          { role: 'user', content: prompt }
        ],
        response_format: { type: 'json_object' }
      })
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`AI服务错误: ${response.status} - ${errorText}`)
    }

    const data = await response.json()
    const result = JSON.parse(data.choices[0].message.content)

    return {
      emotionWord: result.emotionWord || '成长',
      tomorrowMessage: result.tomorrowMessage || '相信自己，每一天都是新的开始。'
    }
  } catch (error) {
    console.error('AI生成情感词和寄语失败', error)
    return {
      emotionWord: '成长',
      tomorrowMessage: '相信自己，每一天都是新的开始。'
    }
  }
} 