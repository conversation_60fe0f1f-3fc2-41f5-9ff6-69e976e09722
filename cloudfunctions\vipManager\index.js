const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

exports.main = async (event, context) => {
  const { 
    type, 
    openid,
    paymentInfo 
  } = event

  try {
    switch(type) {
      // 查询用户 VIP 状态
      case 'checkVipStatus':
        const userInfo = await db.collection('users').where({
          openid: openid
        }).get()

        return {
          success: true,
          isVip: userInfo.data[0]?.isVip || false,
          vipExpireTime: userInfo.data[0]?.vipExpireTime || null
        }

      // 开通/续费 VIP
      case 'purchaseVip':
        const vipDuration = paymentInfo.months || 1
        const vipPrice = vipDuration * 68 // 每月 68 元

        // 检查支付是否成功（这里应该调用微信支付验证）
        const paymentVerified = await verifyPayment(paymentInfo)

        if (!paymentVerified) {
          return { 
            success: false, 
            error: '支付验证失败' 
          }
        }

        // 计算 VIP 到期时间
        const currentTime = new Date()
        const expireTime = new Date(currentTime.setMonth(currentTime.getMonth() + vipDuration))

        // 更新用户 VIP 状态
        const updateResult = await db.collection('users').where({
          openid: openid
        }).update({
          data: {
            isVip: true,
            vipExpireTime: expireTime,
            vipLevel: vipDuration >= 12 ? 'annual' : 'monthly'
          }
        })

        return {
          success: true,
          vipLevel: vipDuration >= 12 ? 'annual' : 'monthly',
          expireTime: expireTime
        }

      // 获取 VIP 特权详情
      case 'getVipPrivileges':
        return {
          success: true,
          privileges: [
            { name: '每日免费光点', value: 4 },
            { name: 'AI日记生成', value: true },
            { name: '专属游戏内容', value: true },
            { name: '无限对话次数', value: true }
          ]
        }

      default:
        return { 
          success: false, 
          error: '未知的操作类型' 
        }
    }
  } catch (error) {
    console.error('VIP 管理错误：', error)
    return { 
      success: false, 
      error: error.message 
    }
  }
}

// 模拟支付验证（实际应接入微信支付验证）
async function verifyPayment(paymentInfo) {
  // 这里应该调用微信支付验证接口
  // 目前仅做模拟
  return true
} 