<view class="container">
  <image class="background" src="cloud://cloudbase-8gji862jcfb501e7.636c-cloudbase-8gji862jcfb501e7-1365531166/images/background.jpg" mode="aspectFill" />

  <!-- 🌫️ 漂浮微尘粒子 -->
  <view class="dust dust1"></view>
  <view class="dust dust2"></view>
  <view class="dust dust3"></view>

  <view class="overlay">
    <view class="content-box">

      <!-- ✨ 逐句浮现 -->
      <view wx:for="{{lines}}" wx:key="index" wx:if="{{visibleLines[index]}}" class="text-line">
        {{item}}
      </view>

      <!-- ✨ 光效动画 -->
      <view wx:if="{{showGlow}}" class="glow"></view>
      <view wx:if="{{showHalo}}" class="halo"></view>
      <view wx:if="{{showStars}}">
        <view class="star star1"></view>
        <view class="star star2"></view>
        <view class="star star3"></view>
      </view>

      <!-- ✅ 最终按钮 -->
      <view wx:if="{{showFinalButton}}" class="final-button">
        <button class="glass-button" bindtap="onNext">进入微光</button>
      </view>

    </view>
  </view>
</view>
