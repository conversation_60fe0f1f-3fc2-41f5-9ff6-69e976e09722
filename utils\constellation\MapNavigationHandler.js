/**
 * Map Navigation Handler
 * Handles zoom and pan functionality for the star constellation map
 */

class MapNavigationHandler {
    constructor() {
        this.zoomLevel = 1.0;
        this.minZoom = 0.5;
        this.maxZoom = 3.0;
        this.panX = 0;
        this.panY = 0;
        this.isDragging = false;
        this.lastTouchDistance = 0;
        this.lastTouchCenter = { x: 0, y: 0 };
        this.lastPanPosition = { x: 0, y: 0 };

        // Boundaries for panning (will be set based on zoom level)
        this.panBounds = {
            minX: 0,
            maxX: 0,
            minY: 0,
            maxY: 0
        };

        // Container dimensions
        this.containerWidth = 0;
        this.containerHeight = 0;

        // Touch event throttling
        this.lastTouchTime = 0;
        this.touchThrottleMs = 16; // ~60fps

        // Double tap detection
        this.lastTapTime = 0;
        this.doubleTapThreshold = 300; // ms
    }

    /**
     * Initialize the navigation handler
     * @param {Object} containerBounds - Container dimensions
     */
    initialize(containerBounds) {
        this.containerWidth = containerBounds.width;
        this.containerHeight = containerBounds.height;
        this.updatePanBounds();

        console.log('MapNavigationHandler initialized:', {
            containerWidth: this.containerWidth,
            containerHeight: this.containerHeight
        });

        return { success: true };
    }

    /**
     * Handle pinch-to-zoom gesture
     * @param {Array} touches - Array of touch points
     * @param {Function} onZoomChange - Callback for zoom changes
     */
    handlePinchZoom(touches, onZoomChange) {
        if (touches.length !== 2) {
            return { success: false, error: 'Pinch zoom requires exactly 2 touches' };
        }

        const now = Date.now();
        if (now - this.lastTouchTime < this.touchThrottleMs) {
            return { success: false, error: 'Touch event throttled' };
        }
        this.lastTouchTime = now;

        // Calculate distance between touches
        const touch1 = touches[0];
        const touch2 = touches[1];
        const distance = Math.sqrt(
            Math.pow(touch2.x - touch1.x, 2) + Math.pow(touch2.y - touch1.y, 2)
        );

        // Calculate center point between touches
        const centerX = (touch1.x + touch2.x) / 2;
        const centerY = (touch1.y + touch2.y) / 2;

        if (this.lastTouchDistance > 0) {
            // Calculate zoom factor
            const zoomFactor = distance / this.lastTouchDistance;
            const newZoomLevel = Math.max(this.minZoom, Math.min(this.maxZoom, this.zoomLevel * zoomFactor));

            if (newZoomLevel !== this.zoomLevel) {
                // Calculate zoom center relative to current pan position
                const zoomCenterX = (centerX - this.panX) / this.zoomLevel;
                const zoomCenterY = (centerY - this.panY) / this.zoomLevel;

                // Update zoom level
                const oldZoomLevel = this.zoomLevel;
                this.zoomLevel = newZoomLevel;

                // Adjust pan position to keep zoom center in place
                this.panX = centerX - zoomCenterX * this.zoomLevel;
                this.panY = centerY - zoomCenterY * this.zoomLevel;

                // Update pan boundaries and constrain position
                this.updatePanBounds();
                this.constrainPanPosition();

                // Trigger callback
                if (onZoomChange) {
                    onZoomChange({
                        zoomLevel: this.zoomLevel,
                        panX: this.panX,
                        panY: this.panY,
                        zoomCenter: { x: centerX, y: centerY },
                        zoomFactor: newZoomLevel / oldZoomLevel
                    });
                }
            }
        }

        this.lastTouchDistance = distance;
        this.lastTouchCenter = { x: centerX, y: centerY };

        return {
            success: true,
            zoomLevel: this.zoomLevel,
            panX: this.panX,
            panY: this.panY
        };
    }

    /**
     * Handle pan/drag gesture
     * @param {Object} touch - Touch point
     * @param {string} phase - 'start', 'move', or 'end'
     * @param {Function} onPanChange - Callback for pan changes
     */
    handlePan(touch, phase, onPanChange) {
        const now = Date.now();

        switch (phase) {
            case 'start':
                this.isDragging = true;
                this.lastPanPosition = { x: touch.x, y: touch.y };
                break;

            case 'move':
                if (!this.isDragging) return { success: false, error: 'Not in dragging state' };

                if (now - this.lastTouchTime < this.touchThrottleMs) {
                    return { success: false, error: 'Touch event throttled' };
                }
                this.lastTouchTime = now;

                // Calculate pan delta
                const deltaX = touch.x - this.lastPanPosition.x;
                const deltaY = touch.y - this.lastPanPosition.y;

                // Update pan position
                this.panX += deltaX;
                this.panY += deltaY;

                // Constrain to boundaries
                this.constrainPanPosition();

                // Update last position
                this.lastPanPosition = { x: touch.x, y: touch.y };

                // Trigger callback
                if (onPanChange) {
                    onPanChange({
                        panX: this.panX,
                        panY: this.panY,
                        deltaX: deltaX,
                        deltaY: deltaY
                    });
                }
                break;

            case 'end':
                this.isDragging = false;
                this.lastPanPosition = { x: 0, y: 0 };
                break;
        }

        return {
            success: true,
            panX: this.panX,
            panY: this.panY,
            isDragging: this.isDragging
        };
    }

    /**
     * Handle double tap to reset zoom
     * @param {Object} touch - Touch point
     * @param {Function} onZoomReset - Callback for zoom reset
     */
    handleDoubleTap(touch, onZoomReset) {
        const now = Date.now();

        if (now - this.lastTapTime < this.doubleTapThreshold) {
            // Double tap detected - reset zoom
            this.resetZoom();

            if (onZoomReset) {
                onZoomReset({
                    zoomLevel: this.zoomLevel,
                    panX: this.panX,
                    panY: this.panY
                });
            }

            this.lastTapTime = 0; // Reset to prevent triple tap
            return { success: true, action: 'reset', zoomLevel: this.zoomLevel };
        } else {
            this.lastTapTime = now;
            return { success: true, action: 'single_tap' };
        }
    }

    /**
     * Reset zoom to default level and center position
     */
    resetZoom() {
        this.zoomLevel = 1.0;
        this.panX = 0;
        this.panY = 0;
        this.updatePanBounds();

        console.log('Zoom reset to default');
    }

    /**
     * Update pan boundaries based on current zoom level
     */
    updatePanBounds() {
        if (this.zoomLevel <= 1.0) {
            // When zoomed out, no panning needed
            this.panBounds = {
                minX: 0,
                maxX: 0,
                minY: 0,
                maxY: 0
            };
        } else {
            // Calculate how much content extends beyond container
            const scaledWidth = this.containerWidth * this.zoomLevel;
            const scaledHeight = this.containerHeight * this.zoomLevel;

            const excessWidth = scaledWidth - this.containerWidth;
            const excessHeight = scaledHeight - this.containerHeight;

            this.panBounds = {
                minX: -excessWidth / 2,
                maxX: excessWidth / 2,
                minY: -excessHeight / 2,
                maxY: excessHeight / 2
            };
        }
    }

    /**
     * Constrain pan position to boundaries
     */
    constrainPanPosition() {
        this.panX = Math.max(this.panBounds.minX, Math.min(this.panBounds.maxX, this.panX));
        this.panY = Math.max(this.panBounds.minY, Math.min(this.panBounds.maxY, this.panY));
    }

    /**
     * Get current transform values for CSS
     */
    getTransform() {
        return {
            transform: `translate(${this.panX}px, ${this.panY}px) scale(${this.zoomLevel})`,
            transformOrigin: 'center center'
        };
    }

    /**
     * Get current navigation state
     */
    getState() {
        return {
            zoomLevel: this.zoomLevel,
            panX: this.panX,
            panY: this.panY,
            isDragging: this.isDragging,
            panBounds: { ...this.panBounds },
            canZoomIn: this.zoomLevel < this.maxZoom,
            canZoomOut: this.zoomLevel > this.minZoom
        };
    }

    /**
     * Set zoom level programmatically
     * @param {number} zoomLevel - Target zoom level
     * @param {Object} center - Optional zoom center point
     */
    setZoomLevel(zoomLevel, center = null) {
        const newZoomLevel = Math.max(this.minZoom, Math.min(this.maxZoom, zoomLevel));

        if (center) {
            // Zoom to specific point
            const zoomCenterX = (center.x - this.panX) / this.zoomLevel;
            const zoomCenterY = (center.y - this.panY) / this.zoomLevel;

            this.zoomLevel = newZoomLevel;

            this.panX = center.x - zoomCenterX * this.zoomLevel;
            this.panY = center.y - zoomCenterY * this.zoomLevel;
        } else {
            // Zoom to center
            this.zoomLevel = newZoomLevel;
        }

        this.updatePanBounds();
        this.constrainPanPosition();

        return {
            success: true,
            zoomLevel: this.zoomLevel,
            panX: this.panX,
            panY: this.panY
        };
    }

    /**
     * Convert screen coordinates to map coordinates
     * @param {Object} screenPoint - Screen coordinates
     */
    screenToMapCoordinates(screenPoint) {
        return {
            x: (screenPoint.x - this.panX) / this.zoomLevel,
            y: (screenPoint.y - this.panY) / this.zoomLevel
        };
    }

    /**
     * Convert map coordinates to screen coordinates
     * @param {Object} mapPoint - Map coordinates
     */
    mapToScreenCoordinates(mapPoint) {
        return {
            x: mapPoint.x * this.zoomLevel + this.panX,
            y: mapPoint.y * this.zoomLevel + this.panY
        };
    }

    /**
     * Check if a point is visible in the current viewport
     * @param {Object} mapPoint - Map coordinates
     */
    isPointVisible(mapPoint) {
        const screenPoint = this.mapToScreenCoordinates(mapPoint);

        return (
            screenPoint.x >= 0 &&
            screenPoint.x <= this.containerWidth &&
            screenPoint.y >= 0 &&
            screenPoint.y <= this.containerHeight
        );
    }

    /**
     * Get visible area bounds in map coordinates
     */
    getVisibleBounds() {
        const topLeft = this.screenToMapCoordinates({ x: 0, y: 0 });
        const bottomRight = this.screenToMapCoordinates({
            x: this.containerWidth,
            y: this.containerHeight
        });

        return {
            left: topLeft.x,
            top: topLeft.y,
            right: bottomRight.x,
            bottom: bottomRight.y,
            width: bottomRight.x - topLeft.x,
            height: bottomRight.y - topLeft.y
        };
    }
}

module.exports = MapNavigationHandler;