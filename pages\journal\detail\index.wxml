<view class="diary-detail-container">
  <view wx:if="{{diaryDetail}}" class="diary-header">
    <view class="diary-theme">
      <text>{{diaryDetail.dialogueTheme}}</text>
      <view wx:if="{{!isVip}}" class="vip-lock" bindtap="unlockFullDialogue">
        <text class="iconfont icon-lock">仅VIP可查看完整对话</text>
      </view>
    </view>
    <text class="diary-date">{{utils.formatDate(diaryDetail.createTime)}}</text>
  </view>

  <view class="dialogue-content">
    <block wx:for="{{dialogueContent}}" wx:key="index">
      <view 
        class="dialogue-item {{item.role === 'user' ? 'user-message' : 'ai-message'}}"
        wx:if="{{isVip || index < 4}}"
      >
        <view class="message-role">
          {{item.role === 'user' ? '我' : 'AI'}}
        </view>
        <view class="message-text">
          {{item.content}}
        </view>
      </view>
    </block>

    <view wx:if="{{!isVip && dialogueContent.length > 4}}" 
          class="vip-unlock-hint" 
          bindtap="unlockFullDialogue">
      <text>查看完整对话，请开通VIP</text>
    </view>
  </view>

  <view class="diary-summary">
    <text class="summary-label">AI总结：</text>
    <text class="summary-text">{{diaryDetail.aiSummary}}</text>
  </view>

  <view class="diary-emotions">
    <view class="emotion-item">
      <text class="emotion-label">此刻感受：</text>
      <text class="emotion-text">{{diaryDetail.emotionWord}}</text>
    </view>
    <view class="emotion-item">
      <text class="emotion-label">给明天的话：</text>
      <text class="emotion-text">{{diaryDetail.tomorrowMessage}}</text>
    </view>
  </view>
</view>

<wxs module="utils">
module.exports = {
  formatDate: function(timestamp) {
    var date = getDate(timestamp);
    return date.getFullYear() + '-' + 
           (date.getMonth() + 1).toString().padStart(2, '0') + '-' + 
           date.getDate().toString().padStart(2, '0')
  }
} 