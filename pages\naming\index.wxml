<view class="container">
  <image class="background" src="cloud://cloudbase-8gji862jcfb501e7.636c-cloudbase-8gji862jcfb501e7-1365531166/images/background.jpg" mode="aspectFill" />

  <view class="overlay">
    <view class="content-box">

      <!-- ✅ 浮现式文字 -->
      <view wx:for="{{currentLines}}" wx:key="index" wx:if="{{visibleLines[index]}}" class="text-line">
        {{item}}
      </view>

      <!-- ✅ 控制按钮与输入显示阶段 -->
      <view wx:if="{{showButton}}">

        <!-- ✅ 第一阶段：点亮你的微光 -->
        <view wx:if="{{stage === 0}}">
          <view style="margin-top: 20px; display: flex; justify-content: center;">
            <button class="main-button" bindtap="handleNext">点亮你的微光</button>
          </view>
        </view>

        <!-- ✅ 第二阶段：起名流程（给灯 or 给自己） -->
        <view wx:if="{{stage === 1 || stage === 2}}">

          <!-- ✅ 输入框单独包一层实现真正居中 -->
          <view style="margin-top: 24px; display: flex; justify-content: center;">
            <input
              class="name-input"
              placeholder="{{stage === 1 ? '给我起一个名字吧' : '给你自己一个名字吧'}}"
              bindinput="onInputChange"
              value="{{stage === 1 ? lightName : userName}}"
            />
          </view>

          <!-- ✅ “没想好”按钮独立一行 -->
          <view style="margin-top: 12px; display: flex; justify-content: center;">
            <button class="main-button" bindtap="autoGenerate">🔄 没想好？我可以帮你起一个</button>
          </view>

          <!-- ✅ “下一步”按钮再单独一行 -->
          <view style="margin-top: 12px; display: flex; justify-content: center;">
            <button class="main-button" bindtap="handleNext">下一步</button>
          </view>

        </view>
      </view>

    </view>
  </view>
</view>
