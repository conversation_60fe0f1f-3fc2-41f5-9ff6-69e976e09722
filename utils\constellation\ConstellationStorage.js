/**
 * 星座存储管理器
 * 负责星座数据的本地存储和云端同步
 */

class ConstellationStorage {
    constructor() {
        this.storageKeys = {
            constellation: 'constellation_data',
            starRecords: 'star_records',
            userPreferences: 'user_preferences',
            cacheVersion: 'cache_version',
            lastSyncTime: 'last_sync_time'
        }

        this.currentVersion = '1.0.0'
        this.maxCacheAge = 24 * 60 * 60 * 1000 // 24小时
        this.compressionEnabled = true
    }

    /**
     * 初始化存储管理器
     */
    initialize() {
        console.log('初始化星座存储管理器...')

        // 检查存储版本
        this.checkStorageVersion()

        // 清理过期缓存
        this.cleanupExpiredCache()

        return { success: true }
    }

    /**
     * 保存星座数据到本地
     */
    async saveConstellationData(constellationData) {
        try {
            // 验证数据格式
            const validationResult = this.validateConstellationData(constellationData)
            if (!validationResult.valid) {
                throw new Error(`数据验证失败: ${validationResult.errors.join(', ')}`)
            }

            // 压缩数据（如果启用）
            const dataToStore = this.compressionEnabled ?
                this.compressData(constellationData) : constellationData

            // 保存到本地存储
            wx.setStorageSync(this.storageKeys.constellation, dataToStore)

            // 更新时间戳
            wx.setStorageSync(this.storageKeys.lastSyncTime, Date.now())

            console.log('星座数据保存成功')
            return { success: true }

        } catch (error) {
            console.error('保存星座数据失败:', error)
            return { success: false, error: error.message }
        }
    }

    /**
     * 从本地加载星座数据
     */
    async loadConstellationData() {
        try {
            const storedData = wx.getStorageSync(this.storageKeys.constellation)

            if (!storedData) {
                console.log('没有找到本地星座数据')
                return { success: true, data: null }
            }

            // 解压数据（如果需要）
            const decompressedData = this.compressionEnabled ?
                this.decompressData(storedData) : storedData

            // 验证加载的数据
            const validationResult = this.validateConstellationData(decompressedData)
            if (!validationResult.valid) {
                console.warn('本地数据验证失败:', validationResult.errors)
                return { success: false, error: '本地数据损坏' }
            }

            console.log('星座数据加载成功')
            return { success: true, data: decompressedData }

        } catch (error) {
            console.error('加载星座数据失败:', error)
            return { success: false, error: error.message }
        }
    }

    /**
     * 保存星星记录
     */
    async saveStarRecords(starRecords) {
        try {
            if (!Array.isArray(starRecords)) {
                throw new Error('星星记录必须是数组格式')
            }

            // 压缩数据
            const dataToStore = this.compressionEnabled ?
                this.compressData(starRecords) : starRecords

            wx.setStorageSync(this.storageKeys.starRecords, dataToStore)

            console.log(`保存了 ${starRecords.length} 条星星记录`)
            return { success: true, count: starRecords.length }

        } catch (error) {
            console.error('保存星星记录失败:', error)
            return { success: false, error: error.message }
        }
    }

    /**
     * 加载星星记录
     */
    async loadStarRecords() {
        try {
            const storedData = wx.getStorageSync(this.storageKeys.starRecords)

            if (!storedData) {
                return { success: true, data: [] }
            }

            // 解压数据
            const decompressedData = this.compressionEnabled ?
                this.decompressData(storedData) : storedData

            if (!Array.isArray(decompressedData)) {
                throw new Error('星星记录数据格式错误')
            }

            console.log(`加载了 ${decompressedData.length} 条星星记录`)
            return { success: true, data: decompressedData }

        } catch (error) {
            console.error('加载星星记录失败:', error)
            return { success: false, error: error.message }
        }
    }

    /**
     * 清除所有本地数据
     */
    async clearAllData() {
        try {
            Object.values(this.storageKeys).forEach(key => {
                try {
                    wx.removeStorageSync(key)
                } catch (error) {
                    console.warn(`清除 ${key} 失败:`, error)
                }
            })

            console.log('所有本地数据已清除')
            return { success: true }

        } catch (error) {
            console.error('清除数据失败:', error)
            return { success: false, error: error.message }
        }
    }

    /**
     * 检查存储版本
     */
    checkStorageVersion() {
        try {
            const storedVersion = wx.getStorageSync(this.storageKeys.cacheVersion)

            if (storedVersion !== this.currentVersion) {
                console.log(`存储版本更新: ${storedVersion} -> ${this.currentVersion}`)

                // 清理旧版本数据
                this.migrateStorageVersion(storedVersion, this.currentVersion)

                // 更新版本号
                wx.setStorageSync(this.storageKeys.cacheVersion, this.currentVersion)
            }

        } catch (error) {
            console.error('检查存储版本失败:', error)
        }
    }

    /**
     * 迁移存储版本
     */
    migrateStorageVersion(oldVersion, newVersion) {
        console.log(`迁移存储版本: ${oldVersion} -> ${newVersion}`)

        // 根据版本差异执行不同的迁移策略
        if (!oldVersion) {
            // 首次安装，无需迁移
            return
        }

        // 这里可以添加具体的版本迁移逻辑
        // 例如数据格式转换、字段重命名等
    }

    /**
     * 清理过期缓存
     */
    cleanupExpiredCache() {
        try {
            const lastSyncTime = wx.getStorageSync(this.storageKeys.lastSyncTime)

            if (lastSyncTime && Date.now() - lastSyncTime > this.maxCacheAge) {
                console.log('清理过期缓存...')

                // 清理过期的缓存数据，但保留核心数据
                const keysToClean = ['temp_cache', 'animation_cache', 'performance_cache']

                keysToClean.forEach(key => {
                    try {
                        wx.removeStorageSync(key)
                    } catch (error) {
                        console.warn(`清理 ${key} 失败:`, error)
                    }
                })
            }

        } catch (error) {
            console.error('清理过期缓存失败:', error)
        }
    }

    /**
     * 获取存储统计信息
     */
    getStorageStats() {
        const stats = {
            totalKeys: 0,
            totalSize: 0,
            keyDetails: {}
        }

        try {
            Object.entries(this.storageKeys).forEach(([name, key]) => {
                try {
                    const data = wx.getStorageSync(key)
                    if (data) {
                        const size = JSON.stringify(data).length
                        stats.keyDetails[name] = {
                            key: key,
                            size: size,
                            hasData: true
                        }
                        stats.totalSize += size
                        stats.totalKeys++
                    } else {
                        stats.keyDetails[name] = {
                            key: key,
                            size: 0,
                            hasData: false
                        }
                    }
                } catch (error) {
                    stats.keyDetails[name] = {
                        key: key,
                        size: 0,
                        hasData: false,
                        error: error.message
                    }
                }
            })

        } catch (error) {
            console.error('获取存储统计失败:', error)
        }

        return stats
    }

    /**
     * 验证星座数据格式
     */
    validateConstellationData(data) {
        const errors = []

        if (!data || typeof data !== 'object') {
            errors.push('数据不能为空')
            return { valid: false, errors }
        }

        if (!data.stars || !Array.isArray(data.stars)) {
            errors.push('星星数据必须是数组')
        }

        if (!data.metadata) {
            errors.push('缺少元数据')
        }

        // 验证每个星星数据
        if (data.stars) {
            data.stars.forEach((star, index) => {
                if (!star.id) {
                    errors.push(`星星 ${index} 缺少ID`)
                }
                if (!star.position) {
                    errors.push(`星星 ${index} 缺少位置信息`)
                }
            })
        }

        return { valid: errors.length === 0, errors }
    }

    /**
     * 压缩数据
     */
    compressData(data) {
        try {
            const jsonString = JSON.stringify(data)

            // 简单的压缩：移除空格和换行
            const compressed = jsonString.replace(/\s+/g, ' ').trim()

            return {
                compressed: true,
                data: compressed,
                originalSize: jsonString.length
            }
        } catch (error) {
            console.warn('数据压缩失败，使用原始数据:', error)
            return data
        }
    }

    /**
     * 解压数据
     */
    decompressData(compressedData) {
        try {
            if (compressedData.compressed) {
                return JSON.parse(compressedData.data)
            }
            return compressedData
        } catch (error) {
            console.error('数据解压失败:', error)
            return null
        }
    }

    /**
     * 销毁存储管理器
     */
    destroy() {
        console.log('星座存储管理器已销毁')
    }
}

module.exports = ConstellationStorage;