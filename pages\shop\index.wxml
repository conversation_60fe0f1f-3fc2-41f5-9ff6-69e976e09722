<view class="container">
  <view class="product-list">
    <view class="product-item" wx:for="{{products}}" wx:key="id">
      <image class="product-image" src="{{item.image}}" mode="aspectFit"/>
      <view class="product-info">
        <text class="product-name">{{item.name}}</text>
        <text class="product-price">¥{{item.price}}</text>
        <text class="product-points" wx:if="{{item.points}}">可获得 {{item.points}} 光点</text>
        <text class="product-type" wx:if="{{item.type === 'vip'}}">VIP特权</text>
      </view>
      <button 
        class="buy-btn" 
        data-id="{{item.id}}" 
        bindtap="handleBuy"
        loading="{{loading}}"
        disabled="{{loading}}"
      >购买</button>
    </view>
  </view>
</view> 