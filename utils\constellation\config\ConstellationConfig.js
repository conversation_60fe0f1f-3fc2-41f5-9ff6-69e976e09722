/**
 * Configuration Constants for Interactive Star Constellation System
 * Defines orbital radii, animation parameters, and system constants
 */

/**
 * Orbital Configuration
 */
const ORBITAL_CONFIG = {
    // Fixed orbital radius as percentage of screen width
    RADIUS_PERCENT: 0.15,

    // Minimum orbital radius in pixels (fallback for very small screens)
    MIN_RADIUS_PX: 50,

    // Maximum orbital radius in pixels (prevent stars from being too far)
    MAX_RADIUS_PX: 200,

    // Margin from screen edges for orbital calculations (percentage)
    SCREEN_MARGIN_PERCENT: 0.1,

    // Auto-adjustment factor when orbit exceeds screen bounds
    RADIUS_ADJUSTMENT_FACTOR: 0.8
};

/**
 * Animation Configuration
 */
const ANIMATION_CONFIG = {
    // Star lighting flash effect duration (ms)
    LIGHTING_FLASH_DURATION: 1000,

    // Star appearance animation duration (ms)
    STAR_APPEARANCE_DURATION: 800,

    // Orbital path visualization duration (ms)
    ORBIT_PATH_DURATION: 2000,

    // Star glow animation cycle duration (ms)
    GLOW_CYCLE_DURATION: 3000,

    // Drag animation easing
    DRAG_EASING: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',

    // Placement confirmation animation duration (ms)
    PLACEMENT_CONFIRM_DURATION: 500,

    // Animation frame rate target (fps)
    TARGET_FPS: 60,

    // Touch event throttle interval (ms)
    TOUCH_THROTTLE_MS: 16
};

/**
 * Visual Configuration
 */
const VISUAL_CONFIG = {
    // Star size range
    STAR_SIZE: {
        MIN: 0.5,
        MAX: 2.0,
        DEFAULT: 1.0
    },

    // Star brightness range
    BRIGHTNESS: {
        MIN: 0.3,
        MAX: 1.0,
        DEFAULT: 0.8
    },

    // Glow intensity range
    GLOW_INTENSITY: {
        MIN: 0.0,
        MAX: 1.0,
        DEFAULT: 0.5
    },

    // Emotion-based color mapping
    EMOTION_COLORS: {
        '开心': '#FFD700',    // Gold
        '快乐': '#FFD700',    // Gold
        '喜悦': '#FFD700',    // Gold
        '平静': '#87CEEB',    // Sky Blue
        '安静': '#87CEEB',    // Sky Blue
        '宁静': '#87CEEB',    // Sky Blue
        '困惑': '#DDA0DD',    // Plum
        '迷茫': '#DDA0DD',    // Plum
        '纠结': '#DDA0DD',    // Plum
        '焦虑': '#F0A0A0',    // Light Coral
        '担心': '#F0A0A0',    // Light Coral
        '紧张': '#F0A0A0',    // Light Coral
        '感动': '#FFB6C1',    // Light Pink
        '温暖': '#FFB6C1',    // Light Pink
        '治愈': '#98FB98',    // Pale Green
        'DEFAULT': '#FFFFFF'   // White
    },

    // Orbital path visualization
    ORBIT_PATH: {
        COLOR: 'rgba(255, 255, 255, 0.3)',
        WIDTH: 2,
        DASH_PATTERN: [5, 5]
    }
};

/**
 * Interaction Configuration
 */
const INTERACTION_CONFIG = {
    // First star placement constraints
    FIRST_STAR: {
        // Margin from screen edges (percentage)
        EDGE_MARGIN_PERCENT: 0.1,

        // Minimum distance from edges (pixels)
        MIN_EDGE_DISTANCE_PX: 30
    },

    // Touch/drag sensitivity
    TOUCH: {
        // Minimum drag distance to register as drag (pixels)
        MIN_DRAG_DISTANCE: 5,

        // Maximum touch points for multi-touch
        MAX_TOUCH_POINTS: 2,

        // Touch target size (pixels)
        TOUCH_TARGET_SIZE: 44,

        // Haptic feedback intensity (0-1)
        HAPTIC_INTENSITY: 0.5
    },

    // Zoom and pan configuration
    ZOOM: {
        MIN_SCALE: 0.5,
        MAX_SCALE: 3.0,
        DEFAULT_SCALE: 1.0,
        ZOOM_STEP: 0.1,

        // Double-tap zoom level
        DOUBLE_TAP_ZOOM: 1.5
    },

    // Pan boundaries (percentage beyond constellation bounds)
    PAN_BOUNDARY_PERCENT: 0.2
};

/**
 * Data Configuration
 */
const DATA_CONFIG = {
    // Local storage keys
    STORAGE_KEYS: {
        CONSTELLATION_DATA: 'constellation_data',
        USER_SETTINGS: 'constellation_settings',
        CACHE_VERSION: 'constellation_cache_version'
    },

    // Data version for migration
    CURRENT_VERSION: 1,

    // Cache configuration
    CACHE: {
        // Maximum stars to cache locally
        MAX_CACHED_STARS: 100,

        // Cache expiration time (ms)
        EXPIRATION_TIME: 24 * 60 * 60 * 1000, // 24 hours

        // Sync retry attempts
        MAX_SYNC_RETRIES: 3,

        // Sync retry delay (ms)
        SYNC_RETRY_DELAY: 1000
    },

    // Cloud function names
    CLOUD_FUNCTIONS: {
        GET_CONSTELLATION: 'getUserHistory',
        SAVE_CONSTELLATION: 'saveThreeQuestions',
        UPDATE_STAR_POSITION: 'updateStarPosition'
    }
};

/**
 * Performance Configuration
 */
const PERFORMANCE_CONFIG = {
    // Rendering optimization
    RENDERING: {
        // Maximum stars to render without culling
        MAX_STARS_NO_CULLING: 20,

        // Viewport padding for culling (percentage)
        CULLING_PADDING_PERCENT: 0.1,

        // LOD (Level of Detail) distance thresholds
        LOD_THRESHOLDS: {
            HIGH_DETAIL: 1.0,    // Full detail
            MEDIUM_DETAIL: 0.5,  // Simplified rendering
            LOW_DETAIL: 0.25     // Minimal rendering
        }
    },

    // Memory management
    MEMORY: {
        // Maximum animation objects in pool
        MAX_ANIMATION_POOL_SIZE: 50,

        // Garbage collection interval (ms)
        GC_INTERVAL: 30000,

        // Maximum cached textures
        MAX_CACHED_TEXTURES: 20
    }
};

/**
 * Error Handling Configuration
 */
const ERROR_CONFIG = {
    // Retry configuration
    RETRY: {
        MAX_ATTEMPTS: 3,
        BASE_DELAY: 1000,
        BACKOFF_MULTIPLIER: 2
    },

    // Error types
    ERROR_TYPES: {
        PLACEMENT_ERROR: 'PLACEMENT_ERROR',
        NETWORK_ERROR: 'NETWORK_ERROR',
        VALIDATION_ERROR: 'VALIDATION_ERROR',
        ANIMATION_ERROR: 'ANIMATION_ERROR',
        STORAGE_ERROR: 'STORAGE_ERROR'
    },

    // Recovery strategies
    RECOVERY_STRATEGIES: {
        PLACEMENT_ERROR: 'suggest_alternative_position',
        NETWORK_ERROR: 'cache_and_retry',
        VALIDATION_ERROR: 'use_default_values',
        ANIMATION_ERROR: 'fallback_to_simple_animation',
        STORAGE_ERROR: 'use_memory_storage'
    }
};

/**
 * Development Configuration
 */
const DEV_CONFIG = {
    // Debug flags
    DEBUG: {
        ENABLE_LOGGING: true,
        SHOW_ORBIT_PATHS: false,
        SHOW_TOUCH_INDICATORS: false,
        PERFORMANCE_MONITORING: true
    },

    // Testing configuration
    TESTING: {
        MOCK_CLOUD_FUNCTIONS: false,
        SIMULATE_NETWORK_DELAY: 0,
        ENABLE_PERFORMANCE_TESTS: false
    }
};

/**
 * Get configuration value with fallback
 * @param {string} path - Configuration path (e.g., 'ORBITAL_CONFIG.RADIUS_PERCENT')
 * @param {*} defaultValue - Default value if path not found
 * @returns {*} Configuration value
 */
function getConfig(path, defaultValue = null) {
    const pathParts = path.split('.');
    let current = module.exports;

    for (const part of pathParts) {
        if (current && typeof current === 'object' && part in current) {
            current = current[part];
        } else {
            return defaultValue;
        }
    }

    return current;
}

/**
 * Validate configuration on module load
 */
function validateConfig() {
    const errors = [];

    // Validate orbital radius
    if (ORBITAL_CONFIG.RADIUS_PERCENT <= 0 || ORBITAL_CONFIG.RADIUS_PERCENT > 0.5) {
        errors.push('ORBITAL_CONFIG.RADIUS_PERCENT must be between 0 and 0.5');
    }

    // Validate animation durations
    if (ANIMATION_CONFIG.LIGHTING_FLASH_DURATION <= 0) {
        errors.push('ANIMATION_CONFIG.LIGHTING_FLASH_DURATION must be positive');
    }

    // Validate zoom limits
    if (INTERACTION_CONFIG.ZOOM.MIN_SCALE >= INTERACTION_CONFIG.ZOOM.MAX_SCALE) {
        errors.push('ZOOM.MIN_SCALE must be less than ZOOM.MAX_SCALE');
    }

    if (errors.length > 0) {
        console.warn('Constellation configuration validation errors:', errors);
    }

    return errors.length === 0;
}

// Validate configuration on load
validateConfig();

module.exports = {
    ORBITAL_CONFIG,
    ANIMATION_CONFIG,
    VISUAL_CONFIG,
    INTERACTION_CONFIG,
    DATA_CONFIG,
    PERFORMANCE_CONFIG,
    ERROR_CONFIG,
    DEV_CONFIG,
    getConfig,
    validateConfig
};