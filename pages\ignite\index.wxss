page {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
  background-color: black;
}

.container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  z-index: 0;
}

.background {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: -1;
  opacity: 1;
  transition: opacity 2s ease-in-out;
  object-fit: cover;
  filter: brightness(0.85);
  transform: translateY(-15%);
}

.overlay {
  z-index: 10;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  min-height: 100vh;
  padding: 60px 24px 0;
}

.content-box {
  max-width: 480px;
  width: 100%;
  text-align: center;
  color: white;
  font-size: 17px;
  line-height: 2.2em;
  font-family: "Noto Serif SC", "Alibaba PuHuiTi", sans-serif;
}

.text-line {
  opacity: 0;
  animation: fadeIn 1s ease-in forwards;
  margin-bottom: 12px;
  text-shadow: 0 0 6px rgba(255, 255, 255, 0.3);
}

.glass-button {
  background: rgba(255, 255, 255, 0.05);
  color: #e3e6f0;
  font-size: 15px;
  border-radius: 12px;
  padding: 10px 18px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  font-family: "Noto Serif SC", serif;
  opacity: 1;
}

.final-button {
  margin-top: 40px;
  animation: fadeIn 1s ease-in forwards;
}

/* 🌕 光圈核心升级 */
.halo {
  position: absolute;
  bottom: 16%;
  left: 50%;
  transform: translateX(-50%);
  width: 220px;
  height: 220px;
  border-radius: 50%;
  background: radial-gradient(circle,
    rgba(255, 255, 200, 0.95) 0%,
    rgba(255, 255, 150, 0.4) 40%,
    rgba(255, 255, 255, 0.05) 80%,
    transparent 100%);
  box-shadow:
    0 0 20px rgba(255,255,120,0.6),
    0 0 40px rgba(255,255,180,0.4);
  filter: blur(4px);
  animation: haloGlow 7s ease-out forwards, lightDrift 8s ease-in-out infinite;
  z-index: 4;
}

/* 🌙 光晕更柔和且浮动 */
.glow {
  position: absolute;
  bottom: 18%;
  left: 50%;
  transform: translateX(-50%);
  width: 300px;
  height: 300px;
  border-radius: 50%;
  background: radial-gradient(circle,
    rgba(255,255,255,0.2) 0%,
    rgba(255,255,255,0.05) 60%,
    transparent 100%);
  filter: blur(8px);
  animation: glowPulse 7s ease-out forwards, lightDrift 10s ease-in-out infinite;
  z-index: 3;
}

/* ✨ 星星升级：亮晶晶 */
.star {
  position: absolute;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: radial-gradient(circle, white 60%, rgba(255,255,255,0.2) 80%, transparent 100%);
  box-shadow:
    0 0 6px rgba(255, 255, 255, 0.8),
    0 0 12px rgba(255, 255, 255, 0.4),
    0 0 18px rgba(255, 255, 255, 0.2);
  animation: twinkle 4s ease-in-out infinite;
  z-index: 2;
}

.star1 { top: 12%; right: 18%; animation-delay: 0s; }
.star2 { top: 38%; left: 14%; animation-delay: 1s; }
.star3 { top: 55%; left: 48%; animation-delay: 2s; }

/* 🌫️ 微尘粒子 */
.dust {
  position: absolute;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: rgba(255,255,255,0.05);
  animation: floatDust 12s infinite ease-in-out;
  z-index: 1;
}
.dust1 { top: 20%; left: 30%; animation-delay: 0s; }
.dust2 { top: 50%; left: 60%; animation-delay: 3s; }
.dust3 { top: 35%; left: 20%; animation-delay: 6s; }

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes haloGlow {
  0% { transform: translateX(-50%) scale(0.2); opacity: 0; }
  50% { transform: translateX(-50%) scale(1); opacity: 0.8; }
  100% { transform: translateX(-50%) scale(1.4); opacity: 0; }
}

@keyframes glowPulse {
  0% { transform: translateX(-50%) scale(0.5); opacity: 0; }
  50% { transform: translateX(-50%) scale(1); opacity: 1; }
  100% { transform: translateX(-50%) scale(1.5); opacity: 0; }
}

@keyframes twinkle {
  0%, 100% { opacity: 0.6; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.3); }
}

@keyframes lightDrift {
  0% { transform: translateX(-50%) scale(1) rotate(0deg); }
  50% { transform: translateX(-50%) scale(1.03) rotate(0.3deg); }
  100% { transform: translateX(-50%) scale(1) rotate(0deg); }
}

@keyframes floatDust {
  0% { transform: translateY(0); opacity: 0.2; }
  50% { transform: translateY(-30px); opacity: 0.5; }
  100% { transform: translateY(0); opacity: 0.2; }
}
