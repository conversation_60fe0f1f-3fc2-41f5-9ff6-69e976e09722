/**
 * Interactive Star Constellation System
 * Main entry point for constellation functionality
 */

// Import all constellation modules
const { StarLightingManager } = require('./StarLightingManager');
const ConstellationBuilder = require('./ConstellationBuilder');
const TouchDragHandler = require('./TouchDragHandler');
const AnimationEngine = require('./AnimationEngine');
const InformationDisplay = require('./InformationDisplay');
const ConstellationStorage = require('./ConstellationStorage');
const PlacementValidator = require('./PlacementValidator').default;
const ConstellationRecovery = require('./ConstellationRecovery').default;
const MapNavigationHandler = require('./MapNavigationHandler');
const StarCullingManager = require('./StarCullingManager');
const ProgressiveLoader = require('./ProgressiveLoader');
const TouchEventOptimizer = require('./TouchEventOptimizer');

// Import configuration and models
const ConstellationConfig = require('./config/ConstellationConfig');
const { StarModel, ConstellationModel } = require('./models/DataModels');

/**
 * Main Constellation System Class
 * Coordinates all constellation functionality
 */
class ConstellationSystem {
    constructor() {
        this.starLightingManager = new StarLightingManager();
        this.constellationBuilder = new ConstellationBuilder();
        this.touchDragHandler = new TouchDragHandler();
        this.animationEngine = new AnimationEngine();
        this.informationDisplay = new InformationDisplay();
        this.storage = new ConstellationStorage();
        this.validator = PlacementValidator;
        this.recovery = ConstellationRecovery;
        this.mapNavigationHandler = new MapNavigationHandler();
        this.starCullingManager = new StarCullingManager();
        this.progressiveLoader = new ProgressiveLoader();
        this.touchEventOptimizer = new TouchEventOptimizer();
    }

    /**
     * Initialize the constellation system
     * @param {Object} options - Initialization options
     */
    async initialize(options = {}) {
        try {
            // Initialize all subsystems
            await this.storage.initialize();
            this.animationEngine.initialize();
            this.touchDragHandler.initialize();

            console.log('Constellation system initialized successfully');
            return { success: true };
        } catch (error) {
            console.error('Failed to initialize constellation system:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Get constellation system instance (singleton pattern)
     */
    static getInstance() {
        if (!ConstellationSystem.instance) {
            ConstellationSystem.instance = new ConstellationSystem();
        }
        return ConstellationSystem.instance;
    }
}

module.exports = {
    ConstellationSystem,
    StarLightingManager,
    ConstellationBuilder,
    TouchDragHandler,
    AnimationEngine,
    InformationDisplay,
    ConstellationStorage,
    PlacementValidator,
    ConstellationRecovery,
    MapNavigationHandler,
    StarCullingManager,
    ProgressiveLoader,
    TouchEventOptimizer,
    ConstellationConfig,
    StarModel,
    ConstellationModel
};