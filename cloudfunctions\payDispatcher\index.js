const crypto = require('crypto');
const fs = require('fs');
const path = require('path');
const cloud = require('wx-server-sdk');

cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });

const platformCertSerial = '1CAF243926D5F2701381AACD0DF12FF24B653AF8';
const apiV3Key = 'uQ3bTx8Lq9aWv7HZg4PxN1JeKd2Mu5Ft';

exports.main = async (event) => {
  try {
    const { request } = event;
    const { headers, body } = request;

    // Step 1: 校验签名
    const timestamp = headers['wechatpay-timestamp'];
    const nonce = headers['wechatpay-nonce'];
    const signature = headers['wechatpay-signature'];
    const serial = headers['wechatpay-serial'];

    if (serial !== platformCertSerial) {
      return { code: 401, message: '平台证书序列号不匹配' };
    }

    const message = `${timestamp}\n${nonce}\n${body}\n`;
    const cert = fs.readFileSync(path.join(__dirname, 'apiclient_cert.pem'), 'utf8');

    const isValid = crypto.createVerify('RSA-SHA256')
      .update(message)
      .verify(cert, signature, 'base64');

    if (!isValid) {
      return { code: 401, message: '签名校验失败' };
    }

    // Step 2: 解密报文内容
    const resource = JSON.parse(body).resource;
    const { associated_data, nonce: nonceStr, ciphertext, tag } = resource;

    const decipher = crypto.createDecipheriv('aes-256-gcm', apiV3Key, Buffer.from(nonceStr, 'base64'));
    decipher.setAuthTag(Buffer.from(tag, 'base64'));
    if (associated_data) {
      decipher.setAAD(Buffer.from(associated_data));
    }

    let decrypted = decipher.update(ciphertext, 'base64', 'utf8');
    decrypted += decipher.final('utf8');
    const result = JSON.parse(decrypted);

    const openid = result.payer.openid;
    const outTradeNo = result.out_trade_no;

    const db = cloud.database();
    const orderRes = await db.collection('orders').where({ orderId: outTradeNo }).get();
    if (orderRes.data.length === 0) return { code: 404, message: '订单未找到' };

    const order = orderRes.data[0];
    const productId = order.productId;

    let lightPoints = 0;
    let vipData = {};

    if (productId === 'light_12') lightPoints = 12;
    else if (productId === 'light_30') lightPoints = 30;
    else if (productId === 'light_240') lightPoints = 240;
    else if (productId === 'vip_month') {
      vipData = {
        isVIP: true,
        vipExpireTime: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
      };
    } else {
      return { code: 400, message: '未知商品类型' };
    }

    const userRes = await db.collection('users').where({ _openid: openid }).get();
    if (userRes.data.length === 0) {
      // 只在不存在时插入
      await db.collection('users').add({
        data: {
          _openid: openid,
          lightPoints: lightPoints,
          ...vipData
        }
      });
    } else {
      const user = userRes.data[0];
      await db.collection('users').doc(user._id).update({
        data: {
          lightPoints: (user.lightPoints || 0) + lightPoints,
          ...vipData
        }
      });
    }

    await db.collection('orders').doc(order._id).update({
      data: { status: 'paid' }
    });

    return { code: 200, message: 'SUCCESS' };
  } catch (err) {
    console.error('处理支付失败：', err);
    return { code: 500, message: '内部服务器错误' };
  }
};
