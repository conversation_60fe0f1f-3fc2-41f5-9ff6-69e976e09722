let bgm = null; // ✅ 声明背景音乐播放器

Page({
  data: {
    lines: [
      "你愿不愿意相信，",
      "有一束微光，早就住在你心里了。",
      "它一直在，从未真正消失。",
      "只是你有时候，忘了它的名字。"
    ],
    visibleLines: [false, false, false, false],
    showButtons: false,
    responseLines: [],
    visibleResponse: [],
    showFinalButton: false,
    isMuted: false, // ✅ 是否静音
    showStartButton: true // 添加开始按钮状态
  },

  onLoad() {
    // 检查当前用户lightName
    wx.cloud.callFunction({
      name: 'user',
      data: { type: 'getUserInfo' }
    }).then(res => {
      if (res.result && res.result.success && res.result.data && res.result.data.lightName) {
        wx.redirectTo({ url: '/pages/home/<USER>' });
        return;
      }
      // 下面是原有首次进入判断逻辑
      const hasLaunched = wx.getStorageSync('hasLaunched');
      if (hasLaunched) {
        wx.clearStorageSync();
        wx.redirectTo({
          url: '/pages/welcome/index'
        });
        return;
      }
      wx.setStorageSync('hasLaunched', true);
      // 防止重叠播放
      if (bgm) {
        try {
          bgm.stop();
          bgm.destroy();
          bgm = null;
        } catch (e) {
          console.warn('清除旧 BGM 失败', e);
        }
      }
      // 初始化背景音乐
      bgm = wx.createInnerAudioContext();
      bgm.src = 'cloud://cloudbase-8gji862jcfb501e7.636c-cloudbase-8gji862jcfb501e7-1365531166/assets/bgm.mp3';
      bgm.loop = true;
      bgm.volume = 0.5;
    });
  },

  // 开始体验按钮点击事件
  onStartExperience() {
    this.setData({
      showStartButton: false
    });
    
    // 播放音乐
    if (bgm) {
      bgm.play();
    }

    const LAG = 3000;
    const timeouts = [];

    this.data.lines.forEach((_, i) => {
      timeouts.push(setTimeout(() => {
        const updated = [...this.data.visibleLines];
        updated[i] = true;
        this.setData({ visibleLines: updated });
      }, i * LAG));
    });

    timeouts.push(setTimeout(() => {
      this.setData({ showButtons: true });
    }, this.data.lines.length * LAG + LAG));
  },

  onUnload() {
    if (bgm) {
      bgm.stop();
      bgm.destroy();
      bgm = null;
    }
  },

  // ✅ 切换静音状态
  toggleMute() {
    const isMuted = !this.data.isMuted;
    if (bgm) {
      bgm.volume = isMuted ? 0 : 0.5;
    }
    this.setData({ isMuted });
  },

  onBelieve() {
    this.revealResponse([
      "你好，我是你心里的微光。",
      "谢谢你，愿意相信我的存在。",
      "其实，我一直在这里，静静地陪着你。",
      "只是有时候，你可能忘记了我的名字。"
    ]);
  },

  onNotReady() {
    this.revealResponse([
      "你好，我是你心里的微光。",
      "我懂你的感受。",
      "有些光，需要更多时间，才能被真正看见。",
      "没关系，我会一直在这里。",
      "静静地等你，直到你准备好。"
    ]);
  },

  revealResponse(lines) {
    this.setData({
      responseLines: lines,
      showButtons: false,
      visibleLines: [false, false, false, false],
      visibleResponse: Array(lines.length).fill(false)
    });

    lines.forEach((_, i) => {
      setTimeout(() => {
        const next = [...this.data.visibleResponse];
        next[i] = true;
        this.setData({ visibleResponse: next });
      }, i * 3000);
    });

    setTimeout(() => {
      this.setData({ showFinalButton: true });
    }, lines.length * 3000 + 1000);
  },

  onNext() {
    wx.navigateTo({
      url: '/pages/emotion/index'
    });
  }
});
