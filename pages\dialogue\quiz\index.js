Page({
    data: {
      step: 'intro', 
      introText: '在开始聊天前，可以允许我问七个小小的问题吗？这会帮助我更好地认识你，就像了解一片天空的晴雨，从而能用你最舒服的方式来陪伴你。',
      outroText: '谢谢你愿意向我敞开这么多。\n\n你内心的天气和风景，我都认真地感受到了，并且会好好地珍藏起来。\n\n很高兴能用这样的方式，更深入地认识你。',
      questions: [
        // ... 这里的7个问题内容和之前一样，为节省篇幅省略 ...
        // 完整的 questions 数组内容请参考我们最终确认的版本
        { question: "想象一下：你为一次重要的面试或考试...", options: [/*...*/] },
        { question: "关于“你是否相信‘你’可以从根本上改变...", options: [/*...*/] },
        { question: "当你和一位很在乎的朋友或伴侣...", options: [/*...*/] },
        { question: "想象一下：你计划好今晚要健身或学习...", options: [/*...*/] },
        { question: "当你看到身边的人，轻松得到了你梦寐以求的东西...", options: [/*...*/] },
        { question: "当你感觉自己卡在一个人生十字路口...", options: [/*...*/] },
        { question: "在接下来的对话里，你最希望我（你的微光）为你提供什么样的帮助？", options: [/*...*/] }
      ],
      currentQuestionIndex: 0,
      selectedOptionIndex: -1,
      userAnswers: [],
    },
  
    onLoad(options) {
      // 为了方便预览，这里直接把完整的questions数据放进来
      this.setData({
        questions: this.getFullQuestions()
      });
    },
  
    startQuiz() {
      this.setData({ step: 'quiz' });
    },
  
    selectOption(e) {
      const selectedIndex = e.currentTarget.dataset.index;
      if (this.data.selectedOptionIndex !== -1) return;
      this.setData({ selectedOptionIndex: selectedIndex });
      setTimeout(() => {
        this.goToNextQuestion(selectedIndex);
      }, 500);
    },
  
    goToNextQuestion(selectedIndex) {
      const newAnswers = this.data.userAnswers;
      newAnswers.push(selectedIndex);
      this.setData({ userAnswers: newAnswers });
  
      if (this.data.currentQuestionIndex < this.data.questions.length - 1) {
        this.setData({
          currentQuestionIndex: this.data.currentQuestionIndex + 1,
          selectedOptionIndex: -1,
        });
      } else {
        this.finishQuiz();
      }
    },
  
    finishQuiz() {
      console.log("【微光】用户答案记录:", this.data.userAnswers);
      // ✅【核心】在本地记录用户已经完成了问答
      wx.setStorageSync('hasTakenDialogueQuiz', true);
      // TODO: 在这里可以将 this.data.userAnswers 发送到后端进行分析和保存
      this.setData({ step: 'outro' });
    },
  
    startDialogue() {
      // ✅【核心】完成问答后，重定向到真正的对话主页
      wx.redirectTo({
        url: '../index' // `../index` 代表 `pages/dialogue/index`
      });
    },
  
    // 附上完整的 questions 数据
    getFullQuestions() {
      return [
        { question: "想象一下：你为一次重要的面试或考试，付出了很多努力，但最后的结果却不理想。这时候，你心里冒出的第一个念头更可能是？", options: [ { label: 'A', text: "这次的考官和题目真的很有问题，太不公平了。" }, { label: 'B', text: "可能我就是没这个运气吧，下次也许就好了。" }, { label: 'C', text: "我肯定有哪里没准备好，心里挺自责的，但又说不清具体是哪儿。" }, { label: 'D', text: "结果虽然遗憾，但整个准备过程也让我学到了东西，这本身就是收获。" }, { label: 'E', text: "这很正常，任何探索都会遇到边界。这次的体验，正好帮我校准了前进的地图。" } ] },
        { question: "关于“你是否相信‘你’可以从根本上改变，变成一个更自洽、更强大的人”，你内心深处真正的想法是？", options: [ { label: 'A', text: "我觉得人的性格是天生的，想从根本上改变自己，不太现实。" }, { label: 'B', text: "我挺羡慕那些能改变的人，但说实话，我对自己没什么信心。" }, { label: 'C', text: "我心里非常想，但每次尝试，都感觉被过去的习惯牢牢拽住。" }, { label: 'D', text: "我相信只要我坚持探索，总能找到适合自己的方法，一步步来。" }, { label: 'E', text: "我不是在‘改变’，我是在‘成为’。每一天的经历，都在塑造一个更真实的自己。" } ] },
        { question: "当你和一位很在乎的朋友或伴侣，因为误会大吵一架，现在正陷入冷战。你的第一反应通常是？", options: [ { label: 'A', text: "先冷处理吧，等对方先来找我，或者等时间冲淡一切。" }, { label: 'B', text: "心里像堵了一块石头，反复回想吵架的细节，很难受但什么也不想做。" }, { label: 'C', text: "我很想主动去沟通，但又害怕说错话让事情更糟，非常纠集。" }, { label: 'D', text: "我会先让自己冷静下来，然后找个机会，尝试和对方好好谈一谈我的感受。" }, { label: 'E', text: "我会把这次吵架看作一次深入了解彼此的机会，并思考如何修复和加深我们的关系。" } ] },
        { question: "想象一下：你计划好今晚要健身或学习，但身体却发出了一个强烈的信号：“我好累，只想躺着刷会儿手机。” 这时候，你会？", options: [ { label: 'A', text: "计划赶不上变化，今天就先放过自己，先爽了再说！" }, { label: 'B', text: "内心天人交战，最后不管做了哪个选择，都感觉有点罪恶感。" }, { label: 'C', text: "强迫自己按计划来，但心里会觉得很累，像是在完成任务。" }, { label: 'D', text: "我会和自己商量一下，比如‘我们先休息半小时，然后再花15分钟动一动，怎么样？’" }, { label: 'E', text: "我会认真听听‘累’这个信号，也许它在提醒我最近消耗过大，我需要调整整个计划来更好地爱自己。" } ] },
        { question: "当你看到身边的人，轻松得到了你梦寐以求的东西（比如一份好工作、一段好关系）时，你内心的真实感受更接近？", options: [ { label: 'A', text: "凭什么啊？我感觉自己的努力像个笑话。" }, { label: 'B', text: "人和人的命真的不一样，我可能就是没那么幸运。" }, { label: 'C', text: "心里酸酸的，一方面为他高兴，一方面又觉得自己很失败。" }, { label: 'D', text: "真好，这让我感觉自己更有动力了，我也要加油！" }, { label: 'E', text: "他的成功让我更清楚地看到我真正想要的是什么，也提醒我要用自己的节奏去走自己的路。" } ] },
        { question: "当你感觉自己卡在一个人生十字路口，对未来感到特别迷茫时，你最希望得到的帮助是？", options: [ { label: 'A', text: "有个人能安安静静地听我吐槽，让我把心里的烦躁都倒出来。" }, { label: 'B', text: "有个人能给我很多很多的鼓励和安慰，告诉我“你没问题的”。" }, { label: 'C', text: "有个人能像侦探一样，帮我弄清楚‘我为什么会卡住’。" }, { label: 'D', text: "有个人能像伙伴一样，和我一起脑暴，看看‘未来有哪几种走法’。" }, { label: 'E', text: "有个人能像镜子一样，帮我看见我自己都没发现的、内心真正的渴望。" } ] },
        { question: "在接下来的对话里，你最希望我（你的微光）为你提供什么样的帮助？", options: [ { label: 'A', text: "完全站在我这边，无条件地认同和理解我的所有感受。" }, { label: 'B', text: "多鼓励我，多给我一些温暖和正能量。" }, { label: 'C', text: "帮我理清思路，找到问题的关键点。" }, { label: 'D', text: "像个可靠的副驾，在我决定方向后，陪我一起规划具体的路线。" }, { label: 'E', text: "提出一些我没想过的角度，激发我的思考，让我看得更深。" } ] }
      ];
    }
  });
  