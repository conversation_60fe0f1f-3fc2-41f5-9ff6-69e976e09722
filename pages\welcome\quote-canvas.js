// ✅ 文件名：quote-canvas.js
// ✅ 用于保存签文卡片为图片，带背景图、文字排版、毛玻璃卡片感

// 扩展圆角矩形绘制方法
CanvasRenderingContext2D.prototype.fillRoundRect = function(x, y, w, h, r) {
    const ctx = this;
    ctx.beginPath();
    ctx.moveTo(x + r, y);
    ctx.arcTo(x + w, y, x + w, y + h, r);
    ctx.arcTo(x + w, y + h, x, y + h, r);
    ctx.arcTo(x, y + h, x, y, r);
    ctx.arcTo(x, y, x + w, y, r);
    ctx.closePath();
    ctx.fill();
  };
  
  // 多行文字绘制方法
  Page.prototype.drawMultilineText = function(ctx, text, centerX, startY, lineHeight, maxWidth) {
    const words = text.split('');
    let line = '', lines = [], temp;
  
    for (let i = 0; i < words.length; i++) {
      temp = line + words[i];
      if (ctx.measureText(temp).width > maxWidth) {
        lines.push(line);
        line = words[i];
      } else {
        line = temp;
      }
    }
    lines.push(line);
  
    ctx.setFontSize(24);
    ctx.setFillStyle('#333');
    ctx.setTextAlign('center');
  
    lines.forEach((l, i) => {
      ctx.fillText(l, centerX, startY + i * lineHeight);
    });
  };
  
  // 主方法：绑定到页面的 saveQuote() 方法中
  Page.prototype.saveQuote = function() {
    const ctx = wx.createCanvasContext('quoteCanvas', this);
  
    wx.getImageInfo({
      src: '/welcome-background-v3.png',
      success: (res) => {
        ctx.drawImage(res.path, 0, 0, 750, 1334);
  
        // 毛玻璃卡片背景
        ctx.setFillStyle('rgba(255, 255, 255, 0.75)');
        ctx.fillRoundRect(60, 320, 630, 700, 20);
  
        // 标题
        ctx.setFontSize(30);
        ctx.setFillStyle('#555');
        ctx.setTextAlign('center');
        ctx.fillText('✦ 微光签文 ✦', 375, 400);
  
        // 主体签文
        this.drawMultilineText(ctx, `“${this.data.quote}”`, 375, 500, 26, 560);
  
        // 副标题
        ctx.setFontSize(22);
        ctx.setFillStyle('#888');
        ctx.fillText(this.data.footerText, 375, 750);
  
        // 日期签名
        ctx.setFontSize(20);
        ctx.setFillStyle('#aaa');
        ctx.fillText(this.data.signature, 375, 800);
  
        ctx.draw(false, () => {
          wx.canvasToTempFilePath({
            canvasId: 'quoteCanvas',
            success: (res) => {
              wx.saveImageToPhotosAlbum({
                filePath: res.tempFilePath,
                success: () => {
                  wx.showToast({ title: '已保存至相册', icon: 'success' });
                },
                fail: () => {
                  wx.showToast({ title: '保存失败，请检查权限', icon: 'none' });
                }
              });
            },
            fail: () => {
              wx.showToast({ title: '生成图片失败', icon: 'none' });
            }
          });
        });
      },
      fail: () => {
        wx.showToast({ title: '背景图加载失败', icon: 'none' });
      }
    });
  };
  