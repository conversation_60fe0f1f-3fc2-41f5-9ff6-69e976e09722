const cloud = require('wx-server-sdk')
const { GoogleGenerativeAI } = require('@google/generative-ai')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 初始化 Gemini API
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY)

// 生成情感关键词和明日寄语的函数
async function generateEmotionAndMessage(summary, questions, answers) {
  const model = genAI.getGenerativeModel({ model: "gemini-pro" })
  
  const prompt = `根据以下对话总结和三问仪式回答，请提取一个最能代表此次对话情感的关键词，并给出一句温暖、鼓舞人心的明日寄语。

对话主题总结：${summary}

三问仪式问题和回答：
1. ${questions[0]}: ${answers[0]}
2. ${questions[1]}: ${answers[1]}
3. ${questions[2]}: ${answers[2]}

请按照以下格式返回：
情感关键词: [一个词]
明日寄语: [一句话]`

  try {
    const result = await model.generateContent(prompt)
    const response = await result.response
    const text = response.text()
    
    const emotionMatch = text.match(/情感关键词:\s*(\S+)/)
    const messageMatch = text.match(/明日寄语:\s*(.+)/)
    
    return {
      emotionWord: emotionMatch ? emotionMatch[1] : '成长',
      tomorrowMessage: messageMatch ? messageMatch[1] : '相信自己，每一天都是新的开始。'
    }
  } catch (error) {
    console.error('生成情感关键词和寄语失败', error)
    return {
      emotionWord: '成长',
      tomorrowMessage: '相信自己，每一天都是新的开始。'
    }
  }
}

exports.main = async (event, context) => {
  const { 
    theme,      // 对话主题
    summary,    // AI总结
    questions,  // 三个问题
    answers     // 三个问题的答案
  } = event

  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID

  try {
    // 生成情感关键词和明日寄语
    const { emotionWord, tomorrowMessage } = await generateEmotionAndMessage(summary, questions, answers)

    // 保存三问仪式记录到数据库
    const result = await db.collection('three_questions_records').add({
      data: {
        openid,
        theme,
        summary,
        questions,
        answers,
        emotionWord,
        tomorrowMessage,
        createTime: db.serverDate()
      }
    })

    // 更新用户的对话历史
    await db.collection('users').doc(openid).update({
      data: {
        dialogueHistory: _.push({
          theme,
          summary,
          emotionWord,
          tomorrowMessage,
          createTime: db.serverDate()
        })
      }
    })

    return {
      success: true,
      recordId: result._id,
      threeQuestions: questions,
      threeAnswers: answers,
      emotionWord,
      tomorrowMessage
    }
  } catch (error) {
    console.error('处理对话总结失败', error)
    return {
      success: false,
      error: error.message
    }
  }
} 