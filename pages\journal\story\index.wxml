<view class="container">
  <image 
    class="background-image" 
    src="cloud://cloudbase-8gji862jcfb501e7.636c-cloudbase-8gji862jcfb501e7-1365531166/images/homeBG.png" 
    mode="aspectFill"
  ></image>
  <view class="overlay"></view>

  <view class="custom-nav" style="height: {{menuButtonInfo.height}}px; padding-top: {{menuButtonInfo.top}}px;">
    <view class="nav-back" bindtap="navigateBack">
      <!-- 【重要修改】这里不再使用 image 标签 -->
      <view class="back-icon"></view>
    </view>
    <view class="nav-title">月光故事馆</view>
  </view>

  <view class="main-title-container">
    <text class="main-title">让故事陪你慢慢入睡</text>
  </view>

  <scroll-view class="character-scroll" scroll-y>
    <view class="character-list">
      <block wx:for="{{characters}}" wx:key="id">
        <view 
          class="character-card-new" 
          bindtap="navigateToChapter" 
          data-id="{{item.id}}"
          animation="{{animationData[index]}}"
        >
          <view class="card-glow-new"></view>
          
          <view class="text-content">
            <text class="character-name-new">{{item.name}}</text>
            <view class="divider"></view>
            <text class="character-tagline-new">「{{item.tagline}}」</text>
          </view>

          <image class="character-image-new" src="{{item.image}}" mode="aspectFit"></image>
        </view>
      </block>
    </view>
  </scroll-view>
</view>