// 数据迁移管理页面
Page({
    data: {
        migrationStatus: {
            totalRecords: 0,
            migratedRecords: 0,
            unmigratedRecords: 0,
            migrationRate: '0%',
            lastMigrationTime: null
        },
        syncStatus: {
            isSyncing: false,
            queueLength: 0,
            lastSyncTime: 0,
            needFullSync: false
        },
        isLoading: false,
        logs: [],
        showLogs: false
    },

    onLoad() {
        this.loadMigrationStatus()
        this.loadSyncStatus()
        this.loadMigrationLogs()
    },

    // 加载迁移状态
    async loadMigrationStatus() {
        try {
            this.setData({ isLoading: true })

            const result = await wx.cloud.callFunction({
                name: 'migrateConstellationData',
                data: { action: 'validate' }
            })

            if (result.result && result.result.success) {
                this.setData({
                    migrationStatus: result.result.validation
                })
                console.log('迁移状态加载成功:', result.result.validation)
            } else {
                throw new Error(result.result?.error || '获取迁移状态失败')
            }

        } catch (error) {
            console.error('加载迁移状态失败:', error)
            wx.showToast({
                title: '加载状态失败',
                icon: 'error'
            })
        } finally {
            this.setData({ isLoading: false })
        }
    },

    // 加载同步状态
    loadSyncStatus() {
        try {
            // 从本地存储获取同步状态
            const syncStatus = {
                isSyncing: false, // 这里应该从DataSyncManager获取
                queueLength: 0,
                lastSyncTime: wx.getStorageSync('last_sync_time') || 0,
                needFullSync: wx.getStorageSync('need_full_sync') || false
            }

            this.setData({ syncStatus })
            console.log('同步状态:', syncStatus)

        } catch (error) {
            console.error('加载同步状态失败:', error)
        }
    },

    // 加载迁移日志
    async loadMigrationLogs() {
        try {
            const db = wx.cloud.database()
            const result = await db.collection('migration_logs')
                .orderBy('timestamp', 'desc')
                .limit(20)
                .get()

            this.setData({
                logs: result.data || []
            })
            console.log('迁移日志加载成功:', result.data?.length || 0)

        } catch (error) {
            console.error('加载迁移日志失败:', error)
        }
    },

    // 执行测试迁移
    async performTestMigration() {
        try {
            wx.showLoading({ title: '执行测试迁移...' })

            const result = await wx.cloud.callFunction({
                name: 'migrateConstellationData',
                data: {
                    action: 'dry-run',
                    options: {
                        userBatchSize: 2,
                        recordBatchSize: 5,
                        maxUsers: 10
                    }
                }
            })

            wx.hideLoading()

            if (result.result && result.result.success) {
                const summary = result.result.summary
                wx.showModal({
                    title: '测试迁移完成',
                    content: `处理用户: ${summary.processedUsers}\n迁移记录: ${summary.migratedRecords}\n失败记录: ${summary.failedRecords}`,
                    showCancel: false
                })

                // 刷新状态
                this.loadMigrationStatus()
                this.loadMigrationLogs()
            } else {
                throw new Error(result.result?.error || '测试迁移失败')
            }

        } catch (error) {
            wx.hideLoading()
            console.error('测试迁移失败:', error)
            wx.showModal({
                title: '测试迁移失败',
                content: error.message,
                showCancel: false
            })
        }
    },

    // 执行实际迁移
    async performActualMigration() {
        try {
            const confirmResult = await new Promise((resolve) => {
                wx.showModal({
                    title: '确认迁移',
                    content: '这将执行实际的数据迁移，请确保已备份数据。是否继续？',
                    success: resolve
                })
            })

            if (!confirmResult.confirm) {
                return
            }

            wx.showLoading({ title: '执行数据迁移...' })

            const result = await wx.cloud.callFunction({
                name: 'migrateConstellationData',
                data: {
                    action: 'migrate',
                    options: {
                        userBatchSize: 5,
                        recordBatchSize: 20,
                        maxUsers: 1000
                    }
                }
            })

            wx.hideLoading()

            if (result.result && result.result.success) {
                const summary = result.result.summary
                wx.showModal({
                    title: '数据迁移完成',
                    content: `处理用户: ${summary.processedUsers}\n迁移记录: ${summary.migratedRecords}\n失败记录: ${summary.failedRecords}\n耗时: ${Math.round(summary.duration / 1000)}秒`,
                    showCancel: false
                })

                // 刷新状态
                this.loadMigrationStatus()
                this.loadMigrationLogs()
            } else {
                throw new Error(result.result?.error || '数据迁移失败')
            }

        } catch (error) {
            wx.hideLoading()
            console.error('数据迁移失败:', error)
            wx.showModal({
                title: '数据迁移失败',
                content: error.message,
                showCancel: false
            })
        }
    },

    // 强制全量同步
    async forceFullSync() {
        try {
            wx.showLoading({ title: '执行全量同步...' })

            // 这里应该调用DataSyncManager的forceFullSync方法
            // 由于在页面中无法直接导入，我们通过设置标志位来触发
            wx.setStorageSync('need_full_sync', true)
            
            // 模拟同步过程
            setTimeout(() => {
                wx.hideLoading()
                wx.showToast({
                    title: '同步完成',
                    icon: 'success'
                })
                this.loadSyncStatus()
            }, 2000)

        } catch (error) {
            wx.hideLoading()
            console.error('全量同步失败:', error)
            wx.showToast({
                title: '同步失败',
                icon: 'error'
            })
        }
    },

    // 清理本地缓存
    clearLocalCache() {
        try {
            wx.showModal({
                title: '确认清理',
                content: '这将清理所有本地缓存数据，下次访问时会重新从云端加载。是否继续？',
                success: (res) => {
                    if (res.confirm) {
                        // 清理缓存
                        const cacheKeys = [
                            'starRecords',
                            'constellation_data',
                            'last_sync_time',
                            'constellation_cache_version'
                        ]

                        cacheKeys.forEach(key => {
                            try {
                                wx.removeStorageSync(key)
                            } catch (error) {
                                console.warn(`清理缓存键 ${key} 失败:`, error)
                            }
                        })

                        wx.showToast({
                            title: '缓存已清理',
                            icon: 'success'
                        })

                        this.loadSyncStatus()
                    }
                }
            })

        } catch (error) {
            console.error('清理缓存失败:', error)
            wx.showToast({
                title: '清理失败',
                icon: 'error'
            })
        }
    },

    // 刷新所有状态
    refreshAll() {
        this.loadMigrationStatus()
        this.loadSyncStatus()
        this.loadMigrationLogs()
    },

    // 切换日志显示
    toggleLogs() {
        this.setData({
            showLogs: !this.data.showLogs
        })
    },

    // 格式化时间
    formatTime(timestamp) {
        if (!timestamp) return '未知'
        
        const date = new Date(timestamp)
        return date.toLocaleString('zh-CN')
    },

    // 格式化日志内容
    formatLogContent(log) {
        try {
            if (typeof log.details === 'object') {
                return JSON.stringify(log.details, null, 2)
            }
            return log.details || '无详细信息'
        } catch (error) {
            return '格式化失败'
        }
    },

    // 下拉刷新
    onPullDownRefresh() {
        this.refreshAll()
        setTimeout(() => {
            wx.stopPullDownRefresh()
        }, 1000)
    }
})
