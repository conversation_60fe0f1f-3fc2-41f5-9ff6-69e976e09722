const cloud = require('wx-server-sdk');
cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });
const db = cloud.database();

// 商品ID与光点数的映射
const PRODUCT_POINTS = {
  light_12: 12,
  light_30: 30,
  light_240: 240,
  vip_month: 0 // VIP会员不增加光点
};

exports.main = async (event, context) => {
  console.log('Received WeChat Pay Callback Event:', event); // 添加这行来打印原始回调消息
  let decodedData;
  try {
    // 判断是否为微信服务器回调（有 resource 字段）
    if (event.resource) {
      // 微信支付服务器回调，解密
      decodedData = cloud.pay.decodeCallbackData(event);
    } else {
      // 前端主动调用，直接用 event 里的数据
      decodedData = event;
    }
    console.log('Decoded WeChat Pay Callback Data:', decodedData);
  } catch (e) {
    console.error('Failed to decode WeChat Pay callback data:', e);
    return { success: false, message: '回调数据解析失败' };
  }

  // 从解密后的数据中获取关键信息
  const { out_trade_no, payer, amount: payAmount, product_id } = decodedData;
  const openid = payer.openid;
  const productId = product_id; // 使用 product_id 作为商品ID

  // 1. 校验参数
  if (!out_trade_no || !openid || !productId) {
    return { success: false, message: '参数不完整或解密失败' };
  }

  // 2. 查询订单
  const orderRes = await db.collection('orders').where({ orderId: out_trade_no }).get();
  if (orderRes.data.length === 0) {
    console.warn(`Order ${out_trade_no} not found.`);
    return { success: false, message: '订单不存在' };
  }
  const order = orderRes.data[0];

  // 3. 如果订单已支付，直接返回
  if (order.status === 'paid') {
    console.log(`Order ${out_trade_no} already paid.`);
    return { success: true, message: '订单已处理' };
  }

  // 4. 检查支付金额是否一致（可选，但推荐）
  // 微信支付金额单位是分，此处对比的是解密后的金额
  if (order.amount !== payAmount.total) { // 假设 orders.amount 存储的是分
      console.warn(`Order ${out_trade_no} amount mismatch. Expected ${order.amount}, got ${payAmount.total}`);
      // return { success: false, message: '支付金额不匹配' }; // 生产环境可开启严格校验
  }

  // 5. 更新订单状态为“已支付”
  await db.collection('orders').doc(order._id).update({
    data: {
      status: 'paid',
      payTime: db.serverDate(),
      transactionId: decodedData.transaction_id // 记录微信支付流水号
    }
  });
  console.log(`Order ${out_trade_no} status updated to paid.`);

  // 6. 更新用户光点
  const pointsToAdd = PRODUCT_POINTS[productId] || 0;
  if (pointsToAdd > 0) {
    const userRes = await db.collection('users').where({ _openid: openid }).get();
    if (userRes.data.length > 0) {
      const user = userRes.data[0];
      await db.collection('users').doc(user._id).update({
        data: {
          points: (user.points || 0) + pointsToAdd
        }
      });
      console.log(`User ${openid} points updated by ${pointsToAdd}. New points: ${user.points + pointsToAdd}`);
    } else {
        console.warn(`User ${openid} not found for points update.`);
    }
  }

  return { success: true, message: '处理成功' };
}; 