const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 错误类型定义
const ERROR_TYPES = {
  VALIDATION_ERROR: 'validation_error',
  DATABASE_ERROR: 'database_error',
  MIGRATION_ERROR: 'migration_error',
  SYSTEM_ERROR: 'system_error'
}

// 创建错误响应
function createErrorResponse(errorType, message, details = {}) {
  return {
    success: false,
    error: message,
    errorType,
    timestamp: new Date().toISOString(),
    details
  }
}

// 记录迁移日志
async function logMigration(operation, details) {
  try {
    await db.collection('migration_logs').add({
      data: {
        operation,
        details,
        timestamp: db.serverDate(),
        status: details.success ? 'success' : 'failed'
      }
    })
  } catch (error) {
    console.error('记录迁移日志失败:', error)
  }
}

// 计算网格位置
function calculateGridPosition(starIndex, emotionKeyword, createTime) {
  const cols = 3 // 每行3颗星星
  const row = Math.floor(starIndex / cols)
  const col = starIndex % cols
  
  // 基础网格位置
  const baseX = 0.15 + col * 0.3 // 15%, 45%, 75%
  const baseY = 0.2 + row * 0.15 // 从20%开始，每行间隔15%
  
  // 基于情感关键词和时间添加随机偏移
  const seed = hashString((emotionKeyword || '') + (createTime || '')) % 1000
  const randomX = (seed % 100 - 50) / 1000 // ±5%的偏移
  const randomY = ((seed * 7) % 80 - 40) / 1000 // ±4%的偏移
  
  return {
    x: Math.max(0.05, Math.min(0.95, baseX + randomX)),
    y: Math.max(0.15, Math.min(0.85, baseY + randomY))
  }
}

// 简单的字符串哈希函数
function hashString(str) {
  let hash = 0
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // 转换为32位整数
  }
  return Math.abs(hash)
}

// 迁移单个用户的数据
async function migrateUserData(openid, options = {}) {
  const { dryRun = false, batchSize = 50 } = options
  const migrationResult = {
    openid,
    totalRecords: 0,
    migratedRecords: 0,
    skippedRecords: 0,
    failedRecords: 0,
    errors: []
  }

  try {
    // 查询用户所有没有星座位置的记录
    const query = db.collection('three_questions_records')
      .where({
        openid: openid,
        completed: true,
        starPosition: _.exists(false)
      })
      .orderBy('createTime', 'asc')
      .limit(batchSize)

    const result = await query.get()
    migrationResult.totalRecords = result.data.length

    console.log(`用户 ${openid} 需要迁移 ${result.data.length} 条记录`)

    if (result.data.length === 0) {
      return migrationResult
    }

    // 批量处理记录
    for (let i = 0; i < result.data.length; i++) {
      const record = result.data[i]
      
      try {
        // 生成星座位置
        const position = calculateGridPosition(
          i, 
          record.emotionKeyword || record.starKeyword, 
          record.createTime
        )

        const starPosition = {
          x: position.x,
          y: position.y,
          generatedAt: new Date().toISOString(),
          generationType: 'migration_grid_layout',
          starIndex: i,
          migrationVersion: '1.0'
        }

        const migrationInfo = {
          migratedAt: new Date().toISOString(),
          migrationType: 'batch_migration',
          originalFormat: 'legacy',
          migrationVersion: '1.0'
        }

        if (!dryRun) {
          // 更新记录
          await db.collection('three_questions_records')
            .doc(record._id)
            .update({
              data: {
                starPosition,
                migrationInfo,
                lastPositionUpdate: db.serverDate()
              }
            })
        }

        migrationResult.migratedRecords++
        console.log(`记录 ${record._id} 迁移成功`)

      } catch (error) {
        console.error(`记录 ${record._id} 迁移失败:`, error)
        migrationResult.failedRecords++
        migrationResult.errors.push({
          recordId: record._id,
          error: error.message
        })
      }
    }

    return migrationResult

  } catch (error) {
    console.error(`用户 ${openid} 数据迁移失败:`, error)
    migrationResult.errors.push({
      type: 'user_migration_error',
      error: error.message
    })
    return migrationResult
  }
}

// 批量迁移所有用户数据
async function batchMigrateAllUsers(options = {}) {
  const { 
    dryRun = false, 
    userBatchSize = 10, 
    recordBatchSize = 50,
    maxUsers = 100 
  } = options

  const migrationSummary = {
    startTime: new Date().toISOString(),
    totalUsers: 0,
    processedUsers: 0,
    totalRecords: 0,
    migratedRecords: 0,
    failedRecords: 0,
    userResults: [],
    errors: []
  }

  try {
    // 获取需要迁移的用户列表
    const usersQuery = await db.collection('three_questions_records')
      .aggregate()
      .match({
        completed: true,
        starPosition: _.exists(false)
      })
      .group({
        _id: '$openid',
        recordCount: _.sum(1),
        firstRecord: _.min('$createTime')
      })
      .sort({ firstRecord: 1 })
      .limit(maxUsers)
      .end()

    const users = usersQuery.list
    migrationSummary.totalUsers = users.length

    console.log(`找到 ${users.length} 个用户需要迁移数据`)

    // 分批处理用户
    for (let i = 0; i < users.length; i += userBatchSize) {
      const userBatch = users.slice(i, i + userBatchSize)
      
      // 并行处理当前批次的用户
      const batchPromises = userBatch.map(user => 
        migrateUserData(user._id, { dryRun, batchSize: recordBatchSize })
      )

      const batchResults = await Promise.allSettled(batchPromises)
      
      batchResults.forEach((result, index) => {
        migrationSummary.processedUsers++
        
        if (result.status === 'fulfilled') {
          const userResult = result.value
          migrationSummary.totalRecords += userResult.totalRecords
          migrationSummary.migratedRecords += userResult.migratedRecords
          migrationSummary.failedRecords += userResult.failedRecords
          migrationSummary.userResults.push(userResult)
        } else {
          console.error(`用户批次处理失败:`, result.reason)
          migrationSummary.errors.push({
            type: 'batch_processing_error',
            userIndex: i + index,
            error: result.reason.message
          })
        }
      })

      // 批次间暂停，避免过载
      if (i + userBatchSize < users.length) {
        await new Promise(resolve => setTimeout(resolve, 1000))
      }
    }

    migrationSummary.endTime = new Date().toISOString()
    migrationSummary.duration = new Date(migrationSummary.endTime) - new Date(migrationSummary.startTime)

    // 记录迁移日志
    await logMigration('batch_migration', migrationSummary)

    return {
      success: true,
      summary: migrationSummary
    }

  } catch (error) {
    console.error('批量迁移失败:', error)
    migrationSummary.errors.push({
      type: 'batch_migration_error',
      error: error.message
    })

    await logMigration('batch_migration_failed', migrationSummary)

    return createErrorResponse(ERROR_TYPES.MIGRATION_ERROR, '批量迁移失败', {
      summary: migrationSummary,
      error: error.message
    })
  }
}

// 验证迁移结果
async function validateMigration(openid = null) {
  try {
    const whereCondition = openid ? { openid } : {}
    
    // 统计迁移前后的数据
    const totalRecords = await db.collection('three_questions_records')
      .where({ ...whereCondition, completed: true })
      .count()

    const migratedRecords = await db.collection('three_questions_records')
      .where({ 
        ...whereCondition, 
        completed: true,
        starPosition: _.exists(true)
      })
      .count()

    const unmigrated = await db.collection('three_questions_records')
      .where({ 
        ...whereCondition, 
        completed: true,
        starPosition: _.exists(false)
      })
      .count()

    return {
      success: true,
      validation: {
        totalRecords: totalRecords.total,
        migratedRecords: migratedRecords.total,
        unmigratedRecords: unmigrated.total,
        migrationRate: totalRecords.total > 0 ? 
          (migratedRecords.total / totalRecords.total * 100).toFixed(2) + '%' : '0%'
      }
    }

  } catch (error) {
    console.error('验证迁移结果失败:', error)
    return createErrorResponse(ERROR_TYPES.VALIDATION_ERROR, '验证迁移结果失败', {
      error: error.message
    })
  }
}

exports.main = async (event, context) => {
  console.log('数据迁移请求:', JSON.stringify(event))

  const { 
    action = 'migrate', 
    openid = null,
    options = {}
  } = event

  try {
    switch (action) {
      case 'migrate':
        if (openid) {
          // 迁移单个用户
          const result = await migrateUserData(openid, options)
          return { success: true, result }
        } else {
          // 批量迁移所有用户
          return await batchMigrateAllUsers(options)
        }

      case 'validate':
        return await validateMigration(openid)

      case 'dry-run':
        // 干运行模式
        const dryRunOptions = { ...options, dryRun: true }
        if (openid) {
          const result = await migrateUserData(openid, dryRunOptions)
          return { success: true, result, dryRun: true }
        } else {
          return await batchMigrateAllUsers(dryRunOptions)
        }

      default:
        return createErrorResponse(ERROR_TYPES.VALIDATION_ERROR, `不支持的操作: ${action}`)
    }

  } catch (error) {
    console.error('数据迁移云函数执行失败:', error)
    return createErrorResponse(ERROR_TYPES.SYSTEM_ERROR, '数据迁移执行失败', {
      error: error.message
    })
  }
}
