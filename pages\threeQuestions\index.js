// 引入错误处理工具
const errorHandler = require('../../utils/errorHandler.js');

Page({
  data: {
    // 对话相关数据
    dialogueTheme: '',
    dialogueContent: [],
    dialogueSummary: '',
    userOpenid: '',

    // 三问仪式数据结构
    questions: [
      {
        id: 1,
        title: '第一问：记录今日感受',
        text: '你愿意用一个词或一句话，来形容一下今天的感受吗？',
        type: 'feeling',
        placeholder: '请输入今天的感受...',
        maxLength: 50,
        required: false
      },
      {
        id: 2,
        title: '第二问：留言给明天',
        text: '你想给明天的自己，留一句悄悄话吗？（我会在明天这个时候转达给你哦）',
        type: 'message',
        placeholder: '给明天的自己留言...',
        maxLength: 100,
        required: false
      },
      {
        id: 3,
        title: '第三问：生成专属日记',
        text: '你想要把我们今天的对话，生成一篇独属于你的"日记"吗？',
        type: 'diary',
        placeholder: '',
        maxLength: 0,
        required: false
      }
    ],

    // 问题答案和状态
    answers: {
      feeling: '',      // 第一问：今日感受
      message: '',      // 第二问：明天留言
      wantDiary: null   // 第三问：是否生成日记
    },

    // 页面状态管理
    currentQuestionIndex: 0,
    isCompleted: false,
    isLoading: false,

    // 输入状态管理
    inputErrors: {
      feeling: '',
      message: ''
    },

    // VIP 相关
    isVip: false,
    showVipUpgrade: false,

    // 日记生成相关
    isGeneratingDiary: false,
    generatedDiary: '',

    // 进度和导航
    totalQuestions: 3,
    canSkip: true,

    // 从对话页面传递的数据
    fromEnding: false,

    // UX 优化相关状态
    isTransitioning: false,        // 问题切换动画状态
    showFeedback: false,           // 操作反馈显示状态
    feedbackMessage: '',           // 反馈消息内容
    feedbackType: 'success',       // 反馈类型：success, error, info
    autoSaveEnabled: true,         // 自动保存开关
    lastSaveTime: null,            // 最后保存时间
    inputFocused: false,           // 输入框焦点状态
    showProgressDetail: false,     // 详细进度显示
    operationInProgress: false,    // 操作进行中状态
    vibrationEnabled: true,        // 震动反馈开关
    soundEnabled: true,            // 声音反馈开关

    // 错误处理和容错机制相关状态
    networkStatus: {               // 网络状态
      isConnected: true,
      networkType: 'unknown'
    },
    errorRecoveryMode: false,      // 错误恢复模式
    lastError: null,               // 最后一次错误
    retryCount: 0,                 // 重试次数
    maxRetries: 3,                 // 最大重试次数
    fallbackMode: false,           // 备用方案模式
    systemHealthy: true,           // 系统健康状态
    criticalErrorOccurred: false,  // 是否发生严重错误

    // 星星点亮系统相关状态
    showLightUpButton: false,      // 是否显示点亮星星按钮
    isLightingUp: false,           // 是否正在执行点亮动画
    lightingComplete: false        // 点亮动画是否完成
  },

  // 初始化数据恢复管理器
  recoveryManager: null,

  onLoad(options) {
    // 初始化错误处理和容错机制
    this.initErrorHandling();
    console.log('三问页面加载，接收参数:', options)

    try {
      // 解析传递的参数
      const { theme, summary, fromEnding } = options

      // 设置基本数据
      this.setData({
        dialogueTheme: theme ? decodeURIComponent(theme) : '',
        dialogueSummary: summary ? decodeURIComponent(summary) : '',
        fromEnding: fromEnding === 'true'
      })

      // 如果是从结束语流程进入，尝试从localStorage获取更完整的数据
      if (fromEnding === 'true') {
        this.loadDataFromEndingWithErrorHandling()
      }

      // 获取用户openid和VIP状态（带错误处理）
      this.getUserOpenidWithRetry()
      this.checkVipStatusWithRetry()

      // 初始化UX优化功能
      this.initUXOptimizations()

      // 恢复之前保存的输入状态
      this.restoreInputStateWithErrorHandling()

      console.log('三问页面初始化完成:', {
        theme: this.data.dialogueTheme,
        summary: this.data.dialogueSummary,
        fromEnding: fromEnding
      })
    } catch (error) {
      console.error('页面初始化失败:', error)
      this.handleCriticalError(error, '页面初始化')
    }
  },

  // 初始化错误处理和容错机制
  initErrorHandling() {
    console.log('初始化错误处理和容错机制...')

    try {
      // 初始化数据恢复管理器
      this.recoveryManager = errorHandler.createDataRecoveryManager()

      // 检查网络状态
      this.checkNetworkStatusPeriodically()

      // 设置全局错误监听
      this.setupGlobalErrorHandlers()

      // 清理过期的恢复数据
      this.recoveryManager.cleanupExpiredRecoveryData()

      // 检查系统健康状态
      this.checkSystemHealth()

      console.log('错误处理机制初始化完成')
    } catch (error) {
      console.error('错误处理机制初始化失败:', error)
      // 即使错误处理初始化失败，也要确保页面能正常工作
      this.setData({
        systemHealthy: false,
        criticalErrorOccurred: true
      })
    }
  },

  // 定期检查网络状态
  checkNetworkStatusPeriodically() {
    const checkNetwork = async () => {
      try {
        const networkStatus = await errorHandler.checkNetworkStatus()
        this.setData({ networkStatus })

        // 如果网络恢复，重置错误状态
        if (networkStatus.isConnected && this.data.errorRecoveryMode) {
          this.setData({
            errorRecoveryMode: false,
            retryCount: 0
          })
          console.log('网络已恢复，退出错误恢复模式')
        }
      } catch (error) {
        console.error('网络状态检查失败:', error)
      }
    }

    // 立即检查一次
    checkNetwork()

    // 每30秒检查一次网络状态
    this.networkCheckTimer = setInterval(checkNetwork, 30000)
  },

  // 设置全局错误处理器
  setupGlobalErrorHandlers() {
    // 监听小程序错误事件
    wx.onError && wx.onError((error) => {
      console.error('小程序全局错误:', error)
      this.handleSystemError(error, '小程序全局错误')
    })

    // 监听未处理的Promise拒绝
    wx.onUnhandledRejection && wx.onUnhandledRejection((error) => {
      console.error('未处理的Promise拒绝:', error)
      this.handleSystemError(error, 'Promise拒绝')
    })
  },

  // 检查系统健康状态
  async checkSystemHealth() {
    try {
      // 检查本地存储是否正常
      wx.setStorageSync('health_check', Date.now())
      const healthCheck = wx.getStorageSync('health_check')

      if (!healthCheck) {
        throw new Error('本地存储异常')
      }

      // 检查云函数是否可用（简单ping）
      const pingResult = await this.pingCloudFunction()

      this.setData({
        systemHealthy: pingResult.success,
        lastHealthCheck: new Date()
      })

      console.log('系统健康检查完成:', { healthy: pingResult.success })
    } catch (error) {
      console.error('系统健康检查失败:', error)
      this.setData({
        systemHealthy: false,
        lastHealthCheck: new Date()
      })
    }
  },

  // Ping云函数检查可用性
  async pingCloudFunction() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'getOpenid',
        data: { ping: true }
      })
      return { success: true, result }
    } catch (error) {
      console.error('云函数ping失败:', error)
      return { success: false, error: error.message }
    }
  },

  // 带错误处理的数据加载
  async loadDataFromEndingWithErrorHandling() {
    try {
      const result = this.loadDataFromEnding()

      if (!result.success) {
        // 保存恢复数据
        this.recoveryManager.saveRecoveryData('loadDataFromEnding', {
          error: result.error,
          timestamp: Date.now(),
          fallbackUsed: true
        })

        // 使用备用数据
        this.useFallbackData()
      }
    } catch (error) {
      console.error('数据加载失败，使用错误处理:', error)
      await this.handleDataLoadError(error, 'loadDataFromEnding')
    }
  },

  // 使用备用数据
  useFallbackData() {
    console.log('使用备用数据...')

    const fallbackData = {
      dialogueTheme: '内心对话',
      dialogueSummary: '今天的对话很有意义',
      dialogueContent: [
        { role: 'ai', content: '我是你心中的微光，今天想和我聊聊什么？' },
        { role: 'user', content: '今天的对话很有意义' }
      ],
      fallbackMode: true
    }

    this.setData(fallbackData)

    wx.showToast({
      title: '已使用备用数据',
      icon: 'none',
      duration: 1500
    })
  },

  // 带重试的获取用户openid
  async getUserOpenidWithRetry() {
    const operation = () => {
      return new Promise((resolve, reject) => {
        wx.cloud.callFunction({
          name: 'getOpenid',
          success: (res) => {
            if (res.result && res.result.openid) {
              this.setData({ userOpenid: res.result.openid })
              resolve(res.result.openid)
            } else {
              reject(new Error('获取openid失败'))
            }
          },
          fail: reject
        })
      })
    }

    try {
      await errorHandler.retryWithBackoff(operation, {
        maxRetries: 3,
        baseDelay: 1000,
        retryCondition: (error) => {
          // 网络错误才重试
          return error.errCode === -1 || error.message.includes('network')
        }
      })
    } catch (error) {
      console.error('获取用户openid最终失败:', error)
      await this.handleNonCriticalError(error, '获取用户openid')
    }
  },

  // 带重试的VIP状态检查
  async checkVipStatusWithRetry() {
    if (!this.data.userOpenid) {
      // 延迟检查
      setTimeout(() => this.checkVipStatusWithRetry(), 1000)
      return
    }

    const operation = () => {
      return new Promise((resolve, reject) => {
        wx.cloud.callFunction({
          name: 'vipManager',
          data: {
            type: 'checkVipStatus',
            openid: this.data.userOpenid
          },
          success: (res) => {
            if (res.result && res.result.success) {
              const isVip = res.result.isVip || false
              const vipExpireTime = res.result.vipExpireTime

              let actualVipStatus = isVip
              if (isVip && vipExpireTime) {
                const now = new Date()
                const expireDate = new Date(vipExpireTime)
                actualVipStatus = now < expireDate
              }

              this.setData({
                isVip: actualVipStatus,
                vipExpireTime: vipExpireTime
              })
              resolve(actualVipStatus)
            } else {
              reject(new Error('VIP状态检查失败'))
            }
          },
          fail: reject
        })
      })
    }

    try {
      await errorHandler.retryWithBackoff(operation, {
        maxRetries: 2,
        baseDelay: 1000
      })
    } catch (error) {
      console.error('VIP状态检查最终失败:', error)
      // VIP状态检查失败不是关键错误，设置默认值
      this.setData({ isVip: false })
      await this.handleNonCriticalError(error, 'VIP状态检查')
    }
  },

  // 带错误处理的输入状态恢复
  restoreInputStateWithErrorHandling() {
    try {
      this.restoreInputState()
    } catch (error) {
      console.error('输入状态恢复失败:', error)
      // 输入状态恢复失败不影响核心功能
      this.handleNonCriticalError(error, '输入状态恢复')
    }
  },

  // 初始化UX优化功能
  initUXOptimizations() {
    // 设置自动保存定时器
    if (this.data.autoSaveEnabled) {
      this.setupAutoSave()
    }

    // 检查设备能力
    this.checkDeviceCapabilities()

    // 预加载资源
    this.preloadResources()

    console.log('UX优化功能初始化完成')
  },

  // 检查设备能力
  checkDeviceCapabilities() {
    try {
      // 检查震动支持
      wx.getSystemInfo({
        success: (res) => {
          const isIOS = res.platform === 'ios'
          const isAndroid = res.platform === 'android'

          this.setData({
            vibrationEnabled: isIOS || isAndroid,
            soundEnabled: true // 小程序都支持音频
          })
        }
      })
    } catch (error) {
      console.warn('设备能力检查失败:', error)
    }
  },

  // 预加载资源
  preloadResources() {
    // 预加载下一个问题可能用到的图片
    const imagesToPreload = [
      'cloud://cloudbase-8gji862jcfb501e7.636c-cloudbase-8gji862jcfb501e7-1365531166/images/diary-icon.png'
    ]

    imagesToPreload.forEach(src => {
      if (wx.createImage) {
        const img = wx.createImage()
        img.src = src
      }
    })
  },

  // 设置自动保存
  setupAutoSave() {
    // 每30秒自动保存一次输入状态
    this.autoSaveTimer = setInterval(() => {
      this.autoSaveInputState()
    }, 30000)
  },

  // 自动保存输入状态
  autoSaveInputState() {
    if (!this.data.autoSaveEnabled) return

    try {
      const saveData = {
        answers: this.data.answers,
        currentQuestionIndex: this.data.currentQuestionIndex,
        timestamp: Date.now()
      }

      wx.setStorageSync('threeQuestions_autoSave', saveData)

      this.setData({
        lastSaveTime: new Date().toLocaleTimeString()
      })

      console.log('输入状态已自动保存')
    } catch (error) {
      console.error('自动保存失败:', error)
    }
  },

  // 恢复输入状态
  restoreInputState() {
    try {
      const savedData = wx.getStorageSync('threeQuestions_autoSave')

      if (savedData && savedData.timestamp) {
        const timeDiff = Date.now() - savedData.timestamp
        // 如果保存时间在1小时内，恢复数据
        if (timeDiff < 3600000) {
          this.setData({
            answers: savedData.answers || this.data.answers,
            currentQuestionIndex: savedData.currentQuestionIndex || 0
          })

          this.showFeedbackMessage('已恢复之前的输入内容', 'info')
          console.log('输入状态已恢复')
        }
      }
    } catch (error) {
      console.error('恢复输入状态失败:', error)
    }
  },

  // 显示反馈消息
  showFeedbackMessage(message, type = 'success', duration = 2000) {
    this.setData({
      showFeedback: true,
      feedbackMessage: message,
      feedbackType: type
    })

    // 触发震动反馈
    this.triggerHapticFeedback(type)

    // 自动隐藏反馈
    setTimeout(() => {
      this.setData({
        showFeedback: false
      })
    }, duration)
  },

  // 触发震动反馈
  triggerHapticFeedback(type = 'success') {
    if (!this.data.vibrationEnabled) return

    try {
      if (type === 'success') {
        wx.vibrateShort({ type: 'light' })
      } else if (type === 'error') {
        wx.vibrateShort({ type: 'heavy' })
      } else {
        wx.vibrateShort({ type: 'medium' })
      }
    } catch (error) {
      console.warn('震动反馈失败:', error)
    }
  },

  // 问题切换动画
  transitionToNextQuestion(targetIndex) {
    if (this.data.isTransitioning) return

    this.setData({ isTransitioning: true })

    // 触发切换动画
    setTimeout(() => {
      this.setData({
        currentQuestionIndex: targetIndex,
        isTransitioning: false
      })

      // 触发成功反馈
      this.triggerHapticFeedback('success')

      // 自动保存当前状态
      this.autoSaveInputState()
    }, 300)
  },

  // 输入框焦点事件
  onInputFocus() {
    this.setData({ inputFocused: true })
    this.triggerHapticFeedback('light')
  },

  // 输入框失焦事件
  onInputBlur() {
    this.setData({ inputFocused: false })
    // 失焦时自动保存
    this.autoSaveInputState()
  },

  // 显示详细进度
  toggleProgressDetail() {
    this.setData({
      showProgressDetail: !this.data.showProgressDetail
    })
    this.triggerHapticFeedback('light')
  },

  // 操作确认
  confirmOperation(operation, callback) {
    this.setData({ operationInProgress: true })

    // 显示操作反馈
    this.showFeedbackMessage('操作处理中...', 'info', 1000)

    // 执行操作
    setTimeout(() => {
      if (callback && typeof callback === 'function') {
        callback()
      }

      this.setData({ operationInProgress: false })
    }, 500)
  },

  // 从结束语流程加载数据 - 增强版数据验证和传递
  loadDataFromEnding() {
    console.log('\n=== 从结束语流程加载数据 ===')

    try {
      // 从localStorage获取完整的对话数据
      const dialogueContent = wx.getStorageSync('dialogueContent')
      const dialogueTheme = wx.getStorageSync('dialogueTheme')
      const endingMessage = wx.getStorageSync('endingMessage')
      const dialogueSummary = wx.getStorageSync('dialogueSummary')
      const dialogueMetadata = wx.getStorageSync('dialogueMetadata')

      console.log('从localStorage加载的数据:', {
        dialogueContentLength: dialogueContent ? dialogueContent.length : 0,
        dialogueTheme: dialogueTheme,
        endingMessage: endingMessage ? endingMessage.substring(0, 50) + '...' : null,
        dialogueSummary: dialogueSummary ? dialogueSummary.substring(0, 50) + '...' : null,
        hasMetadata: !!dialogueMetadata,
        metadataKeys: dialogueMetadata ? Object.keys(dialogueMetadata) : []
      })

      // 数据完整性验证和恢复
      const dataToUpdate = {}

      // 验证并设置对话主题
      const validatedTheme = this.validateAndSetTheme(dialogueTheme)
      if (validatedTheme !== this.data.dialogueTheme) {
        dataToUpdate.dialogueTheme = validatedTheme
      }

      // 验证并设置对话总结（优先使用结束语）
      const validatedSummary = this.validateAndSetSummary(endingMessage, dialogueSummary)
      if (validatedSummary !== this.data.dialogueSummary) {
        dataToUpdate.dialogueSummary = validatedSummary
      }

      // 验证并设置元数据
      if (dialogueMetadata) {
        const validatedMetadata = this.validateAndSetMetadata(dialogueMetadata)
        Object.assign(dataToUpdate, validatedMetadata)
      }

      // 验证对话内容的完整性并保存
      const dialogueValidationResult = this.validateAndSetDialogueContent(dialogueContent)
      if (dialogueValidationResult.success) {
        dataToUpdate.dialogueContent = dialogueValidationResult.content
        console.log('✓ 对话内容验证成功，包含', dialogueValidationResult.content.length, '条消息')
      } else {
        console.warn('⚠ 对话内容验证失败:', dialogueValidationResult.error)
        this.handleMissingDialogueContent()
      }

      // 批量更新数据
      if (Object.keys(dataToUpdate).length > 0) {
        this.setData(dataToUpdate)
        console.log('✓ 数据更新完成:', Object.keys(dataToUpdate))
      }

      // 验证最终数据完整性
      const finalValidation = this.validateFinalDataIntegrity()
      if (!finalValidation.isValid) {
        console.warn('⚠ 最终数据验证失败:', finalValidation.issues)
        this.handleDataIntegrityIssues(finalValidation.issues)
      }

      return {
        success: true,
        loadedData: Object.keys(dataToUpdate),
        contentLength: dataToUpdate.dialogueContent ? dataToUpdate.dialogueContent.length : 0,
        finalValidation: finalValidation
      }

    } catch (error) {
      console.error('❌ 从localStorage加载数据失败:', error)
      this.handleDataLoadingError(error)
      return {
        success: false,
        error: error.message
      }
    }
  },

  // 验证并设置对话主题
  validateAndSetTheme(dialogueTheme) {
    if (dialogueTheme && typeof dialogueTheme === 'string' && dialogueTheme.trim().length > 0) {
      const cleanTheme = dialogueTheme.trim()
      console.log('✓ 对话主题验证通过:', cleanTheme)
      return cleanTheme
    }

    // 如果当前页面已有主题，保持不变
    if (this.data.dialogueTheme && this.data.dialogueTheme.trim().length > 0) {
      console.log('✓ 使用页面现有主题:', this.data.dialogueTheme)
      return this.data.dialogueTheme
    }

    // 使用默认主题
    const defaultTheme = '内心对话'
    console.log('⚠ 使用默认主题:', defaultTheme)
    return defaultTheme
  },

  // 验证并设置对话总结
  validateAndSetSummary(endingMessage, dialogueSummary) {
    // 优先使用结束语
    if (endingMessage && typeof endingMessage === 'string' && endingMessage.trim().length > 0) {
      const cleanEnding = endingMessage.trim()
      console.log('✓ 使用结束语作为总结:', cleanEnding.substring(0, 50) + '...')
      return cleanEnding
    }

    // 其次使用对话总结
    if (dialogueSummary && typeof dialogueSummary === 'string' && dialogueSummary.trim().length > 0) {
      const cleanSummary = dialogueSummary.trim()
      console.log('✓ 使用对话总结:', cleanSummary.substring(0, 50) + '...')
      return cleanSummary
    }

    // 如果当前页面已有总结，保持不变
    if (this.data.dialogueSummary && this.data.dialogueSummary.trim().length > 0) {
      console.log('✓ 使用页面现有总结:', this.data.dialogueSummary.substring(0, 50) + '...')
      return this.data.dialogueSummary
    }

    // 使用默认总结
    const defaultSummary = '今天的对话很有意义'
    console.log('⚠ 使用默认总结:', defaultSummary)
    return defaultSummary
  },

  // 验证并设置元数据
  validateAndSetMetadata(dialogueMetadata) {
    const validatedData = {}

    if (dialogueMetadata && typeof dialogueMetadata === 'object') {
      // 验证用户层级
      if (dialogueMetadata.userLevel && typeof dialogueMetadata.userLevel === 'number') {
        if (dialogueMetadata.userLevel >= 2 && dialogueMetadata.userLevel <= 4) {
          validatedData.userLevel = dialogueMetadata.userLevel
          console.log('✓ 用户层级验证通过:', dialogueMetadata.userLevel)
        }
      }

      // 验证用户层级名称
      if (dialogueMetadata.userLevelName && typeof dialogueMetadata.userLevelName === 'string') {
        validatedData.userLevelName = dialogueMetadata.userLevelName.trim()
        console.log('✓ 用户层级名称验证通过:', validatedData.userLevelName)
      }

      // 验证用户openid
      if (dialogueMetadata.userOpenid && typeof dialogueMetadata.userOpenid === 'string') {
        validatedData.userOpenid = dialogueMetadata.userOpenid.trim()
        console.log('✓ 用户openid验证通过')
      }

      // 验证用户名称
      if (dialogueMetadata.userName && typeof dialogueMetadata.userName === 'string') {
        validatedData.userName = dialogueMetadata.userName.trim()
        console.log('✓ 用户名称验证通过:', validatedData.userName)
      }

      // 验证对话轮数
      if (dialogueMetadata.dialogueRound && typeof dialogueMetadata.dialogueRound === 'number') {
        validatedData.dialogueRound = dialogueMetadata.dialogueRound
        console.log('✓ 对话轮数验证通过:', dialogueMetadata.dialogueRound)
      }
    }

    return validatedData
  },

  // 验证并设置对话内容
  validateAndSetDialogueContent(dialogueContent) {
    if (!dialogueContent || !Array.isArray(dialogueContent)) {
      return {
        success: false,
        error: '对话内容不是有效数组'
      }
    }

    if (dialogueContent.length === 0) {
      return {
        success: false,
        error: '对话内容为空'
      }
    }

    // 验证对话内容结构
    const validationResult = this.validateDialogueContent(dialogueContent)
    if (!validationResult.isValid) {
      console.warn('对话内容结构验证失败:', validationResult.issues)

      // 尝试修复对话内容
      const repairedContent = this.repairDialogueContent(dialogueContent)
      if (repairedContent.length > 0) {
        console.log('✓ 对话内容修复成功，包含', repairedContent.length, '条消息')
        return {
          success: true,
          content: repairedContent,
          repaired: true
        }
      } else {
        return {
          success: false,
          error: '对话内容修复失败'
        }
      }
    }

    return {
      success: true,
      content: dialogueContent
    }
  },

  // 验证最终数据完整性
  validateFinalDataIntegrity() {
    const issues = []
    let isValid = true

    // 检查必要数据
    if (!this.data.dialogueTheme || this.data.dialogueTheme.trim().length === 0) {
      issues.push('对话主题缺失')
      isValid = false
    }

    if (!this.data.dialogueSummary || this.data.dialogueSummary.trim().length === 0) {
      issues.push('对话总结缺失')
      isValid = false
    }

    if (!this.data.dialogueContent || this.data.dialogueContent.length === 0) {
      issues.push('对话内容缺失')
      isValid = false
    }

    if (!this.data.userOpenid || this.data.userOpenid.trim().length === 0) {
      issues.push('用户openid缺失')
      isValid = false
    }

    // 检查数据质量
    if (this.data.dialogueContent && this.data.dialogueContent.length < 2) {
      issues.push('对话内容过少，可能影响三问体验')
    }

    if (this.data.userLevel && (this.data.userLevel < 2 || this.data.userLevel > 4)) {
      issues.push('用户层级异常')
    }

    return { isValid, issues }
  },

  // 处理数据完整性问题
  handleDataIntegrityIssues(issues) {
    console.warn('处理数据完整性问题:', issues)

    // 为缺失的关键数据设置默认值
    const fixes = {}

    if (issues.includes('对话主题缺失')) {
      fixes.dialogueTheme = '内心对话'
    }

    if (issues.includes('对话总结缺失')) {
      fixes.dialogueSummary = '今天的对话很有意义'
    }

    if (issues.includes('对话内容缺失')) {
      fixes.dialogueContent = [
        { role: 'ai', content: '我是你心中的微光，今天想和我聊聊什么？' },
        { role: 'user', content: '今天的对话很有意义' }
      ]
    }

    if (issues.includes('用户层级异常')) {
      fixes.userLevel = 3
      fixes.userLevelName = '挣扎层'
    }

    if (Object.keys(fixes).length > 0) {
      this.setData(fixes)
      console.log('✓ 数据完整性问题已修复:', Object.keys(fixes))
    }

    // 显示用户友好的提示
    if (issues.length > 0) {
      wx.showToast({
        title: '数据加载完成，已自动优化',
        icon: 'success',
        duration: 1500
      })
    }
  },

  // 验证对话内容结构
  validateDialogueContent(dialogueContent) {
    const issues = []
    let isValid = true

    if (!Array.isArray(dialogueContent)) {
      issues.push('对话内容不是数组')
      isValid = false
      return { isValid, issues }
    }

    // 检查每条消息的结构
    dialogueContent.forEach((message, index) => {
      if (!message || typeof message !== 'object') {
        issues.push(`消息${index}不是对象`)
        isValid = false
      } else {
        if (!message.role || !['user', 'ai', 'system'].includes(message.role)) {
          issues.push(`消息${index}角色无效: ${message.role}`)
          isValid = false
        }
        if (typeof message.content !== 'string') {
          issues.push(`消息${index}内容不是字符串`)
          isValid = false
        }
      }
    })

    // 检查对话的基本逻辑
    if (dialogueContent.length > 0) {
      const firstMessage = dialogueContent[0]
      if (firstMessage.role !== 'ai') {
        issues.push('首条消息应该是AI消息')
      }
    }

    return { isValid, issues }
  },

  // 修复对话内容
  repairDialogueContent(dialogueContent) {
    if (!Array.isArray(dialogueContent)) {
      return []
    }

    const repairedContent = []

    dialogueContent.forEach((message, index) => {
      if (message && typeof message === 'object') {
        const repairedMessage = {
          role: message.role || 'ai',
          content: typeof message.content === 'string' ? message.content : '消息内容异常'
        }

        // 确保角色有效
        if (!['user', 'ai', 'system'].includes(repairedMessage.role)) {
          repairedMessage.role = index % 2 === 0 ? 'ai' : 'user'
        }

        repairedContent.push(repairedMessage)
      }
    })

    return repairedContent
  },

  // 处理对话内容缺失
  handleMissingDialogueContent() {
    console.warn('处理对话内容缺失情况')

    // 如果有主题，创建一个最小的对话内容
    if (this.data.dialogueTheme) {
      const fallbackContent = [
        {
          role: 'ai',
          content: `我是你心中的微光，关于${this.data.dialogueTheme}，想和我聊聊什么？`
        },
        {
          role: 'user',
          content: '今天的对话很有意义'
        }
      ]

      this.setData({
        dialogueContent: fallbackContent
      })

      console.log('✓ 已创建备用对话内容')
    }
  },

  // 处理数据加载错误
  handleDataLoadingError(error) {
    console.error('数据加载错误处理:', error)

    // 显示用户友好的错误提示
    wx.showToast({
      title: '数据加载异常，使用默认设置',
      icon: 'none',
      duration: 2000
    })

    // 设置默认数据确保页面能正常工作
    const defaultData = {
      dialogueTheme: this.data.dialogueTheme || '内心对话',
      dialogueSummary: this.data.dialogueSummary || '今天的对话很有意义',
      dialogueContent: this.data.dialogueContent.length > 0 ? this.data.dialogueContent : [
        {
          role: 'ai',
          content: '我是你心中的微光，今天想和我聊聊什么？'
        }
      ]
    }

    this.setData(defaultData)
    console.log('✓ 已设置默认数据')
  },

  // 检查VIP状态
  checkVipStatus() {
    if (!this.data.userOpenid) {
      // 如果还没有获取到openid，延迟检查
      setTimeout(() => {
        this.checkVipStatus()
      }, 500)
      return
    }

    wx.cloud.callFunction({
      name: 'vipManager',
      data: {
        type: 'checkVipStatus',
        openid: this.data.userOpenid
      }
    }).then(res => {
      console.log('VIP状态检查结果:', res)
      if (res.result && res.result.success) {
        const isVip = res.result.isVip || false
        const vipExpireTime = res.result.vipExpireTime

        // 如果有过期时间，检查是否已过期
        let actualVipStatus = isVip
        if (isVip && vipExpireTime) {
          const now = new Date()
          const expireDate = new Date(vipExpireTime)
          actualVipStatus = now < expireDate
        }

        this.setData({
          isVip: actualVipStatus,
          vipExpireTime: vipExpireTime
        })

        console.log('VIP状态更新:', { isVip: actualVipStatus, vipExpireTime })
      } else {
        console.error('VIP状态检查失败:', res.result)
        // 默认设置为非VIP
        this.setData({
          isVip: false
        })
      }
    }).catch(err => {
      console.error('VIP状态检查异常:', err)
      // 默认设置为非VIP
      this.setData({
        isVip: false
      })
    })
  },

  // 获取用户openid
  getUserOpenid() {
    wx.cloud.callFunction({
      name: 'getOpenid'
    }).then(res => {
      if (res.result && res.result.openid) {
        this.setData({
          userOpenid: res.result.openid
        })
      }
    }).catch(err => {
      console.error('获取用户openid失败', err)
    })
  },

  // 处理输入变化
  onInputChange(e) {
    const { value } = e.detail
    const { currentQuestionIndex, questions } = this.data
    const currentQuestion = questions[currentQuestionIndex]

    // 根据问题类型更新对应的答案
    const answers = { ...this.data.answers }
    const inputErrors = { ...this.data.inputErrors }

    if (currentQuestion.type === 'feeling') {
      answers.feeling = value
      // 清除之前的错误信息
      inputErrors.feeling = ''

      // 实时验证长度
      if (value.length > currentQuestion.maxLength) {
        inputErrors.feeling = `感受描述不超过${currentQuestion.maxLength}字`
        this.triggerHapticFeedback('error')
      }
    } else if (currentQuestion.type === 'message') {
      answers.message = value
      // 清除之前的错误信息
      inputErrors.message = ''

      // 实时验证长度
      if (value.length > currentQuestion.maxLength) {
        inputErrors.message = `留言不超过${currentQuestion.maxLength}字`
        this.triggerHapticFeedback('error')
      }
    }

    this.setData({ answers, inputErrors })

    // 实时自动保存（防抖）
    this.debouncedAutoSave()
  },

  // 防抖自动保存
  debouncedAutoSave() {
    if (this.autoSaveDebounceTimer) {
      clearTimeout(this.autoSaveDebounceTimer)
    }

    this.autoSaveDebounceTimer = setTimeout(() => {
      this.autoSaveInputState()
    }, 1000) // 1秒后保存
  },

  // 输入验证
  validateCurrentInput() {
    const { currentQuestionIndex, questions, answers } = this.data
    const currentQuestion = questions[currentQuestionIndex]

    if (currentQuestion.type === 'feeling') {
      const feeling = answers.feeling.trim()

      // 检查长度限制
      if (feeling.length > currentQuestion.maxLength) {
        wx.showToast({
          title: `感受描述不超过${currentQuestion.maxLength}字`,
          icon: 'none'
        })
        return false
      }

      // 如果用户输入了内容，验证不为空
      if (feeling.length > 0 && feeling.length < 1) {
        wx.showToast({
          title: '请输入有效的感受描述',
          icon: 'none'
        })
        return false
      }
    } else if (currentQuestion.type === 'message') {
      const message = answers.message.trim()
      if (message.length > currentQuestion.maxLength) {
        wx.showToast({
          title: `留言不超过${currentQuestion.maxLength}字`,
          icon: 'none'
        })
        return false
      }
    }

    return true
  },

  // 提取感受关键词
  extractFeelingKeyword(feeling) {
    if (!feeling || feeling.trim().length === 0) {
      return '今日感受'
    }

    const trimmedFeeling = feeling.trim()

    // 如果输入很短（1-3个字），直接作为关键词
    if (trimmedFeeling.length <= 3) {
      return trimmedFeeling
    }

    // 如果是短句，提取核心词汇
    if (trimmedFeeling.length <= 10) {
      // 简单的关键词提取：去除常见的修饰词，但保持顺序
      const commonWords = ['很', '非常', '特别', '比较', '有点', '感到', '觉得', '今天', '我']
      let keyword = trimmedFeeling

      // 逐个移除修饰词
      commonWords.forEach(word => {
        keyword = keyword.replace(new RegExp(word, 'g'), '')
      })

      // 清理多余的空格
      keyword = keyword.replace(/\s+/g, '').trim()

      // 如果提取后还有内容且长度合理，返回提取结果
      if (keyword.length > 0 && keyword.length <= 6) {
        return keyword
      }

      // 如果提取后为空或太长，返回原文的前几个字
      return trimmedFeeling.substring(0, Math.min(4, trimmedFeeling.length))
    }

    // 如果是长句，智能提取关键词
    const commonWords = ['很', '非常', '特别', '比较', '有点', '感到', '觉得', '今天', '我', '的', '了', '是', '在']
    let keyword = trimmedFeeling

    // 移除修饰词
    commonWords.forEach(word => {
      keyword = keyword.replace(new RegExp(word, 'g'), '')
    })

    // 清理空格并截取
    keyword = keyword.replace(/\s+/g, '').trim()

    if (keyword.length > 0) {
      return keyword.substring(0, Math.min(6, keyword.length))
    }

    // 如果提取失败，返回原文的前几个字
    return trimmedFeeling.substring(0, 6)
  },

  // 保存第一问答案
  saveDailyFeeling() {
    const { answers } = this.data
    const feeling = answers.feeling.trim()

    if (feeling.length > 0) {
      // 提取关键词
      const keyword = this.extractFeelingKeyword(feeling)

      // 保存到本地存储，供后续使用
      try {
        wx.setStorageSync('dailyFeelingKeyword', keyword)
        wx.setStorageSync('dailyFeelingText', feeling)
        console.log('第一问答案已保存:', { feeling, keyword })
      } catch (error) {
        console.error('保存第一问答案失败:', error)
      }
    }
  },

  // 保存第二问答案（明天留言）
  saveTomorrowMessage() {
    const { answers } = this.data
    const message = answers.message.trim()

    if (message.length > 0) {
      try {
        wx.setStorageSync('tomorrowMessage', message)
        console.log('第二问答案已保存:', message)

        // 显示确认提示
        wx.showToast({
          title: '留言已记录，明天同一时间为你送达',
          icon: 'success',
          duration: 2000
        })
      } catch (error) {
        console.error('保存第二问答案失败:', error)
      }
    }
  },

  // 下一个问题
  nextQuestion() {
    if (!this.validateCurrentInput()) {
      this.showFeedbackMessage('请检查输入内容', 'error')
      return
    }

    // 使用操作确认来提供反馈
    this.confirmOperation('nextQuestion', () => {
      const { currentQuestionIndex, totalQuestions, questions } = this.data
      const currentQuestion = questions[currentQuestionIndex]

      // 根据问题类型保存对应数据
      if (currentQuestion.type === 'feeling') {
        this.saveDailyFeeling()
        this.showFeedbackMessage('今日感受已记录', 'success')
      } else if (currentQuestion.type === 'message') {
        this.saveTomorrowMessage()
        this.showFeedbackMessage('明日留言已安排', 'success')
      }

      if (currentQuestionIndex < totalQuestions - 1) {
        // 使用动画切换到下一个问题
        this.transitionToNextQuestion(currentQuestionIndex + 1)
      } else {
        this.completeThreeQuestions()
      }
    })
  },

  // 跳过当前问题
  skipQuestion() {
    this.confirmOperation('skipQuestion', () => {
      const { currentQuestionIndex, totalQuestions, questions } = this.data
      const currentQuestion = questions[currentQuestionIndex]

      // 如果用户有输入内容，仍然保存
      if (currentQuestion.type === 'feeling') {
        this.saveDailyFeeling()
      } else if (currentQuestion.type === 'message') {
        this.saveTomorrowMessage()
      }

      this.showFeedbackMessage('已跳过当前问题', 'info')

      if (currentQuestionIndex < totalQuestions - 1) {
        // 使用动画切换到下一个问题
        this.transitionToNextQuestion(currentQuestionIndex + 1)
      } else {
        this.completeThreeQuestions()
      }
    })
  },

  // 处理第三问的选择
  handleDiaryChoice(e) {
    console.log('handleDiaryChoice 被触发', e)
    const { choice } = e.currentTarget.dataset
    console.log('选择的操作:', choice)
    console.log('当前VIP状态:', this.data.isVip)

    const answers = { ...this.data.answers }

    if (choice === 'generate') {
      // 临时允许非VIP用户也能生成日记（用于调试）
      console.log('开始生成日记，VIP状态:', this.data.isVip)
      answers.wantDiary = true
      this.setData({ answers })
      this.generateDiary()

      // 原来的VIP检查逻辑（已注释）
      /*
      if (this.data.isVip) {
        console.log('VIP用户，开始生成日记')
        answers.wantDiary = true
        this.setData({ answers })
        this.generateDiary()
      } else {
        console.log('非VIP用户，显示升级弹窗')
        this.showVipUpgradeModal()
      }
      */
    } else if (choice === 'skip') {
      console.log('用户选择跳过日记生成')
      answers.wantDiary = false
      this.setData({ answers })
      this.completeThreeQuestions()
    }
  },

  // 显示VIP升级弹窗
  showVipUpgradeModal() {
    console.log('显示VIP升级弹窗')
    wx.showModal({
      title: 'VIP专属功能',
      content: '日记生成是VIP专属功能，升级VIP即可享受个性化AI日记服务，还可获得每日免费光点和专属游戏内容',
      confirmText: '立即升级',
      cancelText: '直接点亮星星',
      success: (res) => {
        if (res.confirm) {
          // 跳转到VIP页面
          wx.navigateTo({
            url: '/pages/vip/index',
            success: () => {
              console.log('成功跳转到VIP页面')
            },
            fail: (err) => {
              console.error('跳转VIP页面失败:', err)
              wx.showToast({
                title: '页面跳转失败',
                icon: 'none'
              })
            }
          })
        } else {
          // 直接完成三问流程
          const answers = { ...this.data.answers }
          answers.wantDiary = false
          this.setData({ answers })
          this.completeThreeQuestions()
        }
      }
    })
  },

  // 生成日记
  generateDiary() {
    console.log('generateDiary 方法被调用')
    this.setData({ isGeneratingDiary: true })
    console.log('设置 isGeneratingDiary 为 true')

    // 这里暂时设置一个模拟的日记内容
    // 实际的AI日记生成将在后续任务中实现
    setTimeout(() => {
      console.log('开始生成模拟日记内容')
      const mockDiary = `今天和微光的对话让我感受到了内心的平静。

在这次交流中，我发现自己对生活有了新的理解。每一个小小的感受都值得被记录，每一次内心的波动都是成长的印记。

明天，我希望能带着今天的收获，继续前行。`

      console.log('模拟日记生成完成:', mockDiary)
      this.setData({
        isGeneratingDiary: false,
        generatedDiary: mockDiary
      })
      console.log('日记生成状态更新完成')
    }, 2000)
  },

  // 保存日记到本地
  saveDiaryToLocal() {
    const { generatedDiary, dialogueTheme } = this.data

    if (!generatedDiary) {
      wx.showToast({
        title: '没有日记内容可保存',
        icon: 'none'
      })
      return
    }

    try {
      // 生成日记文件名（包含日期和主题）
      const now = new Date()
      const dateStr = `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}-${now.getDate().toString().padStart(2, '0')}`
      const fileName = `${dateStr}_${dialogueTheme || '对话日记'}.txt`

      // 保存到本地存储
      const diaryData = {
        date: dateStr,
        theme: dialogueTheme,
        content: generatedDiary,
        createTime: now.toISOString()
      }

      // 获取已保存的日记列表
      let savedDiaries = wx.getStorageSync('savedDiaries') || []
      savedDiaries.push(diaryData)

      // 保存更新后的日记列表
      wx.setStorageSync('savedDiaries', savedDiaries)

      wx.showToast({
        title: '日记已保存到本地',
        icon: 'success',
        duration: 2000
      })

      console.log('日记已保存:', diaryData)
    } catch (error) {
      console.error('保存日记失败:', error)
      wx.showToast({
        title: '保存失败，请重试',
        icon: 'none'
      })
    }
  },

  // 完成三问仪式（使用错误处理机制）
  async completeThreeQuestions() {
    console.log('开始完成三问流程...')

    wx.showLoading({ title: '保存中...' })

    try {
      const result = await this.completeThreeQuestionsWithErrorHandling()

      if (result && result.success !== false) {
        this.setData({ isCompleted: true })

        // 如果有明天留言，显示调度结果
        if (result.messageScheduled && result.messageScheduleResult) {
          wx.showToast({
            title: result.messageScheduleResult.message || '留言已安排投递',
            icon: 'success',
            duration: 2000
          })
        }

        // 延迟跳转，让用户看到留言调度的反馈
        setTimeout(() => {
          this.proceedToNextStep(result)
        }, result.messageScheduled ? 2000 : 0)
      }
    } catch (error) {
      console.error('完成三问流程失败:', error)
      // 错误已在 completeThreeQuestionsWithErrorHandling 中处理
    } finally {
      wx.hideLoading()
    }
  },

  // 页面卸载时清理定时器
  onUnload() {
    // 清理自动保存定时器
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer)
      this.autoSaveTimer = null
    }

    // 清理防抖定时器
    if (this.autoSaveDebounceTimer) {
      clearTimeout(this.autoSaveDebounceTimer)
      this.autoSaveDebounceTimer = null
    }

    console.log('三问页面已卸载，定时器已清理')
  },

  // 页面隐藏时保存状态
  onHide() {
    // 页面隐藏时立即保存当前状态
    this.autoSaveInputState()
    console.log('页面隐藏，状态已保存')
  },

  // 页面显示时恢复状态
  onShow() {
    // 页面重新显示时检查是否需要恢复状态
    if (this.data.autoSaveEnabled) {
      this.restoreInputState()
    }
    console.log('页面显示，状态已检查')
  },

  // 处理关键错误
  async handleCriticalError(error, context) {
    console.error(`[关键错误] ${context}:`, error)

    this.setData({
      criticalErrorOccurred: true,
      systemHealthy: false,
      lastError: {
        message: error.message || error.toString(),
        context,
        timestamp: new Date(),
        severity: 'critical'
      }
    })

    // 记录错误到分析系统
    await errorHandler.recordErrorMetrics(error, context, errorHandler.ERROR_TYPES.SYSTEM, errorHandler.ERROR_SEVERITY.CRITICAL)

    // 显示用户友好的错误提示
    wx.showModal({
      title: '系统异常',
      content: '遇到了一些问题，我们正在努力修复。您可以尝试重新进入页面，或联系客服获得帮助。',
      confirmText: '重新加载',
      cancelText: '继续使用',
      success: (res) => {
        if (res.confirm) {
          // 重新加载页面
          wx.reLaunch({
            url: '/pages/threeQuestions/index'
          })
        } else {
          // 尝试使用备用数据继续
          this.useFallbackData()
        }
      }
    })
  },

  // 处理非关键错误
  async handleNonCriticalError(error, context) {
    console.warn(`[非关键错误] ${context}:`, error)

    this.setData({
      lastError: {
        message: error.message || error.toString(),
        context,
        timestamp: new Date(),
        severity: 'medium'
      }
    })

    // 记录错误到分析系统
    await errorHandler.recordErrorMetrics(error, context, errorHandler.ERROR_TYPES.SYSTEM, errorHandler.ERROR_SEVERITY.MEDIUM)

    // 显示简单的提示
    this.showFeedbackMessage(`${context}失败，已使用备用方案`, 'error', 2000)
  },

  // 处理系统错误
  async handleSystemError(error, context) {
    console.error(`[系统错误] ${context}:`, error)

    // 分类错误并处理
    const result = await errorHandler.handleErrorWithRecovery(error, context, {
      showUserFeedback: true,
      fallbackData: null
    })

    if (result.useFallback) {
      this.setData({ fallbackMode: true })
    }

    if (result.requiresUserAction) {
      // 需要用户操作的错误
      this.showUserActionRequired(result.userMessage)
    }
  },

  // 处理数据加载错误
  async handleDataLoadError(error, context) {
    console.error(`[数据加载错误] ${context}:`, error)

    // 保存错误信息到恢复数据
    this.recoveryManager.saveRecoveryData(`error_${context}`, {
      error: error.message,
      timestamp: Date.now(),
      context
    })

    // 尝试使用恢复数据
    const recoveryData = this.recoveryManager.getRecoveryData(context)
    if (recoveryData) {
      console.log(`使用恢复数据: ${context}`)
      return recoveryData
    }

    // 使用备用数据
    this.useFallbackData()
  },

  // 显示需要用户操作的提示
  showUserActionRequired(message) {
    wx.showModal({
      title: '需要您的操作',
      content: message,
      confirmText: '重试',
      cancelText: '跳过',
      success: (res) => {
        if (res.confirm) {
          // 用户选择重试
          this.retryLastOperation()
        } else {
          // 用户选择跳过
          this.skipCurrentOperation()
        }
      }
    })
  },

  // 重试最后一次操作
  async retryLastOperation() {
    if (this.data.retryCount >= this.data.maxRetries) {
      wx.showToast({
        title: '重试次数已达上限',
        icon: 'none',
        duration: 2000
      })
      return
    }

    this.setData({
      retryCount: this.data.retryCount + 1,
      errorRecoveryMode: true
    })

    // 这里可以根据具体情况重试不同的操作
    console.log(`执行重试，第 ${this.data.retryCount} 次`)
  },

  // 跳过当前操作
  skipCurrentOperation() {
    console.log('用户选择跳过当前操作')
    this.setData({
      errorRecoveryMode: false,
      retryCount: 0
    })
  },

  // 带错误处理的三问完成
  async completeThreeQuestionsWithErrorHandling() {
    try {
      const result = await this.completeThreeQuestionsWithRetry()
      return result || { success: true, message: '三问完成' }
    } catch (error) {
      console.error('完成三问失败:', error)
      await this.handleNonCriticalError(error, '完成三问')
      return { success: false, error: error.message }
    }
  },

  // 带重试的三问完成
  async completeThreeQuestionsWithRetry() {
    const operation = () => this.saveThreeQuestionsData()

    try {
      const result = await errorHandler.retryWithBackoff(operation, {
        maxRetries: 3,
        baseDelay: 1000,
        retryCondition: (error) => {
          // 网络错误或超时错误才重试
          return error.errCode === -1 ||
            error.message.includes('timeout') ||
            error.message.includes('network')
        }
      })
      return result
    } catch (error) {
      // 最终失败，使用数据恢复机制
      await this.handleDataSaveFailure(error)
      throw error
    }
  },

  // 保存三问数据
  async saveThreeQuestionsData() {
    const { answers, dialogueTheme, dialogueSummary, dialogueContent, userLevel } = this.data

    // 构建保存数据
    const saveData = {
      questionAnswers: {
        feeling: answers.feeling || '',
        tomorrowMessage: answers.message || '',
        wantDiary: answers.wantDiary || false
      },
      dialogueContext: {
        theme: dialogueTheme,
        summary: dialogueSummary,
        content: dialogueContent,
        userLevel: userLevel || 3
      }
    }

    // 保存到恢复数据（以防失败）
    this.recoveryManager.saveRecoveryData('threeQuestions_final', saveData)

    // 临时使用本地存储模拟云端保存（避免超时问题）
    console.log('保存三问数据（本地模拟）:', saveData)

    try {
      wx.setStorageSync('threeQuestions_' + Date.now(), saveData)
      console.log('三问数据已保存到本地存储')

      // 模拟云端响应
      return Promise.resolve({
        success: true,
        message: '保存成功（本地模拟）',
        data: saveData
      })
    } catch (error) {
      console.error('本地保存失败:', error)
      return Promise.reject(new Error('本地保存失败: ' + error.message))
    }

    /* 原始云函数调用（已注释以避免超时）
    return new Promise((resolve, reject) => {
      wx.cloud.callFunction({
        name: 'saveThreeQuestions',
        data: saveData,
        success: (res) => {
          if (res.result && res.result.success) {
            // 清除恢复数据
            this.recoveryManager.clearRecoveryData('threeQuestions_final')
            resolve(res.result)
          } else {
            reject(new Error(res.result?.error || '保存失败'))
          }
        },
        fail: reject
      })
    })
    */
  },

  // 处理数据保存失败
  async handleDataSaveFailure(error) {
    console.error('数据保存失败，启用恢复机制:', error)

    // 获取恢复数据
    const recoveryData = this.recoveryManager.getRecoveryData('threeQuestions_final')

    if (recoveryData) {
      // 显示恢复选项
      wx.showModal({
        title: '数据保存失败',
        content: '检测到未保存的数据，是否尝试恢复？',
        confirmText: '恢复数据',
        cancelText: '放弃',
        success: (res) => {
          if (res.confirm) {
            this.recoverUnsavedData(recoveryData)
          } else {
            this.recoveryManager.clearRecoveryData('threeQuestions_final')
          }
        }
      })
    } else {
      // 没有恢复数据，显示错误提示
      wx.showModal({
        title: '保存失败',
        content: '数据保存失败，请检查网络连接后重试。',
        confirmText: '重试',
        cancelText: '稍后再试',
        success: (res) => {
          if (res.confirm) {
            this.completeThreeQuestionsWithRetry()
          }
        }
      })
    }
  },

  // 恢复未保存的数据
  async recoverUnsavedData(recoveryData) {
    try {
      console.log('恢复未保存的数据:', recoveryData)

      // 重新尝试保存
      const result = await this.saveThreeQuestionsData()

      wx.showToast({
        title: '数据恢复成功',
        icon: 'success',
        duration: 2000
      })

      // 继续后续流程
      this.proceedToNextStep(result)
    } catch (error) {
      console.error('数据恢复失败:', error)
      wx.showToast({
        title: '数据恢复失败',
        icon: 'error',
        duration: 2000
      })
    }
  },

  // 带错误处理的日记生成
  async generateDiaryWithErrorHandling() {
    try {
      this.setData({ isGeneratingDiary: true })

      const result = await this.generateDiaryWithRetry()

      if (result.success) {
        this.setData({
          generatedDiary: result.diaryContent,
          isGeneratingDiary: false
        })
        this.showFeedbackMessage('日记生成成功', 'success')
      } else {
        throw new Error(result.error || '日记生成失败')
      }
    } catch (error) {
      console.error('日记生成失败:', error)
      await this.handleAIGenerationError(error, '日记生成')
    } finally {
      this.setData({ isGeneratingDiary: false })
    }
  },

  // 带重试的日记生成
  async generateDiaryWithRetry() {
    const operation = () => {
      return new Promise((resolve, reject) => {
        wx.cloud.callFunction({
          name: 'geminiProxy',
          data: {
            type: 'generateDiary',
            dialogueContext: this.data.dialogueContent,
            userLevel: this.data.userLevel || 3,
            dailyFeeling: this.data.answers.feeling,
            dialogueTheme: this.data.dialogueTheme,
            openid: this.data.userOpenid
          },
          success: (res) => {
            if (res.result && res.result.success) {
              resolve(res.result)
            } else {
              reject(new Error(res.result?.error || 'AI生成失败'))
            }
          },
          fail: reject
        })
      })
    }

    return await errorHandler.retryWithBackoff(operation, {
      maxRetries: 2,
      baseDelay: 2000,
      retryCondition: (error) => {
        // AI生成错误通常不需要重试太多次
        return error.errCode === -1 || error.message.includes('timeout')
      }
    })
  },

  // 处理AI生成错误
  async handleAIGenerationError(error, context) {
    console.error(`AI生成错误 [${context}]:`, error)

    // 记录错误
    await errorHandler.recordErrorMetrics(error, context, errorHandler.ERROR_TYPES.AI_GENERATION, errorHandler.ERROR_SEVERITY.MEDIUM)

    // 使用备用日记
    const fallbackDiary = this.generateFallbackDiary()

    this.setData({
      generatedDiary: fallbackDiary,
      fallbackMode: true
    })

    wx.showModal({
      title: 'AI服务暂时不可用',
      content: '已为您生成备用日记，您可以稍后重新生成。',
      confirmText: '查看日记',
      cancelText: '重新生成',
      success: (res) => {
        if (!res.confirm) {
          // 用户选择重新生成
          setTimeout(() => {
            this.generateDiaryWithErrorHandling()
          }, 1000)
        }
      }
    })
  },

  // 生成备用日记
  generateFallbackDiary() {
    const { dialogueTheme, answers } = this.data
    const feeling = answers.feeling || '平静'

    const fallbackTemplates = [
      `今天和微光聊了关于${dialogueTheme}的话题，让我感到${feeling}。这次对话让我对自己有了新的认识，也让我明白了一些之前困惑的问题。明天，我希望能带着今天的收获继续前行。`,
      `关于${dialogueTheme}，今天的对话给了我很多启发。我的感受是${feeling}，这种感觉很真实。通过这次交流，我发现自己内心的声音更加清晰了。期待明天能有新的成长。`,
      `今天的内心对话围绕${dialogueTheme}展开，我的心情是${feeling}。这次深度的自我探索让我收获很多，也让我更加了解自己。我相信这些思考会帮助我在未来的路上走得更稳。`
    ]

    const randomIndex = Math.floor(Math.random() * fallbackTemplates.length)
    return fallbackTemplates[randomIndex]
  },

  // 带错误处理的消息调度
  async scheduleMessageWithErrorHandling(message) {
    if (!message || message.trim().length === 0) {
      return { success: true, skipped: true }
    }

    try {
      const result = await this.scheduleMessageWithRetry(message)

      if (result.success) {
        this.showFeedbackMessage('明日留言已安排', 'success')
        return result
      } else {
        throw new Error(result.error || '消息调度失败')
      }
    } catch (error) {
      console.error('消息调度失败:', error)
      await this.handleMessageScheduleError(error, message)
      return { success: false, error: error.message }
    }
  },

  // 带重试的消息调度
  async scheduleMessageWithRetry(message) {
    const operation = () => {
      return new Promise((resolve, reject) => {
        wx.cloud.callFunction({
          name: 'scheduleTomorrowMessage',
          data: {
            message: message.trim(),
            sourceType: 'tomorrow_message'
          },
          success: (res) => {
            if (res.result && res.result.success) {
              resolve(res.result)
            } else {
              reject(new Error(res.result?.error || '消息调度失败'))
            }
          },
          fail: reject
        })
      })
    }

    return await errorHandler.retryWithBackoff(operation, {
      maxRetries: 3,
      baseDelay: 1000
    })
  },

  // 处理消息调度错误
  async handleMessageScheduleError(error, message) {
    console.error('消息调度错误:', error)

    // 记录错误
    await errorHandler.recordErrorMetrics(error, '消息调度', errorHandler.ERROR_TYPES.MESSAGE_SCHEDULE, errorHandler.ERROR_SEVERITY.MEDIUM)

    // 保存消息到本地作为备用
    try {
      const pendingMessages = wx.getStorageSync('pendingMessages') || []
      pendingMessages.push({
        message: message,
        timestamp: Date.now(),
        status: 'pending'
      })
      wx.setStorageSync('pendingMessages', pendingMessages)

      wx.showToast({
        title: '留言已暂存，稍后会自动发送',
        icon: 'none',
        duration: 2000
      })
    } catch (storageError) {
      console.error('保存待发送消息失败:', storageError)
      wx.showToast({
        title: '留言发送失败，请稍后重试',
        icon: 'error',
        duration: 2000
      })
    }
  },

  // 页面卸载时的清理工作
  onUnload() {
    // 清理定时器
    if (this.networkCheckTimer) {
      clearInterval(this.networkCheckTimer)
    }

    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer)
    }

    if (this.autoSaveDebounceTimer) {
      clearTimeout(this.autoSaveDebounceTimer)
    }

    // 最后一次自动保存
    try {
      this.autoSaveInputState()
    } catch (error) {
      console.error('页面卸载时自动保存失败:', error)
    }

    console.log('三问页面已卸载，清理工作完成')
  },

  // 页面隐藏时的处理
  onHide() {
    // 保存当前状态
    try {
      this.autoSaveInputState()
    } catch (error) {
      console.error('页面隐藏时保存状态失败:', error)
    }
  },

  // 页面显示时的处理
  onShow() {
    // 检查网络状态
    this.checkNetworkStatusPeriodically()

    // 检查是否有待处理的错误
    if (this.data.criticalErrorOccurred) {
      console.log('检测到之前的关键错误，尝试恢复')
      this.attemptErrorRecovery()
    }
  },

  // 尝试错误恢复
  async attemptErrorRecovery() {
    try {
      // 重新检查系统健康状态
      await this.checkSystemHealth()

      if (this.data.systemHealthy) {
        this.setData({
          criticalErrorOccurred: false,
          errorRecoveryMode: false,
          retryCount: 0
        })

        wx.showToast({
          title: '系统已恢复正常',
          icon: 'success',
          duration: 1500
        })
      }
    } catch (error) {
      console.error('错误恢复失败:', error)
    }
  },

  // 继续到下一步流程
  proceedToNextStep(result) {
    console.log('继续到下一步流程:', result)
    console.log('准备显示点亮星星按钮')

    // 显示点亮星星按钮
    this.setData({
      showLightUpButton: true,
      isCompleted: true
    })

    console.log('点亮星星按钮状态已更新:', {
      showLightUpButton: this.data.showLightUpButton,
      isCompleted: this.data.isCompleted
    })

    // 触发成功反馈
    this.showFeedbackMessage('三问完成！', 'success')
    this.triggerHapticFeedback('success')
  },

  // 测试按钮点击
  testButtonClick() {
    console.log('测试按钮被点击了！')
    wx.showToast({
      title: '测试按钮正常',
      icon: 'success'
    })
  },

  // 点亮星星按钮点击事件 - 简化版本
  lightUpStar() {
    console.log('lightUpStar 方法被调用')
    console.log('当前状态:', {
      isLightingUp: this.data.isLightingUp,
      showLightUpButton: this.data.showLightUpButton,
      isCompleted: this.data.isCompleted
    })

    if (this.data.isLightingUp) {
      console.log('正在点亮中，忽略重复点击')
      return
    }

    console.log('开始点亮星星流程...')
    this.setData({ isLightingUp: true })

    // 简单的闪光效果
    this.showSimpleFlashEffect()

    // 延迟跳转，让用户看到闪光效果
    setTimeout(() => {
      this.navigateToStarMap()
    }, 800)
  },

  // 简单的闪光效果
  showSimpleFlashEffect() {
    console.log('显示简单闪光效果')

    // 触发震动反馈
    wx.vibrateShort()

    // 简单的页面闪光效果
    this.setData({ flashActive: true })

    setTimeout(() => {
      this.setData({ flashActive: false })
    }, 300)

    // 再次闪光
    setTimeout(() => {
      this.setData({ flashActive: true })
      wx.vibrateShort()
    }, 400)

    setTimeout(() => {
      this.setData({ flashActive: false })
    }, 700)
  },



  // 准备星星数据
  prepareStarData() {
    const { answers, dialogueTheme, dialogueSummary, dialogueContent, userOpenid } = this.data

    return {
      id: `star_${Date.now()}`,
      theme: dialogueTheme || '内心对话',
      summary: dialogueSummary || '今天的对话很有意义',
      emotionKeyword: answers.feeling || '今天的感受',
      dialogueContent: dialogueContent || [],
      questions: [
        '今天的感受是什么？',
        '想对明天的自己说什么？',
        '是否生成日记？'
      ],
      answers: [
        answers.feeling || '',
        answers.tomorrowMessage || '',
        answers.wantDiary ? '生成日记' : '直接点亮星星'
      ],
      tomorrowMessage: answers.tomorrowMessage || '',
      hasGeneratedDiary: !!this.data.generatedDiary,
      diaryContent: this.data.generatedDiary || '',
      createTime: new Date(),
      lightingDate: new Date().toISOString(),
      userOpenid: userOpenid
    }
  },

  // 导航到星图页面 - 按需求文档实现
  navigateToStarMap() {
    console.log('准备跳转到星图页面...')

    // 准备星星数据
    const starData = this.prepareStarData()
    console.log('星星数据:', starData)

    // 保存星星数据到本地存储
    try {
      wx.setStorageSync('newStarData', starData)
      wx.setStorageSync('starLightingTimestamp', Date.now())
      console.log('星星数据已保存到本地存储')
    } catch (error) {
      console.error('保存星星数据失败:', error)
    }

    // 保存额外的标识信息到localStorage
    try {
      wx.setStorageSync('fromThreeQuestions', true)
    } catch (error) {
      console.error('保存跳转标识失败:', error)
    }

    // 简单的跳转URL，避免长参数导致的问题
    const starMapUrl = '/pages/starTrack/index?from=threeQuestions'

    console.log('跳转到星图页面:', starMapUrl)

    // 跳转到微光星图页面
    wx.navigateTo({
      url: starMapUrl,
      success: () => {
        console.log('成功跳转到微光星图页面')
        this.setData({ isLightingUp: false })
      },
      fail: (error) => {
        console.error('跳转到星图页面失败:', error)
        this.setData({ isLightingUp: false })

        // 显示错误提示
        wx.showModal({
          title: '跳转失败',
          content: '无法打开微光星图页面，是否重试？',
          confirmText: '重试',
          cancelText: '返回',
          success: (res) => {
            if (res.confirm) {
              // 重试跳转
              this.navigateToStarMap()
            } else {
              // 返回上一页
              wx.navigateBack({
                delta: 1,
                fail: () => {
                  wx.switchTab({ url: '/pages/home/<USER>' })
                }
              })
            }
          }
        })
      }
    })
  }
})