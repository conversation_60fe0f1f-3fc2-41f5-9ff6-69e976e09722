/**
 * Touch Event Optimizer
 * Optimizes touch event handling during zoom and pan operations for better performance
 */

class TouchEventOptimizer {
    constructor() {
        // Throttling configuration
        this.throttleMs = 16; // ~60fps
        this.lastEventTime = 0;
        this.pendingEvents = new Map();

        // Event batching
        this.batchSize = 5;
        this.eventBatch = [];
        this.batchTimeout = null;
        this.batchDelay = 8; // ms

        // Touch tracking
        this.activeTouches = new Map();
        this.touchHistory = [];
        this.maxHistoryLength = 10;

        // Performance monitoring
        this.metrics = {
            totalEvents: 0,
            throttledEvents: 0,
            batchedEvents: 0,
            averageProcessingTime: 0,
            lastFrameTime: 0
        };

        // Gesture detection
        this.gestureState = {
            type: 'none', // 'none', 'pan', 'zoom', 'tap'
            startTime: 0,
            lastUpdate: 0,
            confidence: 0
        };

        // Optimization flags
        this.optimizationsEnabled = {
            throttling: true,
            batching: true,
            gestureDetection: true,
            touchPrediction: false
        };
    }

    /**
     * Initialize the touch event optimizer
     * @param {Object} options - Configuration options
     */
    initialize(options = {}) {
        this.throttleMs = options.throttleMs || this.throttleMs;
        this.batchSize = options.batchSize || this.batchSize;
        this.batchDelay = options.batchDelay || this.batchDelay;

        if (options.optimizations) {
            this.optimizationsEnabled = { ...this.optimizationsEnabled, ...options.optimizations };
        }

        console.log('TouchEventOptimizer initialized:', {
            throttleMs: this.throttleMs,
            batchSize: this.batchSize,
            optimizations: this.optimizationsEnabled
        });

        return { success: true };
    }

    /**
     * Optimize touch start event
     * @param {Object} event - Touch event
     * @param {Function} handler - Event handler
     */
    optimizeTouchStart(event, handler) {
        const startTime = performance.now();

        // Update metrics
        this.metrics.totalEvents++;

        // Track active touches
        if (event.touches) {
            event.touches.forEach(touch => {
                this.activeTouches.set(touch.identifier, {
                    id: touch.identifier,
                    startX: touch.clientX,
                    startY: touch.clientY,
                    currentX: touch.clientX,
                    currentY: touch.clientY,
                    startTime: Date.now(),
                    lastUpdate: Date.now()
                });
            });
        }

        // Detect gesture type
        if (this.optimizationsEnabled.gestureDetection) {
            this.detectGestureStart(event);
        }

        // Process event
        const result = this.processEvent(event, handler, 'touchstart');

        // Update processing time
        this.updateProcessingTime(performance.now() - startTime);

        return result;
    }

    /**
     * Optimize touch move event
     * @param {Object} event - Touch event
     * @param {Function} handler - Event handler
     */
    optimizeTouchMove(event, handler) {
        const startTime = performance.now();
        const now = Date.now();

        this.metrics.totalEvents++;

        // Throttling check
        if (this.optimizationsEnabled.throttling && this.shouldThrottle(now)) {
            this.metrics.throttledEvents++;
            return { success: false, reason: 'throttled' };
        }

        // Update active touches
        if (event.touches) {
            event.touches.forEach(touch => {
                const activeTouch = this.activeTouches.get(touch.identifier);
                if (activeTouch) {
                    activeTouch.currentX = touch.clientX;
                    activeTouch.currentY = touch.clientY;
                    activeTouch.lastUpdate = now;
                }
            });
        }

        // Update gesture detection
        if (this.optimizationsEnabled.gestureDetection) {
            this.updateGestureDetection(event);
        }

        // Add to touch history
        this.addToTouchHistory(event);

        // Process event (with batching if enabled)
        let result;
        if (this.optimizationsEnabled.batching) {
            result = this.batchEvent(event, handler, 'touchmove');
        } else {
            result = this.processEvent(event, handler, 'touchmove');
        }

        this.lastEventTime = now;
        this.updateProcessingTime(performance.now() - startTime);

        return result;
    }

    /**
     * Optimize touch end event
     * @param {Object} event - Touch event
     * @param {Function} handler - Event handler
     */
    optimizeTouchEnd(event, handler) {
        const startTime = performance.now();

        this.metrics.totalEvents++;

        // Remove ended touches
        if (event.changedTouches) {
            event.changedTouches.forEach(touch => {
                this.activeTouches.delete(touch.identifier);
            });
        }

        // Finalize gesture detection
        if (this.optimizationsEnabled.gestureDetection) {
            this.finalizeGestureDetection(event);
        }

        // Flush any pending batched events
        if (this.optimizationsEnabled.batching) {
            this.flushEventBatch();
        }

        // Process event
        const result = this.processEvent(event, handler, 'touchend');

        this.updateProcessingTime(performance.now() - startTime);

        return result;
    }

    /**
     * Check if event should be throttled
     * @param {number} now - Current timestamp
     */
    shouldThrottle(now) {
        return (now - this.lastEventTime) < this.throttleMs;
    }

    /**
     * Process event with optional optimizations
     * @param {Object} event - Touch event
     * @param {Function} handler - Event handler
     * @param {string} eventType - Type of event
     */
    processEvent(event, handler, eventType) {
        try {
            // Add optimization metadata
            const optimizedEvent = {
                ...event,
                optimized: true,
                gestureType: this.gestureState.type,
                confidence: this.gestureState.confidence,
                touchCount: this.activeTouches.size,
                eventType: eventType
            };

            // Call handler
            const result = handler(optimizedEvent);

            return { success: true, result };
        } catch (error) {
            console.error('Event processing error:', error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Batch event for processing
     * @param {Object} event - Touch event
     * @param {Function} handler - Event handler
     * @param {string} eventType - Type of event
     */
    batchEvent(event, handler, eventType) {
        // Add event to batch
        this.eventBatch.push({ event, handler, eventType, timestamp: Date.now() });
        this.metrics.batchedEvents++;

        // Process batch if full or timeout
        if (this.eventBatch.length >= this.batchSize) {
            this.flushEventBatch();
        } else if (!this.batchTimeout) {
            this.batchTimeout = setTimeout(() => {
                this.flushEventBatch();
            }, this.batchDelay);
        }

        return { success: true, batched: true };
    }

    /**
     * Flush batched events
     */
    flushEventBatch() {
        if (this.batchTimeout) {
            clearTimeout(this.batchTimeout);
            this.batchTimeout = null;
        }

        if (this.eventBatch.length === 0) return;

        // Process all events in batch
        const batch = [...this.eventBatch];
        this.eventBatch = [];

        // Use requestAnimationFrame for smooth processing (fallback to setTimeout in Node.js)
        const scheduleExecution = (callback) => {
            if (typeof requestAnimationFrame !== 'undefined') {
                requestAnimationFrame(callback);
            } else {
                setTimeout(callback, 16); // ~60fps fallback
            }
        };

        scheduleExecution(() => {
            batch.forEach(({ event, handler, eventType }) => {
                this.processEvent(event, handler, eventType);
            });
        });
    }

    /**
     * Detect gesture start
     * @param {Object} event - Touch event
     */
    detectGestureStart(event) {
        const touchCount = event.touches ? event.touches.length : 0;
        const now = Date.now();

        this.gestureState = {
            type: touchCount === 1 ? 'pan' : touchCount === 2 ? 'zoom' : 'none',
            startTime: now,
            lastUpdate: now,
            confidence: 0.5
        };
    }

    /**
     * Update gesture detection
     * @param {Object} event - Touch event
     */
    updateGestureDetection(event) {
        const now = Date.now();
        const touchCount = event.touches ? event.touches.length : 0;

        // Update gesture type based on touch count
        if (touchCount === 1 && this.gestureState.type !== 'pan') {
            this.gestureState.type = 'pan';
            this.gestureState.confidence = 0.7;
        } else if (touchCount === 2 && this.gestureState.type !== 'zoom') {
            this.gestureState.type = 'zoom';
            this.gestureState.confidence = 0.8;
        }

        // Increase confidence over time
        const duration = now - this.gestureState.startTime;
        if (duration > 100) { // After 100ms, we're more confident
            this.gestureState.confidence = Math.min(1.0, this.gestureState.confidence + 0.1);
        }

        this.gestureState.lastUpdate = now;
    }

    /**
     * Finalize gesture detection
     * @param {Object} event - Touch event
     */
    finalizeGestureDetection(event) {
        const duration = Date.now() - this.gestureState.startTime;

        // Detect tap gesture
        if (duration < 200 && this.gestureState.type === 'pan') {
            this.gestureState.type = 'tap';
            this.gestureState.confidence = 0.9;
        }

        // Reset after a delay
        setTimeout(() => {
            this.gestureState = {
                type: 'none',
                startTime: 0,
                lastUpdate: 0,
                confidence: 0
            };
        }, 100);
    }

    /**
     * Add event to touch history
     * @param {Object} event - Touch event
     */
    addToTouchHistory(event) {
        this.touchHistory.push({
            timestamp: Date.now(),
            touches: event.touches ? Array.from(event.touches).map(touch => ({
                id: touch.identifier,
                x: touch.clientX,
                y: touch.clientY
            })) : [],
            gestureType: this.gestureState.type
        });

        // Limit history length
        if (this.touchHistory.length > this.maxHistoryLength) {
            this.touchHistory.shift();
        }
    }

    /**
     * Update processing time metrics
     * @param {number} processingTime - Time taken to process
     */
    updateProcessingTime(processingTime) {
        const currentAvg = this.metrics.averageProcessingTime;
        const totalEvents = this.metrics.totalEvents;

        this.metrics.averageProcessingTime =
            (currentAvg * (totalEvents - 1) + processingTime) / totalEvents;

        this.metrics.lastFrameTime = processingTime;
    }

    /**
     * Get current gesture information
     */
    getCurrentGesture() {
        return {
            type: this.gestureState.type,
            confidence: this.gestureState.confidence,
            duration: Date.now() - this.gestureState.startTime,
            touchCount: this.activeTouches.size
        };
    }

    /**
     * Get touch velocity (for prediction)
     */
    getTouchVelocity() {
        if (this.touchHistory.length < 2) {
            return { x: 0, y: 0 };
        }

        const recent = this.touchHistory.slice(-2);
        const timeDiff = recent[1].timestamp - recent[0].timestamp;

        if (timeDiff === 0 || recent[0].touches.length === 0 || recent[1].touches.length === 0) {
            return { x: 0, y: 0 };
        }

        const touch0 = recent[0].touches[0];
        const touch1 = recent[1].touches[0];

        return {
            x: (touch1.x - touch0.x) / timeDiff,
            y: (touch1.y - touch0.y) / timeDiff
        };
    }

    /**
     * Get performance metrics
     */
    getMetrics() {
        return {
            ...this.metrics,
            throttleRate: this.metrics.totalEvents > 0
                ? (this.metrics.throttledEvents / this.metrics.totalEvents) * 100
                : 0,
            batchRate: this.metrics.totalEvents > 0
                ? (this.metrics.batchedEvents / this.metrics.totalEvents) * 100
                : 0,
            activeTouches: this.activeTouches.size,
            gestureState: { ...this.gestureState }
        };
    }

    /**
     * Reset metrics
     */
    resetMetrics() {
        this.metrics = {
            totalEvents: 0,
            throttledEvents: 0,
            batchedEvents: 0,
            averageProcessingTime: 0,
            lastFrameTime: 0
        };
    }

    /**
     * Update configuration
     * @param {Object} config - New configuration
     */
    updateConfig(config) {
        if (config.throttleMs !== undefined) {
            this.throttleMs = config.throttleMs;
        }
        if (config.batchSize !== undefined) {
            this.batchSize = config.batchSize;
        }
        if (config.batchDelay !== undefined) {
            this.batchDelay = config.batchDelay;
        }
        if (config.optimizations) {
            this.optimizationsEnabled = { ...this.optimizationsEnabled, ...config.optimizations };
        }

        console.log('TouchEventOptimizer configuration updated:', config);
    }

    /**
     * Enable/disable specific optimizations
     * @param {Object} optimizations - Optimization flags
     */
    setOptimizations(optimizations) {
        this.optimizationsEnabled = { ...this.optimizationsEnabled, ...optimizations };
    }

    /**
     * Clear all state
     */
    clear() {
        this.activeTouches.clear();
        this.touchHistory = [];
        this.eventBatch = [];
        if (this.batchTimeout) {
            clearTimeout(this.batchTimeout);
            this.batchTimeout = null;
        }
        this.gestureState = {
            type: 'none',
            startTime: 0,
            lastUpdate: 0,
            confidence: 0
        };
    }
}

module.exports = TouchEventOptimizer;