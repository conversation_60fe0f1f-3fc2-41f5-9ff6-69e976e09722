// 简化版星图页面 - 移除复杂依赖，解决白屏问题
Page({
    data: {
        userOpenid: '',
        starRecords: [], // 星光对话记录
        isLoading: true,
        selectedRecord: null, // 选中的对话记录
        showDetail: false, // 是否显示详情弹窗

        // Interactive placement state
        isPlacementMode: false, // 是否在放置模式
        newStarData: null, // 待放置的新星星数据
        placementInstructions: '', // 放置指引文本
        screenBounds: { width: 0, height: 0 }, // 屏幕尺寸
        newStarPreviewPosition: { x: 50, y: 50 }, // 新星星预览位置
        canConfirmPlacement: false, // 是否可以确认放置
        currentPlacementPosition: null, // 当前放置位置
        isFirstStar: false, // 是否是首颗星

        // Map navigation state
        mapTransform: 'translate(0px, 0px) scale(1)', // CSS transform for map
        zoomLevel: 1.0,
        zoomLevelText: '100%', // 缩放级别文本显示
        canZoomIn: true,
        canZoomOut: false,
        isNavigating: false // 是否在导航模式（缩放/平移）
    },

    onLoad(options) {
        console.log('星图页面加载，参数:', options)

        // 检查是否从三问页面跳转过来，从localStorage读取新星星数据
        if (options && options.from === 'threeQuestions') {
            try {
                const newStarData = wx.getStorageSync('newStarData')
                const fromThreeQuestions = wx.getStorageSync('fromThreeQuestions')

                if (newStarData && fromThreeQuestions) {
                    console.log('从localStorage读取到新星星数据:', newStarData)
                    this.setData({ newStarData })

                    // 清除localStorage中的数据，避免重复使用
                    wx.removeStorageSync('fromThreeQuestions')
                } else {
                    console.log('未找到新星星数据')
                }
            } catch (error) {
                console.error('读取新星星数据失败:', error)
            }
        }

        // 获取用户openid
        this.getUserOpenid()
    },

    onShow() {
        // 每次显示页面时刷新数据
        if (this.data.userOpenid) {
            this.loadStarRecords()
        }

        // 获取屏幕尺寸
        this.getScreenBounds()
    },

    onReady() {
        // 页面渲染完成后获取屏幕尺寸
        this.getScreenBounds()
    },

    // 获取用户openid
    getUserOpenid() {
        wx.cloud.callFunction({
            name: 'getOpenid'
        }).then(res => {
            console.log('getUserOpenid', res.result.openid)
            if (res.result && res.result.openid) {
                this.setData({
                    userOpenid: res.result.openid
                })
                // 获取到openid后加载星光记录
                this.loadStarRecords()
            }
        }).catch(err => {
            console.error('获取用户openid失败', err)
            this.setData({ isLoading: false })
        })
    },

    // 简化版加载星光对话记录
    async loadStarRecords() {
        if (!this.data.userOpenid) return

        this.setData({ isLoading: true })

        try {
            console.log('从云端获取星光记录...')
            const res = await wx.cloud.callFunction({
                name: 'getUserHistory',
                data: {
                    openid: this.data.userOpenid,
                    limit: 20, // 获取最近20条记录
                    includeStarPosition: true // 包含星座位置信息
                }
            })

            console.log('星光记录获取结果:', res.result)

            if (res.result.success && res.result.history) {
                // 简化数据处理，直接使用返回的数据
                const starRecords = res.result.history.map((record, index) => {
                    // 如果没有星座位置，生成默认样式
                    if (!record.starStyle) {
                        record.starStyle = this.generateStarStyle(index)
                    }
                    return record
                })

                this.setData({
                    starRecords: starRecords,
                    isLoading: false
                })

                // 生成现有星星的连线
                this.generateStarConnections(starRecords)

                console.log('处理后的星光记录:', starRecords.length)
            } else {
                console.log('没有找到星光记录')
                this.setData({
                    starRecords: [],
                    isLoading: false
                })
            }
        } catch (error) {
            console.error('加载星光记录失败:', error)
            this.setData({
                starRecords: [],
                isLoading: false
            })

            wx.showToast({
                title: '加载失败，请重试',
                icon: 'none'
            })
        }
    },

    // 为每颗星星生成随机样式
    generateStarStyle(index) {
        // 确保星星不会重叠，使用网格布局的思想
        const cols = 3 // 每行3颗星星
        const row = Math.floor(index / cols)
        const col = index % cols

        // 基础位置（网格）
        const baseLeft = 15 + col * 30 // 15%, 45%, 75%
        const baseTop = 20 + row * 15 // 从20%开始，每行间隔15%

        // 添加随机偏移，让星星看起来更自然
        const randomOffsetX = (Math.random() - 0.5) * 10 // ±5%的随机偏移
        const randomOffsetY = (Math.random() - 0.5) * 8  // ±4%的随机偏移

        return {
            left: Math.max(5, Math.min(90, baseLeft + randomOffsetX)), // 确保在5%-90%范围内
            top: Math.max(15, Math.min(85, baseTop + randomOffsetY)), // 确保在15%-85%范围内
            size: 0.8 + Math.random() * 0.4, // 0.8-1.2倍大小
            brightness: 0.7 + Math.random() * 0.3, // 0.7-1.0透明度
            animationDelay: Math.random() * 2 // 0-2秒的动画延迟
        }
    },

    // 简化版点击星星查看详情
    onStarTap(e) {
        const { index } = e.currentTarget.dataset
        const record = this.data.starRecords[index]

        if (!record) {
            return
        }

        // 直接显示详情弹窗
        this.setData({
            selectedRecord: record,
            showDetail: true
        })

        console.log('显示星星详情:', record)
    },

    // 关闭详情弹窗
    closeDetail() {
        this.setData({
            showDetail: false,
            selectedRecord: null
        })
    },

    // 格式化时间显示
    formatTime(timestamp) {
        if (!timestamp) return '未知时间'

        const date = new Date(timestamp)
        const now = new Date()
        const diff = now - date

        // 如果是今天
        if (diff < 24 * 60 * 60 * 1000 && date.getDate() === now.getDate()) {
            return `今天 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
        }

        // 如果是昨天
        const yesterday = new Date(now)
        yesterday.setDate(yesterday.getDate() - 1)
        if (date.getDate() === yesterday.getDate() && date.getMonth() === yesterday.getMonth()) {
            return `昨天 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
        }

        // 其他时间
        return `${date.getMonth() + 1}月${date.getDate()}日`
    },

    // 获取情感关键词的颜色
    getEmotionColor(emotion) {
        const emotionColors = {
            '开心': '#FFD700',
            '快乐': '#FFD700',
            '喜悦': '#FFD700',
            '平静': '#87CEEB',
            '安静': '#87CEEB',
            '宁静': '#87CEEB',
            '困惑': '#DDA0DD',
            '迷茫': '#DDA0DD',
            '纠结': '#DDA0DD',
            '焦虑': '#F0A0A0',
            '担心': '#F0A0A0',
            '紧张': '#F0A0A0',
            '感动': '#FFB6C1',
            '温暖': '#FFB6C1',
            '治愈': '#98FB98'
        }

        return emotionColors[emotion] || '#FFFFFF'
    },

    // 下拉刷新
    onPullDownRefresh() {
        this.loadStarRecords().then(() => {
            wx.stopPullDownRefresh()
        })
    },

    // 导航到对话页面
    navigateToDialogue() {
        wx.navigateTo({
            url: '/pages/dialogue/index'
        })
    },

    // 测试添加新星星功能
    testAddNewStar() {
        console.log('测试添加新星星功能')

        // 模拟新星星数据
        const testStarData = {
            id: `test_star_${Date.now()}`,
            theme: '测试对话',
            summary: '这是一个测试星星，用于验证放置功能',
            emotionKeyword: '测试',
            dialogueContent: [
                { role: 'user', content: '这是测试消息' },
                { role: 'ai', content: '这是AI回复' }
            ],
            questions: [
                '今天的感受是什么？',
                '想对明天的自己说什么？',
                '是否生成日记？'
            ],
            answers: [
                '测试感受',
                '明天要更好',
                '直接点亮星星'
            ],
            tomorrowMessage: '明天要更好',
            createTime: new Date(),
            hasGeneratedDiary: false,
            diaryContent: ''
        }

        // 设置新星星数据并进入放置模式
        this.setData({
            newStarData: testStarData,
            isLoading: false
        })

        // 获取屏幕尺寸并进入放置模式
        this.getScreenBounds()

        wx.showToast({
            title: '测试星星已准备，请放置位置',
            icon: 'none',
            duration: 2000
        })
    },

    // 获取屏幕尺寸
    getScreenBounds() {
        const query = wx.createSelectorQuery()

        // 尝试多个可能的容器元素
        query.select('.star-records-container').boundingClientRect()
        query.select('.star-sky-container').boundingClientRect()
        query.select('.placement-mode-container').boundingClientRect()

        query.exec((res) => {
            let screenBounds = null

            // 找到第一个有效的容器
            for (let i = 0; i < res.length; i++) {
                if (res[i]) {
                    screenBounds = {
                        width: res[i].width,
                        height: res[i].height
                    }
                    break
                }
            }

            // 如果没有找到容器，使用系统信息作为降级方案
            if (!screenBounds) {
                const systemInfo = wx.getSystemInfoSync()
                screenBounds = {
                    width: systemInfo.windowWidth,
                    height: systemInfo.windowHeight * 0.8 // 预留头部空间
                }
                console.log('使用系统信息作为屏幕尺寸:', screenBounds)
            }

            this.setData({ screenBounds })
            console.log('屏幕尺寸:', screenBounds)

            // 如果有新星星数据，进入放置模式
            if (this.data.newStarData) {
                console.log('检测到新星星数据，进入放置模式')
                this.enterPlacementMode()
            }
        })
    },

    // 进入星星放置模式
    enterPlacementMode() {
        const isFirstStar = this.data.starRecords.length === 0
        const instructions = isFirstStar
            ? '选择一个位置来安放你的第一颗星星吧'
            : '拖拽星星到轨道上的合适位置'

        this.setData({
            isPlacementMode: true,
            isFirstStar,
            placementInstructions: instructions
        })

        // 如果不是首颗星，显示轨道路径动画
        if (!isFirstStar && this.data.starRecords.length > 0) {
            const lastStar = this.data.starRecords[this.data.starRecords.length - 1]
            if (lastStar && lastStar.starStyle) {
                this.animateOrbitPath({
                    x: lastStar.starStyle.left,
                    y: lastStar.starStyle.top
                }, 120) // 120rpx 半径
            }
        }

        console.log('进入放置模式:', { isFirstStar, instructions })
    },

    // 放置模式点击事件
    onStarPlacementTap(e) {
        if (!this.data.isPlacementMode || !this.data.newStarData) {
            return
        }

        const { x, y } = e.detail
        const touchPoint = { x, y }

        if (this.data.isFirstStar) {
            this.placeFirstStar(touchPoint)
        } else {
            this.placeOrbitStar(touchPoint)
        }
    },

    // 放置首颗星星
    placeFirstStar(touchPoint) {
        console.log('放置首颗星星:', touchPoint)

        // 转换为百分比坐标
        const { screenBounds } = this.data
        const x = (touchPoint.x / screenBounds.width) * 100
        const y = (touchPoint.y / screenBounds.height) * 100

        // 限制在有效范围内
        const clampedX = Math.max(10, Math.min(90, x))
        const clampedY = Math.max(15, Math.min(85, y))

        const position = { x: clampedX / 100, y: clampedY / 100 }
        this.confirmStarPlacement(position)
    },

    // 放置轨道星星
    placeOrbitStar(touchPoint) {
        console.log('放置轨道星星:', touchPoint)

        if (this.data.starRecords.length === 0) {
            console.error('没有锚点星星，无法放置轨道星星')
            return
        }

        // 转换为百分比坐标
        const { screenBounds } = this.data
        const x = (touchPoint.x / screenBounds.width) * 100
        const y = (touchPoint.y / screenBounds.height) * 100

        // 限制在有效范围内
        const clampedX = Math.max(10, Math.min(90, x))
        const clampedY = Math.max(15, Math.min(85, y))

        const position = { x: clampedX / 100, y: clampedY / 100 }

        // 验证轨道位置（简化版本，允许更灵活的放置）
        const lastStar = this.data.starRecords[this.data.starRecords.length - 1]
        const distance = Math.sqrt(
            Math.pow((position.x - lastStar.starStyle.left / 100), 2) +
            Math.pow((position.y - lastStar.starStyle.top / 100), 2)
        )

        // 如果距离太近，稍微调整位置
        if (distance < 0.15) { // 15%的最小距离
            const angle = Math.atan2(
                position.y - lastStar.starStyle.top / 100,
                position.x - lastStar.starStyle.left / 100
            )
            position.x = lastStar.starStyle.left / 100 + Math.cos(angle) * 0.15
            position.y = lastStar.starStyle.top / 100 + Math.sin(angle) * 0.15

            // 确保调整后的位置仍在有效范围内
            position.x = Math.max(0.1, Math.min(0.9, position.x))
            position.y = Math.max(0.15, Math.min(0.85, position.y))
        }

        this.confirmStarPlacement(position)
    },

    // 放置模式触摸开始
    onPlacementTouchStart(e) {
        if (!this.data.isPlacementMode || !this.data.newStarData) {
            return
        }

        const touch = e.touches[0]
        const { clientX, clientY } = touch

        // 转换为百分比坐标
        const { screenBounds } = this.data
        const x = (clientX / screenBounds.width) * 100
        const y = (clientY / screenBounds.height) * 100

        this.setData({
            newStarPreviewPosition: { x, y },
            canConfirmPlacement: true,
            currentPlacementPosition: { x: x / 100, y: y / 100 }
        })

        console.log('放置模式触摸开始:', { x, y })
    },

    // 放置模式触摸移动
    onPlacementTouchMove(e) {
        if (!this.data.isPlacementMode || !this.data.newStarData) {
            return
        }

        const touch = e.touches[0]
        const { clientX, clientY } = touch

        // 转换为百分比坐标
        const { screenBounds } = this.data
        const x = (clientX / screenBounds.width) * 100
        const y = (clientY / screenBounds.height) * 100

        // 限制在有效范围内
        const clampedX = Math.max(5, Math.min(95, x))
        const clampedY = Math.max(10, Math.min(90, y))

        this.setData({
            newStarPreviewPosition: { x: clampedX, y: clampedY },
            currentPlacementPosition: { x: clampedX / 100, y: clampedY / 100 }
        })

        // 触发拖拽动画
        this.animateStarDrag({ x: clampedX, y: clampedY })
    },

    // 星星触摸开始事件
    onStarTouchStart(e) {
        const { index } = e.currentTarget.dataset

        // 添加触摸反馈动画
        this.animateStarHover(index, true)

        // 添加触摸反馈类
        this.setData({
            [`starRecords[${index}].touchActive`]: true
        })
    },

    // 星星触摸结束事件
    onStarTouchEnd(e) {
        const { index } = e.currentTarget.dataset

        // 移除触摸反馈动画
        this.animateStarHover(index, false)

        // 移除触摸反馈类
        this.setData({
            [`starRecords[${index}].touchActive`]: false
        })
    },

    // 放置模式触摸结束
    onPlacementTouchEnd(e) {
        if (!this.data.isPlacementMode || !this.data.newStarData) {
            return
        }

        // 触摸结束时保持当前位置，等待用户确认
        console.log('放置模式触摸结束，等待确认:', this.data.currentPlacementPosition)
    },

    // 确认当前放置位置
    confirmCurrentPlacement() {
        if (!this.data.canConfirmPlacement || !this.data.currentPlacementPosition) {
            wx.showToast({
                title: '请先选择放置位置',
                icon: 'none'
            })
            return
        }

        const position = this.data.currentPlacementPosition
        this.confirmStarPlacement(position)
    },

    // 确认星星放置
    confirmStarPlacement(position) {
        const { newStarData } = this.data

        // 创建新的星星记录
        const newStarRecord = {
            id: newStarData.id || `star_${Date.now()}`,
            theme: newStarData.theme || '未知主题',
            summary: newStarData.summary || '',
            emotionKeyword: newStarData.emotionKeyword || '',
            dialogueContent: newStarData.dialogueContent || [],
            questions: newStarData.questions || [],
            answers: newStarData.answers || [],
            tomorrowMessage: newStarData.tomorrowMessage || '',
            createTime: new Date(),
            starStyle: {
                left: position.x * 100,
                top: position.y * 100,
                size: 1.0,
                brightness: 1.0,
                animationDelay: 0
            },
            // 保存星座位置信息
            constellationPosition: position
        }

        // 添加到星星记录中
        const updatedStarRecords = [...this.data.starRecords, newStarRecord]

        this.setData({
            starRecords: updatedStarRecords,
            isPlacementMode: false,
            newStarData: null,
            placementInstructions: ''
        })

        // 触发星星放置动画
        this.animateStarPlacement(newStarRecord, updatedStarRecords.length - 1)

        // 生成星星连线
        this.generateStarConnections(updatedStarRecords)

        // 简化版保存到云端
        this.saveStarPosition(newStarRecord)

        // 显示成功提示
        wx.showToast({
            title: '星星放置成功！',
            icon: 'success'
        })

        console.log('星星放置成功:', newStarRecord)
    },

    // 简化版保存星星位置到云端
    async saveStarPosition(starRecord) {
        try {
            console.log('开始保存星星位置到云端:', starRecord.id)

            // 调用云函数保存位置
            const result = await wx.cloud.callFunction({
                name: 'updateStarPosition',
                data: {
                    recordId: starRecord.id,
                    starPosition: starRecord.starStyle,
                    operation: 'update'
                }
            })

            if (result.result && result.result.success) {
                console.log('星星位置保存成功:', result.result)
                return { success: true, result: result.result }
            } else {
                throw new Error(result.result?.error || '保存失败')
            }

        } catch (error) {
            console.error('保存星星位置失败:', error)
            wx.showToast({
                title: '保存失败，请重试',
                icon: 'none'
            })
            return { success: false, error: error.message }
        }
    },

    // ========== 星星动画方法 ==========

    // 星星出现动画
    animateStarAppearance(starRecord, starIndex) {
        console.log('开始星星出现动画:', starIndex)

        // 使用 requestAnimationFrame 确保流畅度
        const animateFrame = () => {
            const query = wx.createSelectorQuery()
            query.select(`.dialogue-star[data-index="${starIndex}"]`).boundingClientRect()
            query.exec((res) => {
                if (res[0]) {
                    const starElement = res[0]

                    // 添加出现动画类
                    this.setData({
                        [`starRecords[${starIndex}].animationClass`]: 'star-appear-animation'
                    })

                    // 动画完成后移除动画类
                    setTimeout(() => {
                        this.setData({
                            [`starRecords[${starIndex}].animationClass`]: ''
                        })
                    }, 800)
                }
            })
        }

        // 使用 requestAnimationFrame 优化性能
        if (typeof requestAnimationFrame !== 'undefined') {
            requestAnimationFrame(animateFrame)
        } else {
            setTimeout(animateFrame, 16) // 降级方案
        }
    },

    // 星星放置动画
    animateStarPlacement(starRecord, starIndex) {
        console.log('开始星星放置动画:', starIndex)

        const animateFrame = () => {
            // 添加放置动画类
            this.setData({
                [`starRecords[${starIndex}].animationClass`]: 'star-placement-animation'
            })

            // 创建放置确认动画效果
            this.createPlacementConfirmEffect(starRecord.starStyle)

            // 动画完成后移除动画类
            setTimeout(() => {
                this.setData({
                    [`starRecords[${starIndex}].animationClass`]: ''
                })
            }, 600)
        }

        if (typeof requestAnimationFrame !== 'undefined') {
            requestAnimationFrame(animateFrame)
        } else {
            setTimeout(animateFrame, 16)
        }
    },

    // 创建放置确认动画效果
    createPlacementConfirmEffect(starStyle) {
        console.log('创建放置确认效果')

        // 创建脉冲效果
        const pulseEffect = {
            left: starStyle.left,
            top: starStyle.top,
            show: true,
            id: `pulse_${Date.now()}`
        }

        this.setData({
            placementPulseEffect: pulseEffect
        })

        // 1秒后移除效果
        setTimeout(() => {
            this.setData({
                placementPulseEffect: null
            })
        }, 1000)
    },

    // 轨道路径显示动画
    animateOrbitPath(anchorPosition, radius = 100) {
        console.log('显示轨道路径动画')

        const orbitPath = {
            centerX: anchorPosition.x,
            centerY: anchorPosition.y,
            radius: radius,
            show: true,
            animationClass: 'orbit-path-animation'
        }

        this.setData({
            orbitPathAnimation: orbitPath
        })
    },

    // 隐藏轨道路径动画
    hideOrbitPath() {
        this.setData({
            orbitPathAnimation: null
        })
    },

    // 星星拖拽动画
    animateStarDrag(position) {
        const animateFrame = () => {
            this.setData({
                newStarPreviewPosition: position,
                starDragAnimation: 'star-drag-animation'
            })
        }

        if (typeof requestAnimationFrame !== 'undefined') {
            requestAnimationFrame(animateFrame)
        } else {
            setTimeout(animateFrame, 16)
        }
    },

    // 星星悬停效果
    animateStarHover(starIndex, isHover) {
        const animationClass = isHover ? 'star-hover-animation' : ''

        this.setData({
            [`starRecords[${starIndex}].hoverAnimation`]: animationClass
        })
    },

    // 生成星星连线
    generateStarConnections(starRecords) {
        if (starRecords.length < 2) {
            // 少于2颗星星时不需要连线
            this.setData({ starConnections: [] })
            return
        }

        const connections = []

        // 连接相邻的星星（按时间顺序）
        for (let i = 0; i < starRecords.length - 1; i++) {
            const currentStar = starRecords[i]
            const nextStar = starRecords[i + 1]

            if (currentStar.starStyle && nextStar.starStyle) {
                const connection = this.calculateConnectionLine(
                    currentStar.starStyle,
                    nextStar.starStyle,
                    i
                )
                connections.push(connection)
            }
        }

        // 如果有3颗或更多星星，可以添加一些额外的连线形成星座
        if (starRecords.length >= 3) {
            // 连接第一颗和最后一颗星星，形成闭合的星座
            const firstStar = starRecords[0]
            const lastStar = starRecords[starRecords.length - 1]

            if (firstStar.starStyle && lastStar.starStyle) {
                const closingConnection = this.calculateConnectionLine(
                    firstStar.starStyle,
                    lastStar.starStyle,
                    starRecords.length - 1,
                    'closing'
                )
                connections.push(closingConnection)
            }
        }

        // 更新连线数据
        this.setData({ starConnections: connections })

        // 触发连线动画
        this.animateStarConnections(connections)

        console.log('生成星星连线:', connections.length)
    },

    // 计算两颗星星之间的连线
    calculateConnectionLine(star1Style, star2Style, index, type = 'normal') {
        const x1 = star1Style.left
        const y1 = star1Style.top
        const x2 = star2Style.left
        const y2 = star2Style.top

        // 计算连线的位置和角度
        const deltaX = x2 - x1
        const deltaY = y2 - y1
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)
        const angle = Math.atan2(deltaY, deltaX) * (180 / Math.PI)

        // 连线的中心点
        const centerX = (x1 + x2) / 2
        const centerY = (y1 + y2) / 2

        return {
            id: `connection_${index}_${type}`,
            x1, y1, x2, y2,
            centerX, centerY,
            width: distance,
            angle,
            type,
            animationDelay: index * 0.2 // 连线依次出现
        }
    },

    // 星星连线动画
    animateStarConnections(connections) {
        connections.forEach((connection, index) => {
            setTimeout(() => {
                // 添加连线出现动画
                this.setData({
                    [`starConnections[${index}].animationClass`]: 'connection-appear-animation'
                })

                // 动画完成后移除动画类
                setTimeout(() => {
                    this.setData({
                        [`starConnections[${index}].animationClass`]: ''
                    })
                }, 800)
            }, connection.animationDelay * 1000)
        })
    },

    // 退出放置模式
    exitPlacementMode() {
        // 隐藏轨道路径动画
        this.hideOrbitPath()

        this.setData({
            isPlacementMode: false,
            newStarData: null,
            placementInstructions: '',
            newStarPreviewPosition: { x: 50, y: 50 },
            canConfirmPlacement: false,
            currentPlacementPosition: null
        })

        console.log('退出放置模式')

        // 显示确认对话框
        wx.showModal({
            title: '取消放置',
            content: '确定要取消星星放置吗？',
            confirmText: '确定',
            cancelText: '继续放置',
            success: (res) => {
                if (res.confirm) {
                    // 返回上一页或首页
                    wx.navigateBack({
                        delta: 1,
                        fail: () => {
                            wx.switchTab({ url: '/pages/home/<USER>' })
                        }
                    })
                } else {
                    // 重新进入放置模式
                    this.enterPlacementMode()
                }
            }
        })
    }
})