// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const userCollection = db.collection('users')

// 层级定义常量
const LEVELS = {
  1: { name: "关闭层", description: "需要极致温柔和无条件支持" },
  2: { name: "徘徊层", description: "需要鼓励和发现积极火花" },
  3: { name: "挣扎层", description: "需要将痛苦转化为行动能量" },
  4: { name: "主人翁层", description: "需要深度自我探索和反思" },
  5: { name: "创造者层", description: "需要思想碰撞和创造性激发" }
}

// 默认层级
const DEFAULT_LEVEL = 3

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const { type, userInfo, lightName, userName } = event

  switch (type) {
    case 'login':
      // 查询用户是否存在
      const user = await userCollection.where({
        _openid: wxContext.OPENID
      }).get()

      // 用户不存在，创建新用户
      if (!user.data.length) {
        const newUser = {
          _openid: wxContext.OPENID,
          unionid: wxContext.UNIONID || '',
          createTime: db.serverDate(),
          userInfo: userInfo || {},
          points: 0, // 光点初始值
          vip: false, // VIP状态
          vipExpireTime: null, // VIP过期时间
          level: DEFAULT_LEVEL, // 用户层级（数字1-5，默认为3）
          levelName: LEVELS[DEFAULT_LEVEL].name, // 层级名称
          oldLevel: '', // 保留旧的层级字段（前置七问的结果）
          lastLoginTime: db.serverDate(),
          lightName: lightName || '', // 新增
          userName: userName || ''    // 新增
        }

        // 创建用户记录
        await userCollection.add({
          data: newUser
        })

        return {
          success: true,
          data: newUser,
          message: '新用户创建成功'
        }
      }

      // 检查现有用户是否有新的层级字段，如果没有则添加默认层级
      const existingUser = user.data[0];
      const updateData = {
        lastLoginTime: db.serverDate(),
        userInfo: userInfo || existingUser.userInfo
      };

      // 如果用户没有新的层级字段，添加默认层级
      if (existingUser.level === undefined || existingUser.level === null || typeof existingUser.level === 'string') {
        updateData.level = DEFAULT_LEVEL;
        updateData.levelName = LEVELS[DEFAULT_LEVEL].name;
        updateData.levelUpdateTime = db.serverDate();
        // 保留旧的层级字段
        if (typeof existingUser.level === 'string') {
          updateData.oldLevel = existingUser.level;
        }
      }

      // 更新用户最后登录时间和层级信息
      await userCollection.where({
        _openid: wxContext.OPENID
      }).update({
        data: updateData
      });

      // 获取更新后的用户数据
      const updatedUser = await userCollection.where({
        _openid: wxContext.OPENID
      }).get();

      return {
        success: true,
        data: updatedUser.data[0],
        message: '用户登录成功'
      }

    case 'getUserInfo':
      // 获取用户信息
      const userDetail = await userCollection.where({
        _openid: wxContext.OPENID
      }).get()

      if (!userDetail.data.length) {
        return {
          success: false,
          message: '用户不存在'
        }
      }

      return {
        success: true,
        data: userDetail.data[0],
        message: '获取用户信息成功'
      }

    case 'getSelf': // 新增获取用户自身数据，包含光点
      const selfUser = await userCollection.where({
        _openid: wxContext.OPENID
      }).get();
      if (selfUser.data && selfUser.data.length > 0) {
        return {
          success: true,
          data: selfUser.data[0],
          message: '获取用户自身数据成功'
        };
      } else {
        return {
          success: false,
          data: { points: 0 }, // 用户不存在时返回默认光点0
          message: '用户不存在，返回默认光点'
        };
      }

    case 'updateName':
      // 更新用户lightName和userName
      await userCollection.where({
        _openid: wxContext.OPENID
      }).update({
        data: {
          lightName: lightName || '',
          userName: userName || ''
        }
      });
      return {
        success: true,
        message: '昵称更新成功'
      }

    case 'judgeLevel':
      // 前置七问计分与层级判定
      // answers为[0,1,2,...]，A=1分，B=2分，C=3分
      const answers = event.answers || [];
      let total = 0;
      for (let i = 0; i < answers.length; i++) {
        total += (answers[i] + 1);
      }
      let userLevel = '';
      if (total <= 14) userLevel = 'struggling';
      else if (total <= 17) userLevel = 'growing';
      else userLevel = 'blooming';
      await userCollection.where({ _openid: wxContext.OPENID }).update({
        data: { level: userLevel }
      });
      return {
        success: true,
        level: userLevel
      }

    case 'consumePoints':
      // 扣除用户光点
      const pointsToConsume = event.points || 0;

      // 先查询用户当前光点数量
      const currentUser = await userCollection.where({
        _openid: wxContext.OPENID
      }).get();

      if (!currentUser.data.length) {
        return {
          success: false,
          message: '用户不存在'
        };
      }

      const currentPoints = currentUser.data[0].points || 0;

      // 检查光点是否足够
      if (currentPoints < pointsToConsume) {
        return {
          success: false,
          message: '光点不足',
          currentPoints: currentPoints
        };
      }

      // 扣除光点
      const newPoints = currentPoints - pointsToConsume;
      await userCollection.where({
        _openid: wxContext.OPENID
      }).update({
        data: {
          points: newPoints
        }
      });

      return {
        success: true,
        message: '光点扣除成功',
        consumedPoints: pointsToConsume,
        remainingPoints: newPoints
      };

    case 'setLevel':
      // 设置用户层级
      const { level, levelName, reason, triggeredBy } = event;

      // 验证层级参数
      if (!level || !Number.isInteger(level) || level < 1 || level > 5) {
        return {
          success: false,
          message: '无效的层级参数，层级必须是1-5之间的整数'
        };
      }

      // 如果没有提供levelName，使用默认的层级名称
      const finalLevelName = levelName || LEVELS[level].name;

      try {
        // 先获取用户当前层级
        const userWithLevel = await userCollection.where({
          _openid: wxContext.OPENID
        }).get();

        let fromLevel = null;
        if (userWithLevel.data.length > 0) {
          fromLevel = userWithLevel.data[0].level;
        }

        // 更新用户层级
        await userCollection.where({
          _openid: wxContext.OPENID
        }).update({
          data: {
            level: level,
            levelName: finalLevelName,
            levelUpdateTime: db.serverDate()
          }
        });

        // 记录层级切换（如果层级发生了变化）
        if (fromLevel !== null && fromLevel !== level) {
          try {
            await cloud.callFunction({
              name: 'tieringAnalytics',
              data: {
                type: 'recordLevelSwitch',
                data: {
                  openid: wxContext.OPENID,
                  fromLevel: fromLevel,
                  toLevel: level,
                  reason: reason || 'manual_update',
                  triggeredBy: triggeredBy || 'user'
                }
              }
            });
            console.log('层级切换记录成功:', fromLevel, '->', level);
          } catch (error) {
            console.error('记录层级切换失败:', error);
            // 不影响主流程，只记录错误
          }
        }

        return {
          success: true,
          message: '用户层级设置成功',
          level: level,
          levelName: finalLevelName,
          fromLevel: fromLevel
        };
      } catch (error) {
        console.error('设置用户层级失败:', error);
        return {
          success: false,
          message: '设置用户层级失败',
          error: error.message
        };
      }

    case 'getLevel':
      // 获取用户层级
      try {
        const levelUser = await userCollection.where({
          _openid: wxContext.OPENID
        }).get();

        if (!levelUser.data.length) {
          return {
            success: false,
            message: '用户不存在'
          };
        }

        const userData = levelUser.data[0];

        // 如果用户没有新的层级字段，设置默认层级
        if (userData.level === undefined || userData.level === null) {
          await userCollection.where({
            _openid: wxContext.OPENID
          }).update({
            data: {
              level: DEFAULT_LEVEL,
              levelName: LEVELS[DEFAULT_LEVEL].name,
              levelUpdateTime: db.serverDate()
            }
          });

          return {
            success: true,
            message: '获取用户层级成功（已设置默认层级）',
            level: DEFAULT_LEVEL,
            levelName: LEVELS[DEFAULT_LEVEL].name,
            isDefault: true
          };
        }

        return {
          success: true,
          message: '获取用户层级成功',
          level: userData.level,
          levelName: userData.levelName || LEVELS[userData.level]?.name || '未知层级',
          levelUpdateTime: userData.levelUpdateTime
        };
      } catch (error) {
        console.error('获取用户层级失败:', error);
        return {
          success: false,
          message: '获取用户层级失败',
          error: error.message
        };
      }

    default:
      return {
        success: false,
        message: '未知的操作类型'
      }
  }
} 