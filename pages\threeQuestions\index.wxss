page {
  height: 100vh;
  overflow: hidden;
}

.three-questions-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  position: relative;
}

/* 反馈消息样式 */
.feedback-message {
  position: fixed;
  top: 100rpx;
  left: 50%;
  transform: translateX(-50%) translateY(-100rpx);
  z-index: 1000;
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;
}

.feedback-message.show {
  transform: translateX(-50%) translateY(0);
  opacity: 1;
}

.feedback-content {
  display: flex;
  align-items: center;
  gap: 15rpx;
  padding: 20rpx 30rpx;
  border-radius: 50rpx;
  backdrop-filter: blur(20rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
  min-width: 200rpx;
  justify-content: center;
}

.feedback-success .feedback-content {
  background: linear-gradient(
    135deg,
    rgba(76, 175, 80, 0.9),
    rgba(139, 195, 74, 0.9)
  );
  color: white;
}

.feedback-error .feedback-content {
  background: linear-gradient(
    135deg,
    rgba(244, 67, 54, 0.9),
    rgba(255, 87, 34, 0.9)
  );
  color: white;
}

.feedback-info .feedback-content {
  background: linear-gradient(
    135deg,
    rgba(33, 150, 243, 0.9),
    rgba(3, 169, 244, 0.9)
  );
  color: white;
}

.feedback-icon {
  font-size: 32rpx;
  font-weight: bold;
}

.feedback-text {
  font-size: 28rpx;
  font-weight: 500;
}

.background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

/* 页面头部 */
.header {
  padding: 30rpx;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10rpx);
  position: relative;
}

.theme {
  font-size: 36rpx;
  font-weight: bold;
  color: white;
}

/* 自动保存指示器 */
.auto-save-indicator {
  position: absolute;
  top: 10rpx;
  right: 20rpx;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
}

.save-time {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 对话总结 */
.summary-container {
  background-color: rgba(255, 255, 255, 0.9);
  padding: 30rpx;
  margin: 20rpx;
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
}

.summary-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.summary-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 问题容器 */
.question-container {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  padding: 30rpx;
  background-color: rgba(255, 255, 255, 0.9);
  margin: 0 20rpx 20rpx;
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
}

/* 问题头部 */
.question-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx;
}

.question-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #6b7aff;
  margin-bottom: 20rpx;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.question-title.transitioning {
  opacity: 0.5;
  transform: translateY(-10rpx);
}

.progress-container {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  gap: 15rpx;
  cursor: pointer;
  padding: 10rpx;
  border-radius: 20rpx;
  transition: background-color 0.2s ease;
}

.progress-container:active {
  background-color: rgba(107, 122, 255, 0.1);
}

.progress-dot {
  width: 16rpx;
  height: 16rpx;
  background-color: rgba(224, 224, 224, 0.6);
  border-radius: 50%;
  margin: 0 10rpx;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.progress-dot.active {
  background-color: #6b7aff;
  width: 32rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(107, 122, 255, 0.4);
}

.progress-dot.current {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(107, 122, 255, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10rpx rgba(107, 122, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(107, 122, 255, 0);
  }
}

.progress-detail {
  background-color: rgba(107, 122, 255, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.progress-text {
  font-size: 24rpx;
  color: #6b7aff;
  font-weight: 500;
}

.question-text {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 30rpx;
  line-height: 1.5;
  text-align: center;
}

/* 输入区域 */
.input-section {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.input-section.focused {
  transform: scale(1.02);
}

.input-container {
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.input-container.transitioning {
  opacity: 0.7;
  transform: translateX(10rpx);
}

.answer-input {
  width: 100%;
  min-height: 200rpx;
  background-color: rgba(244, 244, 248, 0.8);
  border-radius: 16rpx;
  padding: 20rpx;
  font-size: 28rpx;
  margin-bottom: 10rpx;
  color: #333;
  border: 2px solid rgba(107, 122, 255, 0.2);
  box-sizing: border-box;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  line-height: 1.5;
  resize: none;
}

.answer-input.focused {
  border-color: #6b7aff;
  background-color: rgba(244, 244, 248, 0.95);
  box-shadow: 0 4rpx 20rpx rgba(107, 122, 255, 0.15);
  transform: translateY(-2rpx);
}

.answer-input.input-error {
  border-color: #ff6b6b;
  background-color: rgba(255, 107, 107, 0.05);
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5rpx);
  }
  75% {
    transform: translateX(5rpx);
  }
}

/* 输入增强提示 */
.input-enhancement {
  position: absolute;
  top: -40rpx;
  left: 0;
  right: 0;
  animation: slideInDown 0.3s ease;
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.enhancement-tips {
  background-color: rgba(107, 122, 255, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  text-align: center;
}

.tip-text {
  font-size: 22rpx;
  color: #6b7aff;
}

/* 输入状态 */
.input-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.char-count {
  text-align: right;
}

.count-text {
  font-size: 24rpx;
  color: #999;
  transition: color 0.3s ease;
}

.count-text.count-error {
  color: #ff6b6b;
  font-weight: bold;
  animation: pulse-error 1s ease-in-out;
}

@keyframes pulse-error {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.input-indicator {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.indicator-dot {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background-color: #ccc;
  transition: all 0.3s ease;
}

.indicator-dot.active {
  background-color: #6b7aff;
  animation: blink 1.5s infinite;
}

@keyframes blink {
  0%,
  50% {
    opacity: 1;
  }
  51%,
  100% {
    opacity: 0.3;
  }
}

.indicator-text {
  font-size: 22rpx;
  color: #6b7aff;
}

/* 错误信息显示 */
.error-message {
  margin-bottom: 10rpx;
  display: flex;
  align-items: center;
  gap: 10rpx;
  padding: 15rpx;
  background-color: rgba(255, 107, 107, 0.1);
  border-radius: 12rpx;
  border-left: 4rpx solid #ff6b6b;
}

.error-message.slide-in {
  animation: slideInLeft 0.3s ease;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.error-icon {
  font-size: 24rpx;
}

.error-text {
  font-size: 24rpx;
  color: #ff6b6b;
  flex: 1;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 20rpx;
  margin-top: auto;
  transition: opacity 0.3s ease;
}

.action-buttons.disabled {
  opacity: 0.6;
  pointer-events: none;
}

.skip-btn,
.next-btn {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  outline: none;
}

.skip-btn {
  flex: 1;
  background-color: rgba(255, 255, 255, 0.8);
  color: #666;
  border-radius: 50rpx;
  padding: 25rpx;
  font-size: 28rpx;
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.skip-btn:active {
  transform: scale(0.95);
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.skip-btn.loading {
  background-color: rgba(255, 255, 255, 0.6);
  color: #999;
}

.next-btn {
  flex: 2;
  background: linear-gradient(135deg, #6b7aff, #8b9aff);
  color: white;
  border-radius: 50rpx;
  padding: 25rpx;
  font-size: 28rpx;
  font-weight: bold;
  box-shadow: 0 8rpx 24rpx rgba(107, 122, 255, 0.4);
}

.next-btn:active {
  transform: scale(0.95);
  box-shadow: 0 4rpx 16rpx rgba(107, 122, 255, 0.6);
}

.next-btn.loading {
  background: linear-gradient(
    135deg,
    rgba(107, 122, 255, 0.7),
    rgba(139, 154, 255, 0.7)
  );
}

/* 按钮内容和加载状态 */
.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15rpx;
  position: relative;
}

.btn-text {
  transition: opacity 0.3s ease;
}

.btn-loading {
  position: absolute;
  right: -30rpx;
  opacity: 0;
  transition: all 0.3s ease;
}

.loading .btn-loading {
  opacity: 1;
  right: 0;
}

.loading .btn-text {
  opacity: 0.7;
}

.loading-spinner {
  width: 20rpx;
  height: 20rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-top: 2rpx solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 移动端触摸优化 */
@media (max-width: 750rpx) {
  .action-buttons {
    gap: 15rpx;
  }

  .skip-btn,
  .next-btn {
    padding: 30rpx 20rpx;
    font-size: 30rpx;
    min-height: 100rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .skip-btn:active,
  .next-btn:active {
    transform: scale(0.92);
  }
}

/* 高分辨率屏幕优化 */
@media (min-resolution: 2dppx) {
  .skip-btn,
  .next-btn {
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
  }

  .next-btn {
    box-shadow: 0 8rpx 32rpx rgba(107, 122, 255, 0.3);
  }
}

/* 日记生成区域 */
.diary-section {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.diary-choices {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.diary-intro {
  text-align: center;
  margin-bottom: 40rpx;
}

.intro-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

.choice-buttons {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
  margin-bottom: 40rpx;
}

.generate-diary-btn {
  background: linear-gradient(135deg, #6b7aff, #8b9aff);
  color: white;
  border-radius: 50rpx;
  padding: 40rpx 30rpx;
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(107, 122, 255, 0.4);
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.generate-diary-btn.vip-required {
  background: linear-gradient(135deg, #ff9500, #ffb84d);
  box-shadow: 0 8rpx 24rpx rgba(255, 149, 0, 0.4);
}

.generate-diary-btn:active {
  transform: scale(0.98);
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20rpx;
}

.btn-icon {
  font-size: 36rpx;
}

.btn-text {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.btn-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 5rpx;
}

.btn-subtitle {
  font-size: 24rpx;
  opacity: 0.8;
}

.vip-badge {
  position: absolute;
  top: 10rpx;
  right: 20rpx;
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  font-size: 20rpx;
  font-weight: bold;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
}

.skip-diary-btn {
  background-color: rgba(255, 255, 255, 0.8);
  color: #666;
  border-radius: 50rpx;
  padding: 30rpx;
  font-size: 28rpx;
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15rpx;
}

.skip-diary-btn:active {
  transform: scale(0.98);
  background-color: rgba(255, 255, 255, 0.9);
}

.skip-icon {
  font-size: 28rpx;
}

/* VIP特权说明 */
.vip-benefits {
  background-color: rgba(255, 149, 0, 0.1);
  border-radius: 20rpx;
  padding: 30rpx;
  border: 1px solid rgba(255, 149, 0, 0.2);
}

.benefits-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #ff9500;
  margin-bottom: 20rpx;
  text-align: center;
}

.benefits-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.benefit-item {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.benefit-icon {
  font-size: 28rpx;
  width: 40rpx;
  text-align: center;
}

.benefit-text {
  font-size: 26rpx;
  color: #666;
}

/* 日记生成中状态 */
.generating-diary {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
}

.loading-animation {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-dots {
  display: flex;
  gap: 10rpx;
  margin-bottom: 30rpx;
}

.dot {
  width: 12rpx;
  height: 12rpx;
  background-color: #6b7aff;
  border-radius: 50%;
  animation: loading-bounce 1.4s ease-in-out infinite both;
}

.dot1 {
  animation-delay: -0.32s;
}

.dot2 {
  animation-delay: -0.16s;
}

@keyframes loading-bounce {
  0%,
  80%,
  100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.loading-text {
  font-size: 28rpx;
  color: #6b7aff;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.loading-subtitle {
  font-size: 24rpx;
  color: #999;
}

/* 已生成日记显示 */
.generated-diary {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.diary-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.diary-icon {
  width: 60rpx;
  height: 60rpx;
}

.diary-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #6b7aff;
}

.diary-content {
  background-color: rgba(244, 244, 248, 0.8);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  border: 1px solid rgba(107, 122, 255, 0.2);
  max-height: 400rpx;
  overflow-y: auto;
}

.diary-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  white-space: pre-wrap;
}

.diary-actions {
  display: flex;
  gap: 20rpx;
}

.save-diary-btn {
  flex: 1;
  background-color: rgba(255, 255, 255, 0.8);
  color: #666;
  border-radius: 50rpx;
  padding: 25rpx;
  font-size: 26rpx;
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
}

.save-diary-btn:active {
  transform: scale(0.98);
  background-color: rgba(255, 255, 255, 0.9);
}

.save-icon {
  font-size: 24rpx;
}

.complete-btn {
  flex: 2;
  background: linear-gradient(135deg, #6b7aff, #8b9aff);
  color: white;
  border-radius: 50rpx;
  padding: 25rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(107, 122, 255, 0.4);
  transition: all 0.2s ease;
}

.complete-btn:active {
  transform: scale(0.98);
}

/* 星星点亮按钮样式 */
.light-up-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  background-color: rgba(255, 255, 255, 0.9);
  margin: 20rpx;
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
}

.light-up-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #6b7aff;
  margin-bottom: 20rpx;
  text-align: center;
}

.light-up-description {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

.light-up-btn {
  background: linear-gradient(135deg, #ffd700, #ffa500);
  color: white;
  border-radius: 50rpx;
  padding: 40rpx 60rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  box-shadow: 0 12rpx 32rpx rgba(255, 215, 0, 0.4);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20rpx;
  min-width: 300rpx;
}

.light-up-btn:active {
  transform: scale(0.95);
  box-shadow: 0 8rpx 24rpx rgba(255, 215, 0, 0.6);
}

.light-up-btn.lighting {
  background: linear-gradient(135deg, #ffd700, #ffff00);
  animation: pulse-glow 0.3s ease-in-out infinite alternate;
}

@keyframes pulse-glow {
  0% {
    box-shadow: 0 12rpx 32rpx rgba(255, 215, 0, 0.4);
  }
  100% {
    box-shadow: 0 16rpx 40rpx rgba(255, 215, 0, 0.8);
  }
}

.star-icon {
  font-size: 40rpx;
  animation: twinkle 2s ease-in-out infinite;
}

@keyframes twinkle {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

.light-up-btn.lighting .star-icon {
  animation: spin-twinkle 0.5s linear infinite;
}

@keyframes spin-twinkle {
  0% {
    transform: rotate(0deg) scale(1);
  }
  50% {
    transform: rotate(180deg) scale(1.2);
  }
  100% {
    transform: rotate(360deg) scale(1);
  }
}

/* AnimationEngine 闪光效果样式 */
.flash-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 9999;
}

.flash-layer-0,
.flash-layer-1,
.flash-layer-2 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.flash-layer-0 {
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(255, 255, 255, 0.3) 70%,
    rgba(255, 255, 255, 0) 100%
  );
}

.flash-layer-1 {
  background: radial-gradient(
    circle,
    rgba(255, 215, 0, 0.7) 0%,
    rgba(255, 215, 0, 0.2) 70%,
    rgba(255, 215, 0, 0) 100%
  );
  transform: scale(1.1);
}

.flash-layer-2 {
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.5) 0%,
    rgba(255, 255, 255, 0.1) 70%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: scale(1.2);
}

.flash-active-0 {
  opacity: 0.9 !important;
  animation: flash-pulse-0 0.3s ease-in-out;
}

.flash-active-1 {
  opacity: 0.7 !important;
  animation: flash-pulse-1 0.3s ease-in-out 0.05s;
}

.flash-active-2 {
  opacity: 0.5 !important;
  animation: flash-pulse-2 0.3s ease-in-out 0.1s;
}

@keyframes flash-pulse-0 {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  50% {
    opacity: 0.9;
    transform: scale(1);
  }
  100% {
    opacity: 0;
    transform: scale(1.2);
  }
}

@keyframes flash-pulse-1 {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
  100% {
    opacity: 0;
    transform: scale(1.3);
  }
}

@keyframes flash-pulse-2 {
  0% {
    opacity: 0;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.2);
  }
  100% {
    opacity: 0;
    transform: scale(1.4);
  }
}

/* 降级闪光效果（备用方案） */
.three-questions-container.flash-active {
  animation: simple-flash 0.2s ease-in-out;
}

@keyframes simple-flash {
  0% {
    background-color: rgba(255, 255, 255, 0);
  }
  50% {
    background-color: rgba(255, 255, 255, 0.8);
  }
  100% {
    background-color: rgba(255, 255, 255, 0);
  }
}

/* 星星出现动画 */
.star-appearing {
  animation: star-appear 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes star-appear {
  0% {
    opacity: 0;
    transform: scale(0) rotate(0deg);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.2) rotate(180deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(360deg);
  }
}

/* 轨道路径可视化 */
.orbit-path-visible {
  position: relative;
}

.orbit-path-visible::after {
  content: "";
  position: absolute;
  border: 2rpx dashed rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  pointer-events: none;
  animation: orbit-fade-in 0.3s ease-in-out;
}

@keyframes orbit-fade-in {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 星星发光效果 */
.star-glowing {
  animation: star-glow 3s ease-in-out infinite;
}

@keyframes star-glow {
  0%,
  100% {
    box-shadow: 0 0 10rpx rgba(255, 215, 0, 0.5);
  }
  50% {
    box-shadow: 0 0 30rpx rgba(255, 215, 0, 0.8);
  }
}

/* 放置模式过渡 */
.placement-mode {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-active {
  opacity: 0.8;
  transform: scale(0.98);
}
