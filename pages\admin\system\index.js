// 系统管理页面
const SystemMonitor = require('../../../utils/monitoring/SystemMonitor.js').default
const DeploymentManager = require('../../../utils/deployment/DeploymentManager.js').default
const PerformanceOptimizer = require('../../../utils/constellation/performance/PerformanceOptimizer.js').default
const ConstellationTestSuite = require('../../../tests/constellation/ConstellationTestSuite.js').default
const PerformanceBenchmark = require('../../../tests/performance/PerformanceBenchmark.js').default

Page({
    data: {
        // 系统状态
        systemHealth: {
            score: 100,
            status: 'healthy',
            uptime: 0
        },
        
        // 性能指标
        performanceMetrics: {
            frameRate: 60,
            memoryUsage: 0,
            responseTime: 0,
            networkLatency: 0
        },
        
        // 部署状态
        deploymentStatus: {
            currentVersion: '1.0.0',
            environment: 'production',
            isDeploying: false
        },
        
        // 测试结果
        testResults: {
            total: 0,
            passed: 0,
            failed: 0,
            successRate: '0%'
        },
        
        // 基准测试结果
        benchmarkResults: {
            renderingFPS: 0,
            animationFPS: 0,
            memoryEfficiency: 0
        },
        
        // 告警信息
        alerts: [],
        
        // 功能开关
        featureFlags: [],
        
        // 页面状态
        activeTab: 'overview',
        isLoading: false,
        lastUpdate: Date.now()
    },

    onLoad() {
        this.initializeSystemManagement()
        this.loadSystemStatus()
        this.startAutoRefresh()
    },

    onUnload() {
        this.stopAutoRefresh()
    },

    // 初始化系统管理
    async initializeSystemManagement() {
        try {
            console.log('初始化系统管理...')
            
            // 初始化各个管理器
            await SystemMonitor.initialize()
            await DeploymentManager.initialize()
            await PerformanceOptimizer.initialize()
            await ConstellationTestSuite.initialize()
            await PerformanceBenchmark.initialize()
            
            console.log('系统管理初始化完成')
        } catch (error) {
            console.error('系统管理初始化失败:', error)
            wx.showToast({
                title: '初始化失败',
                icon: 'error'
            })
        }
    },

    // 加载系统状态
    async loadSystemStatus() {
        try {
            this.setData({ isLoading: true })

            // 获取系统健康状态
            const systemHealth = SystemMonitor.getSystemHealth()
            
            // 获取性能报告
            const performanceReport = SystemMonitor.getPerformanceReport()
            
            // 获取部署状态
            const deploymentStatus = DeploymentManager.getDeploymentStatus()
            
            // 获取测试结果
            const testResults = ConstellationTestSuite.getTestResults()
            
            // 获取基准测试结果
            const benchmarkResults = PerformanceBenchmark.getBenchmarkResults()

            this.setData({
                systemHealth,
                performanceMetrics: performanceReport.metrics.performance,
                deploymentStatus,
                testResults,
                benchmarkResults: benchmarkResults.summary || {},
                alerts: performanceReport.alerts || [],
                featureFlags: deploymentStatus.featureFlags || [],
                lastUpdate: Date.now()
            })

        } catch (error) {
            console.error('加载系统状态失败:', error)
            wx.showToast({
                title: '加载失败',
                icon: 'error'
            })
        } finally {
            this.setData({ isLoading: false })
        }
    },

    // 开始自动刷新
    startAutoRefresh() {
        this.refreshInterval = setInterval(() => {
            this.loadSystemStatus()
        }, 30000) // 每30秒刷新一次
    },

    // 停止自动刷新
    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval)
            this.refreshInterval = null
        }
    },

    // 切换标签页
    switchTab(e) {
        const { tab } = e.currentTarget.dataset
        this.setData({ activeTab: tab })
    },

    // 手动刷新
    onRefresh() {
        this.loadSystemStatus()
    },

    // 运行系统测试
    async runSystemTests() {
        try {
            wx.showLoading({ title: '运行测试中...' })
            
            const results = await ConstellationTestSuite.runAllTests()
            
            this.setData({
                testResults: {
                    total: results.total,
                    passed: results.passed,
                    failed: results.failed,
                    successRate: results.successRate || '0%'
                }
            })

            wx.hideLoading()
            
            if (results.failed === 0) {
                wx.showToast({
                    title: '所有测试通过',
                    icon: 'success'
                })
            } else {
                wx.showModal({
                    title: '测试结果',
                    content: `${results.passed}个通过，${results.failed}个失败`,
                    showCancel: false
                })
            }

        } catch (error) {
            wx.hideLoading()
            console.error('运行测试失败:', error)
            wx.showToast({
                title: '测试失败',
                icon: 'error'
            })
        }
    },

    // 运行性能基准测试
    async runPerformanceBenchmark() {
        try {
            wx.showLoading({ title: '运行基准测试...' })
            
            const results = await PerformanceBenchmark.runAllBenchmarks()
            
            this.setData({
                benchmarkResults: results.summary || {}
            })

            wx.hideLoading()
            wx.showToast({
                title: '基准测试完成',
                icon: 'success'
            })

        } catch (error) {
            wx.hideLoading()
            console.error('基准测试失败:', error)
            wx.showToast({
                title: '基准测试失败',
                icon: 'error'
            })
        }
    },

    // 切换功能开关
    async toggleFeatureFlag(e) {
        const { feature, enabled } = e.currentTarget.dataset
        
        try {
            DeploymentManager.setFeatureFlag(feature, !enabled)
            
            // 刷新功能开关状态
            const deploymentStatus = DeploymentManager.getDeploymentStatus()
            this.setData({
                featureFlags: deploymentStatus.featureFlags
            })

            wx.showToast({
                title: `${feature} ${!enabled ? '已启用' : '已禁用'}`,
                icon: 'success'
            })

        } catch (error) {
            console.error('切换功能开关失败:', error)
            wx.showToast({
                title: '操作失败',
                icon: 'error'
            })
        }
    },

    // 设置性能模式
    async setPerformanceMode(e) {
        const { mode } = e.currentTarget.dataset
        
        try {
            const result = PerformanceOptimizer.setPerformanceMode(mode)
            
            if (result) {
                wx.showToast({
                    title: `性能模式已设为${mode}`,
                    icon: 'success'
                })
                
                // 刷新性能指标
                setTimeout(() => {
                    this.loadSystemStatus()
                }, 1000)
            } else {
                throw new Error('设置性能模式失败')
            }

        } catch (error) {
            console.error('设置性能模式失败:', error)
            wx.showToast({
                title: '设置失败',
                icon: 'error'
            })
        }
    },

    // 清理系统缓存
    async clearSystemCache() {
        try {
            wx.showModal({
                title: '确认清理',
                content: '这将清理所有系统缓存，可能影响性能。是否继续？',
                success: async (res) => {
                    if (res.confirm) {
                        wx.showLoading({ title: '清理中...' })
                        
                        // 清理各种缓存
                        const cacheKeys = [
                            'starRecords',
                            'constellation_cache',
                            'performance_cache',
                            'animation_cache'
                        ]

                        cacheKeys.forEach(key => {
                            try {
                                wx.removeStorageSync(key)
                            } catch (error) {
                                console.warn(`清理缓存 ${key} 失败:`, error)
                            }
                        })

                        wx.hideLoading()
                        wx.showToast({
                            title: '缓存已清理',
                            icon: 'success'
                        })

                        // 刷新系统状态
                        this.loadSystemStatus()
                    }
                }
            })

        } catch (error) {
            console.error('清理缓存失败:', error)
            wx.showToast({
                title: '清理失败',
                icon: 'error'
            })
        }
    },

    // 导出系统报告
    async exportSystemReport() {
        try {
            wx.showLoading({ title: '生成报告...' })
            
            // 收集所有系统数据
            const systemReport = {
                timestamp: new Date().toISOString(),
                systemHealth: this.data.systemHealth,
                performanceMetrics: this.data.performanceMetrics,
                deploymentStatus: this.data.deploymentStatus,
                testResults: this.data.testResults,
                benchmarkResults: this.data.benchmarkResults,
                alerts: this.data.alerts,
                featureFlags: this.data.featureFlags
            }

            // 保存到本地存储
            wx.setStorageSync('system_report', systemReport)
            
            wx.hideLoading()
            wx.showModal({
                title: '报告已生成',
                content: '系统报告已保存到本地存储，可通过开发者工具查看',
                showCancel: false
            })

        } catch (error) {
            wx.hideLoading()
            console.error('导出报告失败:', error)
            wx.showToast({
                title: '导出失败',
                icon: 'error'
            })
        }
    },

    // 重启系统监控
    async restartMonitoring() {
        try {
            SystemMonitor.stopMonitoring()
            await SystemMonitor.initialize()
            
            wx.showToast({
                title: '监控已重启',
                icon: 'success'
            })
            
            this.loadSystemStatus()

        } catch (error) {
            console.error('重启监控失败:', error)
            wx.showToast({
                title: '重启失败',
                icon: 'error'
            })
        }
    },

    // 格式化时间
    formatTime(timestamp) {
        if (!timestamp) return '未知'
        
        const date = new Date(timestamp)
        return date.toLocaleString('zh-CN')
    },

    // 格式化持续时间
    formatDuration(ms) {
        if (!ms) return '0秒'
        
        const seconds = Math.floor(ms / 1000)
        const minutes = Math.floor(seconds / 60)
        const hours = Math.floor(minutes / 60)
        const days = Math.floor(hours / 24)

        if (days > 0) return `${days}天${hours % 24}小时`
        if (hours > 0) return `${hours}小时${minutes % 60}分钟`
        if (minutes > 0) return `${minutes}分钟${seconds % 60}秒`
        return `${seconds}秒`
    },

    // 格式化内存大小
    formatMemorySize(bytes) {
        if (!bytes) return '0B'
        
        const units = ['B', 'KB', 'MB', 'GB']
        let size = bytes
        let unitIndex = 0
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024
            unitIndex++
        }
        
        return `${size.toFixed(1)}${units[unitIndex]}`
    },

    // 下拉刷新
    onPullDownRefresh() {
        this.loadSystemStatus()
        setTimeout(() => {
            wx.stopPullDownRefresh()
        }, 1000)
    }
})
