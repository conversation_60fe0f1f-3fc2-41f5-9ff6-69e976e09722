# AI 分层提示系统部署指南

本文档提供了 AI 分层提示系统的部署步骤和监控指南。

## 部署前准备

1. 确保已安装最新版本的微信开发者工具
2. 确保云开发环境已配置正确
3. 确保有足够的数据库权限和云函数调用权限

## 部署步骤

### 1. 部署云函数

以下云函数需要部署到云环境：

- `initAiPromptsDatabase`: 初始化 AI 提示数据库
- `aiPromptManager`: AI 提示管理
- `user`: 用户管理（已更新支持层级操作）
- `geminiProxy`: AI 对话代理（已更新支持层级提示）
- `initTieringAnalytics`: 初始化层级分析数据库
- `tieringAnalytics`: 层级系统监控和分析

使用微信开发者工具上传这些云函数：

1. 在微信开发者工具中打开项目
2. 在左侧文件树中找到 `cloudfunctions` 目录
3. 右键点击每个云函数文件夹，选择"上传并部署：云端安装依赖"

### 2. 执行部署脚本

我们提供了自动化部署脚本 `deploy-tiering-system.js`，它将执行以下操作：

1. 执行数据库结构迁移
2. 导入 AI 提示数据
3. 初始化监控系统
4. 执行系统集成测试

执行部署脚本：

```bash
# 在微信开发者工具的终端中执行
node deploy-tiering-system.js
```

### 3. 验证部署

部署完成后，请验证以下功能：

1. 打开小程序，进入对话页面，确认对话功能正常
2. 检查用户层级是否正确显示
3. 访问层级系统监控仪表板页面，确认数据正常显示
4. 尝试切换用户层级，确认层级切换功能正常

## 监控系统

### 监控脚本

我们提供了监控脚本 `monitor-tiering-system.js`，它将检查：

1. 系统性能指标
2. 用户层级分布
3. 异常使用模式
4. 对话质量指标
5. AI 提示数据库状态

执行监控脚本：

```bash
# 在微信开发者工具的终端中执行
node monitor-tiering-system.js
```

### 监控仪表板

层级系统监控仪表板页面 (`pages/tieringDashboard/index`) 提供了可视化的监控界面，包括：

1. 用户层级分布统计
2. 对话质量监控
3. 异常使用模式检测
4. 系统性能监控

## 回滚计划

如果部署后发现严重问题，可以执行以下回滚步骤：

1. 恢复用户层级：

```javascript
// 在云函数控制台执行
const cloud = require("wx-server-sdk");
cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });
const db = cloud.database();

// 将所有用户恢复到默认层级
db.collection("users").update({
  data: {
    level: 3,
    levelName: "挣扎层",
  },
});
```

2. 禁用层级特定提示：

修改 `geminiProxy` 云函数，将 `userLevel` 参数固定为 3（默认层级）。

## 常见问题

### Q: 部署后用户层级显示不正确

A: 执行以下步骤：

1. 检查 `user` 云函数是否正确部署
2. 确认用户数据库中 `level` 字段类型为数字
3. 尝试重新登录小程序

### Q: AI 回复不符合层级特点

A: 执行以下步骤：

1. 检查 `aiPromptManager` 云函数是否正确部署
2. 确认 `ai_prompts` 集合中是否有对应层级的提示数据
3. 检查 `geminiProxy` 云函数中的层级提示集成逻辑

### Q: 监控仪表板无数据

A: 执行以下步骤：

1. 确认 `tieringAnalytics` 云函数是否正确部署
2. 检查相关数据库集合是否已创建
3. 执行 `initTieringAnalytics` 云函数初始化监控数据库

## 联系支持

如有部署问题，请联系技术支持团队。
