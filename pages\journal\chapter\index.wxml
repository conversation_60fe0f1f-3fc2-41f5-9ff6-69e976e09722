<view class="container">
  <!-- 背景层 -->
  <image 
    class="background-image" 
    src="cloud://cloudbase-8gji862jcfb501e7.636c-cloudbase-8gji862jcfb501e7-1365531166/images/homeBG.png" 
    mode="aspectFill"
  ></image>
  <view class="overlay"></view>

  <!-- 角色展示层 (用于视差滚动) -->
  <image 
    class="character-bg-image" 
    src="{{characterInfo.image}}" 
    mode="aspectFit"
    style="transform: translateY(-{{characterImageParallax}}px);"
  ></image>

  <!-- 内容层 -->
  <view class="content-wrapper">
    <!-- 自定义导航栏 -->
    <view class="custom-nav" style="height: {{menuButtonInfo.height}}px; padding-top: {{menuButtonInfo.top}}px;">
      <view class="nav-back" bindtap="navigateBack">
        <view class="back-icon"></view>
      </view>
      <user-points custom-class="nav-points" bind:pointstap="onPointsTap"></user-points>
    </view>

    <!-- 角色信息区 -->
    <view class="character-info-container">
      <view class="character-name">{{characterInfo.name}}</view>
      <view class="character-tagline">{{characterInfo.tagline}}</view>
      <view class="divider"></view>
      <view class="character-description">{{characterInfo.description}}</view>
    </view>

    <!-- 章节列表 -->
    <view class="chapter-list">
      <block wx:for="{{chaptersWithStatus}}" wx:key="id">
        <view class="chapter-card" data-chapter="{{item}}" bindtap="onChapterTap">
          <view class="chapter-index">
            {{item.id < 10 ? '0' + item.id : item.id}}
          </view>
          <view class="chapter-title {{item.status === 'locked' ? 'locked' : ''}}">
            {{item.title}}
          </view>
          
          <!-- 状态标识 -->
          <view class="status-tag">
            <block wx:if="{{item.status === 'free'}}">
              <view class="tag-free">免费阅读</view>
            </block>
            <block wx:if="{{item.status === 'unlocked'}}">
              <view class="tag-unlocked">已购买</view>
            </block>
            <block wx:if="{{item.status === 'locked'}}">
              <view class="tag-locked">
                <view class="light-dot-icon"></view> 
                <text>{{item.cost || 2}}</text>
              </view>
            </block>
          </view>
        </view>
      </block>
    </view>
  </view>

  <!-- 解锁弹窗 -->
  <view class="modal-mask" wx:if="{{showUnlockModal}}" bindtap="hideModal"></view>
  <view class="unlock-modal {{showUnlockModal ? 'show' : ''}}">
    <view class="modal-title">解锁新篇章</view>
    <view class="modal-content">
      是否消耗 
      <view class="light-dot-icon inline"></view> 
      <text class="highlight">2</text> 
      光点，继续阅读<text class="highlight">{{characterInfo.name}}</text>的故事？
    </view>
    <view class="modal-actions">
      <button class="btn-cancel" bindtap="hideModal">再想想</button>
      <button class="btn-confirm" bindtap="confirmUnlock">确认解锁</button>
    </view>
  </view>

</view>