/**
 * 系统监控器
 * 负责监控星座系统的运行状态和性能指标
 */

class SystemMonitor {
    constructor() {
        this.metrics = {
            system: {
                uptime: 0,
                memoryUsage: 0,
                cpuUsage: 0,
                networkLatency: 0
            },
            application: {
                activeUsers: 0,
                totalStars: 0,
                activeAnimations: 0,
                errorCount: 0,
                responseTime: 0
            },
            performance: {
                frameRate: 60,
                renderTime: 0,
                loadTime: 0,
                interactionDelay: 0
            }
        }

        this.alerts = []
        this.thresholds = {
            memoryUsage: 80, // 80%
            cpuUsage: 70,    // 70%
            frameRate: 30,   // 30fps
            responseTime: 2000, // 2秒
            errorRate: 5     // 5%
        }

        this.isMonitoring = false
        this.monitoringInterval = null
        this.startTime = Date.now()
    }

    /**
     * 初始化系统监控器
     */
    initialize() {
        console.log('初始化系统监控器...')
        
        // 开始监控
        this.startMonitoring()
        
        // 设置错误监听
        this.setupErrorHandling()
        
        return { success: true }
    }

    /**
     * 开始监控
     */
    startMonitoring() {
        if (this.isMonitoring) return

        this.isMonitoring = true
        
        // 每5秒收集一次指标
        this.monitoringInterval = setInterval(() => {
            this.collectMetrics()
            this.checkThresholds()
        }, 5000)

        console.log('系统监控已启动')
    }

    /**
     * 停止监控
     */
    stopMonitoring() {
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval)
            this.monitoringInterval = null
        }
        this.isMonitoring = false
        console.log('系统监控已停止')
    }

    /**
     * 收集系统指标
     */
    collectMetrics() {
        try {
            // 收集系统指标
            this.collectSystemMetrics()
            
            // 收集应用指标
            this.collectApplicationMetrics()
            
            // 收集性能指标
            this.collectPerformanceMetrics()
            
        } catch (error) {
            console.error('收集指标失败:', error)
            this.recordError('metrics_collection_failed', error.message)
        }
    }

    /**
     * 收集系统指标
     */
    collectSystemMetrics() {
        // 运行时间
        this.metrics.system.uptime = Date.now() - this.startTime

        // 内存使用情况
        if (wx.getPerformance && wx.getPerformance().memory) {
            const memory = wx.getPerformance().memory
            this.metrics.system.memoryUsage = memory.usedJSHeapSize || 0
        }

        // 网络延迟（模拟）
        this.measureNetworkLatency()
    }

    /**
     * 收集应用指标
     */
    collectApplicationMetrics() {
        // 活跃用户数（从本地存储获取）
        this.metrics.application.activeUsers = this.getActiveUserCount()
        
        // 星星总数
        this.metrics.application.totalStars = this.getTotalStarCount()
        
        // 活跃动画数
        this.metrics.application.activeAnimations = this.getActiveAnimationCount()
        
        // 响应时间
        this.metrics.application.responseTime = this.getAverageResponseTime()
    }

    /**
     * 收集性能指标
     */
    collectPerformanceMetrics() {
        // 帧率
        this.metrics.performance.frameRate = this.getCurrentFrameRate()
        
        // 渲染时间
        this.metrics.performance.renderTime = this.getAverageRenderTime()
        
        // 交互延迟
        this.metrics.performance.interactionDelay = this.getInteractionDelay()
    }

    /**
     * 测量网络延迟
     */
    async measureNetworkLatency() {
        try {
            const startTime = Date.now()
            
            // 发送ping请求到云函数
            await wx.cloud.callFunction({
                name: 'ping',
                data: { timestamp: startTime }
            })
            
            const endTime = Date.now()
            this.metrics.system.networkLatency = endTime - startTime
            
        } catch (error) {
            this.metrics.system.networkLatency = -1 // 表示网络不可用
        }
    }

    /**
     * 获取活跃用户数
     */
    getActiveUserCount() {
        // 从本地存储或全局状态获取
        return 1 // 当前用户
    }

    /**
     * 获取星星总数
     */
    getTotalStarCount() {
        try {
            const starRecords = wx.getStorageSync('starRecords') || []
            return starRecords.length
        } catch (error) {
            return 0
        }
    }

    /**
     * 获取活跃动画数
     */
    getActiveAnimationCount() {
        // 这里应该从动画管理器获取实际数量
        return 0
    }

    /**
     * 获取平均响应时间
     */
    getAverageResponseTime() {
        // 从性能记录中计算平均响应时间
        return 500 // 模拟值
    }

    /**
     * 获取当前帧率
     */
    getCurrentFrameRate() {
        // 简化的帧率计算
        return 60 // 模拟值
    }

    /**
     * 获取平均渲染时间
     */
    getAverageRenderTime() {
        return 16 // 模拟值，16ms对应60fps
    }

    /**
     * 获取交互延迟
     */
    getInteractionDelay() {
        return 50 // 模拟值，50ms
    }

    /**
     * 检查阈值并生成告警
     */
    checkThresholds() {
        const alerts = []

        // 检查内存使用
        const memoryUsagePercent = (this.metrics.system.memoryUsage / (100 * 1024 * 1024)) * 100
        if (memoryUsagePercent > this.thresholds.memoryUsage) {
            alerts.push({
                type: 'memory_high',
                level: 'warning',
                message: `内存使用率过高: ${memoryUsagePercent.toFixed(1)}%`,
                value: memoryUsagePercent,
                threshold: this.thresholds.memoryUsage,
                timestamp: Date.now()
            })
        }

        // 检查帧率
        if (this.metrics.performance.frameRate < this.thresholds.frameRate) {
            alerts.push({
                type: 'fps_low',
                level: 'warning',
                message: `帧率过低: ${this.metrics.performance.frameRate}fps`,
                value: this.metrics.performance.frameRate,
                threshold: this.thresholds.frameRate,
                timestamp: Date.now()
            })
        }

        // 检查响应时间
        if (this.metrics.application.responseTime > this.thresholds.responseTime) {
            alerts.push({
                type: 'response_slow',
                level: 'warning',
                message: `响应时间过长: ${this.metrics.application.responseTime}ms`,
                value: this.metrics.application.responseTime,
                threshold: this.thresholds.responseTime,
                timestamp: Date.now()
            })
        }

        // 检查网络延迟
        if (this.metrics.system.networkLatency > 1000) {
            alerts.push({
                type: 'network_slow',
                level: 'warning',
                message: `网络延迟过高: ${this.metrics.system.networkLatency}ms`,
                value: this.metrics.system.networkLatency,
                threshold: 1000,
                timestamp: Date.now()
            })
        }

        // 添加新告警
        alerts.forEach(alert => this.addAlert(alert))
    }

    /**
     * 添加告警
     */
    addAlert(alert) {
        // 检查是否已存在相同类型的告警
        const existingAlert = this.alerts.find(a => a.type === alert.type && a.level === alert.level)
        
        if (!existingAlert) {
            this.alerts.push(alert)
            console.warn(`系统告警: ${alert.message}`)
            
            // 限制告警数量
            if (this.alerts.length > 50) {
                this.alerts = this.alerts.slice(-50)
            }
        }
    }

    /**
     * 记录错误
     */
    recordError(type, message, details = {}) {
        this.metrics.application.errorCount++
        
        const error = {
            type,
            message,
            details,
            timestamp: Date.now()
        }

        // 添加错误告警
        this.addAlert({
            type: 'error_occurred',
            level: 'error',
            message: `系统错误: ${message}`,
            error,
            timestamp: Date.now()
        })

        console.error('系统错误记录:', error)
    }

    /**
     * 设置错误处理
     */
    setupErrorHandling() {
        // 监听全局错误
        wx.onError && wx.onError((error) => {
            this.recordError('global_error', error)
        })

        // 监听未处理的Promise拒绝
        wx.onUnhandledRejection && wx.onUnhandledRejection((res) => {
            this.recordError('unhandled_rejection', res.reason)
        })
    }

    /**
     * 获取系统健康状态
     */
    getSystemHealth() {
        const now = Date.now()
        const recentAlerts = this.alerts.filter(alert => now - alert.timestamp < 5 * 60 * 1000) // 5分钟内

        let healthScore = 100
        let status = 'healthy'

        // 根据告警数量和严重程度计算健康分数
        recentAlerts.forEach(alert => {
            if (alert.level === 'error') {
                healthScore -= 20
            } else if (alert.level === 'warning') {
                healthScore -= 10
            }
        })

        healthScore = Math.max(0, healthScore)

        if (healthScore >= 80) {
            status = 'healthy'
        } else if (healthScore >= 60) {
            status = 'warning'
        } else {
            status = 'critical'
        }

        return {
            score: healthScore,
            status,
            recentAlerts: recentAlerts.length,
            uptime: this.metrics.system.uptime,
            lastCheck: now
        }
    }

    /**
     * 获取性能报告
     */
    getPerformanceReport() {
        return {
            metrics: { ...this.metrics },
            health: this.getSystemHealth(),
            alerts: this.alerts.slice(-10), // 最近10个告警
            recommendations: this.generateRecommendations()
        }
    }

    /**
     * 生成性能建议
     */
    generateRecommendations() {
        const recommendations = []
        const { system, application, performance } = this.metrics

        // 内存建议
        const memoryUsagePercent = (system.memoryUsage / (100 * 1024 * 1024)) * 100
        if (memoryUsagePercent > 60) {
            recommendations.push({
                type: 'memory',
                priority: 'medium',
                message: '内存使用率较高，建议启用对象池和垃圾回收优化'
            })
        }

        // 性能建议
        if (performance.frameRate < 45) {
            recommendations.push({
                type: 'performance',
                priority: 'high',
                message: '帧率较低，建议减少动画数量或启用性能优化模式'
            })
        }

        // 网络建议
        if (system.networkLatency > 500) {
            recommendations.push({
                type: 'network',
                priority: 'medium',
                message: '网络延迟较高，建议启用本地缓存和离线模式'
            })
        }

        // 错误建议
        if (application.errorCount > 10) {
            recommendations.push({
                type: 'stability',
                priority: 'high',
                message: '错误数量较多，建议检查错误日志并修复问题'
            })
        }

        return recommendations
    }

    /**
     * 导出监控数据
     */
    exportMonitoringData() {
        return {
            timestamp: Date.now(),
            metrics: this.metrics,
            alerts: this.alerts,
            health: this.getSystemHealth(),
            uptime: this.metrics.system.uptime,
            version: '1.0.0'
        }
    }

    /**
     * 设置自定义阈值
     */
    setThresholds(newThresholds) {
        Object.assign(this.thresholds, newThresholds)
        console.log('监控阈值已更新:', this.thresholds)
    }

    /**
     * 清除历史告警
     */
    clearAlerts(olderThan = 24 * 60 * 60 * 1000) { // 默认24小时
        const cutoffTime = Date.now() - olderThan
        const originalCount = this.alerts.length
        
        this.alerts = this.alerts.filter(alert => alert.timestamp > cutoffTime)
        
        const clearedCount = originalCount - this.alerts.length
        if (clearedCount > 0) {
            console.log(`清除了 ${clearedCount} 个历史告警`)
        }
        
        return clearedCount
    }

    /**
     * 销毁监控器
     */
    destroy() {
        this.stopMonitoring()
        this.alerts = []
        console.log('系统监控器已销毁')
    }
}

// 创建全局实例
const systemMonitor = new SystemMonitor()

export default systemMonitor
