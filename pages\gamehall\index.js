const app = getApp();

Page({
    data: {
        // 导航栏在滚动时的透明度
        navOpacity: 0,
        // 用户信息示例
        userInfo: {
            nickname: '探索者',
            lightPoints: 100
        },
        // 游戏列表数据
        gameList: [
            {
                id: 'memory-train',
                title: '列车上的陌生人',
                slogan: '一趟没有终点的午夜列车，一个与你一模一样的陌生人。\n你失去的，究竟是记忆，还是你自己？',
                tags: ['悬疑探索', '心理博弈', '自我发现'],
                duration: '8-12分钟',
                cost: 5,
                coverImage: '/game_cover_train.png'
            }
        ]
    },

    // --- 1. 生命周期函数 ---

    onLoad(options) {
        console.log("游戏厅页面加载 (V3.11 增加返回键修复版)");
    },

    // --- 2. 页面交互逻辑 ---

    // 【新增】返回上一页的函数
    navigateBack() {
        wx.navigateBack({
            delta: 1
        });
    },

    // 页面滚动事件，用于控制顶部导航栏的背景渐变
    onPageScroll(e) {
        const scrollTop = e.scrollTop;
        const navBarHeight = 80;
        let opacity = scrollTop / navBarHeight;
        if (opacity > 1) opacity = 1;

        if (opacity !== this.data.navOpacity) {
            this.setData({
                navOpacity: opacity
            });
        }
    },

    // 点击游戏卡片，触发进入游戏流程
    navigateToGame(event) {
        const selectedGame = event.currentTarget.dataset.game;
        const userPoints = this.data.userInfo.lightPoints;

        if (userPoints >= selectedGame.cost) {
            wx.showModal({
                title: '开启旅程',
                content: `本次体验将消耗您 ${selectedGame.cost} 光点，是否继续？`,
                confirmText: '确认出发',
                cancelText: '再想想',
                confirmColor: '#FFD700',
                cancelColor: '#AAAAAA',
                success: (res) => {
                    if (res.confirm) {
                        console.log(`用户确认，准备进入游戏: ${selectedGame.id}`);
                        const newPoints = userPoints - selectedGame.cost;
                        this.setData({ 'userInfo.lightPoints': newPoints });
                        wx.navigateTo({
                            url: `/pages/gamehall/${selectedGame.id}/index?title=${selectedGame.title}`
                        });
                    }
                }
            });
        } else {
            wx.showToast({
                title: '光点不足，先去别处转转吧',
                icon: 'none',
                duration: 2000
            });
        }
    }
});