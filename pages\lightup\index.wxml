<view class="container">
  <image class="background" src="cloud://cloudbase-8gji862jcfb501e7.636c-cloudbase-8gji862jcfb501e7-1365531166/images/background.jpg" mode="aspectFill" />

  <!-- ✅ 音量按钮（右上角） -->
  <view class="volume-btn {{isMuted ? 'muted' : ''}}" bindtap="toggleMute">
    <view class="volume-icon"></view>
  </view>

  <!-- 开始体验按钮 -->
  <view class="start-button" wx:if="{{showStartButton}}" bindtap="onStartExperience">
    <text>开始体验</text>
  </view>

  <!-- 内容区域 -->
  <view class="content" wx:if="{{!showStartButton}}">
    <view wx:for="{{lines}}" wx:key="index" wx:if="{{visibleLines[index]}}" class="text-line">
      {{item}}
    </view>

    <view wx:if="{{showButtons}}" class="button-group">
      <button class="glass-button" bindtap="onBelieve">我愿意相信</button>
      <button class="glass-button" bindtap="onNotReady">我现在还不敢相信</button>
    </view>

    <view wx:if="{{responseLines.length > 0}}">
      <view wx:for="{{responseLines}}" wx:key="index" wx:if="{{visibleResponse[index]}}" class="text-line">
        {{item}}
      </view>
    </view>

    <view wx:if="{{showFinalButton}}" class="final-button">
      <button class="glass-button" bindtap="onNext">去看看你的微光吧</button>
    </view>
  </view>
</view>
