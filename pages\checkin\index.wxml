<!-- pages/checkin/index.wxml -->
<!-- 
  开发者注释：
  这是签到页面的最终版WXML结构。
  - 核心显示区域为`.stars-wrapper`，通过wx:for循环动态生成7个水晶图标。
  - 水晶的样式和动画效果由`.star-item`的class动态绑定控制。
  - 奖励弹窗`.reward-modal-mask`通过`wx:if`控制显示和隐藏。
-->
<view class="container {{isShaking ? 'screen-shake' : ''}}">
  <!-- 背景图 -->
  <image class="background-image" src="{{imagePaths.background}}" mode="aspectFill" />

  <!-- 顶部标题和关闭按钮 -->
  <view class="header">
    <view class="header-title">星辰之路</view>
    <view class="close-button" bindtap="closePage">×</view>
  </view>

  <!-- 水晶（星星）的布局区域 -->
  <view class="stars-wrapper">
    <block wx:for="{{starList}}" wx:key="day">
      <!-- 
        class绑定逻辑:
        - .special: 应用第七天大奖水晶的尺寸样式。
        - .breathing: 应用于当天待点亮水晶的“呼吸光晕”动画。
        - item.animationClass: 用于动态应用不同动画效果（如普通点亮'pop-out'或第七天大奖'grand-prize'）。
      -->
      <view 
        class="star-item {{item.isSpecial ? 'special' : ''}} {{item.isCurrent && !item.isLit ? 'breathing' : ''}} {{item.animationClass}}" 
        style="top: {{item.position.top}}; left: {{item.position.left}};"
      >
        <!-- 
          【V1.7 最终版核心修改】图片路径判断逻辑:
          - 新逻辑：如果水晶是特殊日(isSpecial: true)，则无论是否点亮，都始终显示大奖水晶(imagePaths.special)。
          - 否则 (非特殊日)，才根据是否点亮(isLit)来显示点亮(imagePaths.lit)或暗淡(imagePaths.dim)的水晶。
        -->
        <image src="{{item.isSpecial ? imagePaths.special : (item.isLit ? imagePaths.lit : imagePaths.dim)}}" mode="aspectFit"/>
      </view>
    </block>
  </view>

  <!-- 底部操作区 -->
  <view class="action-area">
    <view class="action-title">已连续签到 {{continuousDays}} 天，连续7天开启星辰大宝箱</view>
    <button class="checkin-button" bindtap="handleCheckin" disabled="{{isButtonDisabled}}">
      {{buttonText}}
    </button>
  </view>

  <!-- 奖励弹窗 -->
  <view class="reward-modal-mask" wx:if="{{showRewardModal}}" catchtouchmove="preventTouchMove">
    <view class="reward-modal-content swing-in-top-fwd">
      <view class="reward-title">签到成功</view>
      <view class="reward-message">{{rewardMessage}}</view>
      <button class="reward-confirm-button" bindtap="closeRewardModal">真开心！</button>
    </view>
  </view>
</view>
