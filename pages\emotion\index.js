Page({
  data: {
    questions: [
      {
        lines: [
          "如果把你今天的情绪煮成一道菜，",
          "你会是哪一道？"
        ],
        options: [
          "A. 冒泡的辣火锅（心里有点炸锅了，藏了很多情绪，闷在那没说出来。）",
          "B. 化掉的冰淇淋（本来想让自己轻松一点，可是整个人软塌塌的，提不起劲。）",
          "C. 清清淡淡的白粥（没啥起伏，也没啥特别的情绪，就是平平淡淡的一天。）",
          "D. 刚出锅的煎饺（有点累、有点烦，但还咬着牙撑着，希望自己能继续努力。）",
          "E. 撒了糖的草莓（心情意外地轻松，好像今天没那么难熬，有点甜。）"
        ]
      },
      {
        lines: [
          "如果现在有一支温度计，可以测出你心里的温度，",
          "你觉得它大概是多少度？"
        ],
        options: [
          "A. -10°C —— 冷得发抖，像冻在一个角落里。",
          "B. 0°C —— 心像停住了，没有温度，也没有方向。",
          "C. 10°C —— 风吹着，有点冷，也有点空。",
          "D. 20°C —— 有点温和，但还是怕风吹到。",
          "E. 25°C —— 被阳光晒了一下，暖暖的，心软软的。"
        ]
      },
      {
        lines: [
          "如果现在有一个人能安静地陪你一会儿，",
          "你最希望 TA 陪你做点什么？"
        ],
        options: [
          "A. 坐下来听我说话，不评判、不打断，只是认真听我说完。",
          "B. 不说话，陪我发一会呆、躺着、看天、发愣也好。",
          "C. 带我去外面走走，兜一圈街也好、吹吹风也好。",
          "D. 抱一下我，哪怕什么都不说，就只抱一下就好。",
          "E. 陪我待着，干嘛都行——一起吃个饭、刷个剧、打个游戏也可以。"
        ]
      }
    ],
    step: 0,
    currentLines: [],
    currentOptions: [],
    visibleLines: [],
    showOptions: false,
    userAnswers: []
  },

  onLoad() {
    this.showQuestion(0);
  },

  showQuestion(step) {
    const question = this.data.questions[step];
    const visible = Array(question.lines.length).fill(false);

    this.setData({
      step,
      currentLines: question.lines,
      currentOptions: question.options,
      visibleLines: visible,
      showOptions: false
    });

    question.lines.forEach((_, i) => {
      setTimeout(() => {
        const updated = [...this.data.visibleLines];
        updated[i] = true;
        this.setData({ visibleLines: updated });
      }, i * 3000);
    });

    setTimeout(() => {
      this.setData({ showOptions: true });
    }, question.lines.length * 3000 + 2000);
  },

  onSelect(e) {
    const selected = this.data.currentOptions[e.currentTarget.dataset.index];
    const updatedAnswers = [...this.data.userAnswers, selected];

    this.setData({ userAnswers: updatedAnswers });

    // ✅ 存储到本地缓存
    wx.setStorageSync('userAnswers', updatedAnswers);

    // ✅ 存入全局变量（前提是 app.js 中定义了 globalData）
    if (getApp().globalData) {
      getApp().globalData.userAnswers = updatedAnswers;
    }

    if (this.data.step < this.data.questions.length - 1) {
      this.showQuestion(this.data.step + 1);
    } else {
      wx.navigateTo({
        url: '/pages/naming/index' // ✅ 替换为你命名页的实际路径
      });
    }
  }
});
