/**
 * 数据同步管理器
 * 负责星图数据与三问记录之间的同步和一致性维护
 */

class DataSyncManager {
    constructor() {
        this.syncQueue = []
        this.isSyncing = false
        this.syncRetryCount = 3
        this.syncRetryDelay = 1000
    }

    /**
     * 初始化数据同步管理器
     */
    async initialize() {
        try {
            console.log('初始化数据同步管理器...')
            
            // 检查本地缓存版本
            await this.checkCacheVersion()
            
            // 启动同步队列处理
            this.startSyncQueueProcessor()
            
            console.log('数据同步管理器初始化完成')
            return { success: true }
        } catch (error) {
            console.error('数据同步管理器初始化失败:', error)
            return { success: false, error: error.message }
        }
    }

    /**
     * 检查缓存版本（测试阶段简化版）
     */
    async checkCacheVersion() {
        try {
            const currentVersion = wx.getStorageSync('constellation_data_version') || '1.0.0'

            // 测试阶段直接设置为当前版本，不进行复杂迁移
            wx.setStorageSync('constellation_data_version', '1.0.0')

            console.log('缓存版本检查完成，当前版本: 1.0.0')
        } catch (error) {
            console.error('检查缓存版本失败:', error)
        }
    }

    /**
     * 同步星图数据到云端
     */
    async syncStarMapToCloud(starData) {
        return new Promise((resolve) => {
            this.addToSyncQueue({
                type: 'star_map_sync',
                data: starData,
                timestamp: Date.now(),
                resolve,
                retryCount: 0
            })
        })
    }

    /**
     * 同步三问记录到本地
     */
    async syncThreeQuestionsToLocal(recordData) {
        try {
            // 更新本地星图数据
            const localStarRecords = wx.getStorageSync('starRecords') || []
            
            // 查找是否已存在该记录
            const existingIndex = localStarRecords.findIndex(
                record => record.id === recordData.id
            )

            if (existingIndex >= 0) {
                // 更新现有记录
                localStarRecords[existingIndex] = this.convertRecordToStarData(recordData)
            } else {
                // 添加新记录
                localStarRecords.push(this.convertRecordToStarData(recordData))
            }

            // 保存到本地存储
            wx.setStorageSync('starRecords', localStarRecords)
            wx.setStorageSync('last_sync_time', Date.now())

            return { success: true }
        } catch (error) {
            console.error('同步三问记录到本地失败:', error)
            return { success: false, error: error.message }
        }
    }

    /**
     * 将三问记录转换为星图数据格式
     */
    convertRecordToStarData(recordData) {
        return {
            id: recordData._id || recordData.id,
            theme: recordData.theme || '未知主题',
            summary: recordData.summary || '',
            emotionKeyword: recordData.emotionKeyword || recordData.starKeyword || '',
            dialogueContent: recordData.dialogueContent || [],
            questions: recordData.questions || [],
            answers: recordData.answers || [],
            tomorrowMessage: recordData.tomorrowMessage || '',
            createTime: recordData.createTime || recordData.completedTime,
            
            // 星座位置信息
            starStyle: this.convertPositionToStyle(recordData.starPosition),
            constellationPosition: recordData.starPosition,
            
            // 迁移信息
            migrationInfo: recordData.migrationInfo || null
        }
    }

    /**
     * 将星座位置转换为样式格式
     */
    convertPositionToStyle(starPosition) {
        if (!starPosition) {
            // 如果没有位置信息，生成默认样式
            return {
                left: 50,
                top: 50,
                size: 1.0,
                brightness: 1.0,
                animationDelay: 0
            }
        }

        return {
            left: starPosition.x * 100,
            top: starPosition.y * 100,
            size: starPosition.size || 1.0,
            brightness: starPosition.brightness || 1.0,
            animationDelay: starPosition.animationDelay || 0
        }
    }

    /**
     * 添加任务到同步队列
     */
    addToSyncQueue(task) {
        this.syncQueue.push(task)
        
        if (!this.isSyncing) {
            this.processSyncQueue()
        }
    }

    /**
     * 启动同步队列处理器
     */
    startSyncQueueProcessor() {
        // 定期处理同步队列
        setInterval(() => {
            if (this.syncQueue.length > 0 && !this.isSyncing) {
                this.processSyncQueue()
            }
        }, 5000) // 每5秒检查一次
    }

    /**
     * 处理同步队列
     */
    async processSyncQueue() {
        if (this.isSyncing || this.syncQueue.length === 0) {
            return
        }

        this.isSyncing = true

        try {
            while (this.syncQueue.length > 0) {
                const task = this.syncQueue.shift()
                await this.processSyncTask(task)
            }
        } catch (error) {
            console.error('处理同步队列失败:', error)
        } finally {
            this.isSyncing = false
        }
    }

    /**
     * 处理单个同步任务
     */
    async processSyncTask(task) {
        try {
            let result = null

            switch (task.type) {
                case 'star_map_sync':
                    result = await this.performStarMapSync(task.data)
                    break
                
                case 'position_update':
                    result = await this.performPositionUpdate(task.data)
                    break
                
                default:
                    console.warn('未知的同步任务类型:', task.type)
                    result = { success: false, error: '未知任务类型' }
            }

            if (result.success) {
                task.resolve(result)
            } else {
                await this.handleSyncFailure(task, result.error)
            }

        } catch (error) {
            console.error('处理同步任务失败:', error)
            await this.handleSyncFailure(task, error.message)
        }
    }

    /**
     * 执行星图同步
     */
    async performStarMapSync(starData) {
        try {
            const result = await wx.cloud.callFunction({
                name: 'updateStarPosition',
                data: {
                    recordId: starData.id,
                    starPosition: starData.constellationPosition,
                    operation: 'update'
                }
            })

            if (result.result && result.result.success) {
                console.log('星图数据同步成功:', starData.id)
                return { success: true, result: result.result }
            } else {
                throw new Error(result.result?.error || '同步失败')
            }

        } catch (error) {
            console.error('星图同步失败:', error)
            return { success: false, error: error.message }
        }
    }

    /**
     * 执行位置更新
     */
    async performPositionUpdate(updateData) {
        try {
            const result = await wx.cloud.callFunction({
                name: 'updateStarPosition',
                data: updateData
            })

            if (result.result && result.result.success) {
                console.log('位置更新成功:', updateData.recordId)
                return { success: true, result: result.result }
            } else {
                throw new Error(result.result?.error || '位置更新失败')
            }

        } catch (error) {
            console.error('位置更新失败:', error)
            return { success: false, error: error.message }
        }
    }

    /**
     * 处理同步失败
     */
    async handleSyncFailure(task, error) {
        task.retryCount = (task.retryCount || 0) + 1

        if (task.retryCount < this.syncRetryCount) {
            console.log(`同步任务重试 ${task.retryCount}/${this.syncRetryCount}:`, task.type)
            
            // 延迟后重新加入队列
            setTimeout(() => {
                this.syncQueue.unshift(task)
            }, this.syncRetryDelay * task.retryCount)
        } else {
            console.error('同步任务最终失败:', task.type, error)
            task.resolve({ success: false, error })
        }
    }

    /**
     * 强制全量同步
     */
    async forceFullSync() {
        try {
            console.log('开始强制全量同步...')

            // 从云端获取最新数据
            const result = await wx.cloud.callFunction({
                name: 'getUserHistory',
                data: { includeStarPosition: true }
            })

            if (result.result && result.result.success) {
                const records = result.result.history || []
                
                // 转换并保存到本地
                const starRecords = records.map(record => 
                    this.convertRecordToStarData(record)
                )

                wx.setStorageSync('starRecords', starRecords)
                wx.setStorageSync('last_full_sync_time', Date.now())
                wx.removeStorageSync('need_full_sync')

                console.log(`全量同步完成，同步了 ${starRecords.length} 条记录`)
                return { success: true, syncedCount: starRecords.length }
            } else {
                throw new Error(result.result?.error || '获取云端数据失败')
            }

        } catch (error) {
            console.error('强制全量同步失败:', error)
            return { success: false, error: error.message }
        }
    }

    /**
     * 检查是否需要同步
     */
    shouldSync() {
        const needFullSync = wx.getStorageSync('need_full_sync')
        const lastSyncTime = wx.getStorageSync('last_sync_time') || 0
        const syncInterval = 30 * 60 * 1000 // 30分钟

        return needFullSync || (Date.now() - lastSyncTime > syncInterval)
    }

    /**
     * 获取同步状态
     */
    getSyncStatus() {
        return {
            isSyncing: this.isSyncing,
            queueLength: this.syncQueue.length,
            lastSyncTime: wx.getStorageSync('last_sync_time') || 0,
            needFullSync: wx.getStorageSync('need_full_sync') || false
        }
    }
}

// 创建全局实例
const dataSyncManager = new DataSyncManager()

export default dataSyncManager
