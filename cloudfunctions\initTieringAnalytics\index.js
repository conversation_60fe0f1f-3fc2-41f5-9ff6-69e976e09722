// 初始化层级系统监控和分析数据库
const cloud = require('wx-server-sdk');

cloud.init({
    env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
    try {
        // 创建对话指标集合
        await createDialogueMetricsCollection();

        // 创建层级切换日志集合
        await createLevelSwitchLogsCollection();

        // 创建系统性能集合
        await createSystemPerformanceCollection();

        // 创建三问仪式指标集合
        await createThreeQuestionsMetricsCollection();

        // 创建用户消息集合
        await createUserMessagesCollection();

        // 创建初始系统性能记录
        await createInitialPerformanceRecord();

        return {
            success: true,
            message: '层级系统监控和分析数据库初始化成功'
        };
    } catch (error) {
        console.error('初始化层级系统监控和分析数据库失败:', error);
        return {
            success: false,
            error: error.message
        };
    }
};

// 创建对话指标集合
async function createDialogueMetricsCollection() {
    try {
        // 检查集合是否存在
        const collections = await db.listCollections().get();
        const collectionNames = collections.data.map(collection => collection.name);

        if (!collectionNames.includes('dialogue_metrics')) {
            await db.createCollection('dialogue_metrics');
            console.log('创建dialogue_metrics集合成功');

            // 创建索引
            await db.collection('dialogue_metrics').createIndex({
                keys: {
                    _openid: 1
                },
                options: {
                    name: 'openid_index'
                }
            });

            await db.collection('dialogue_metrics').createIndex({
                keys: {
                    userLevel: 1
                },
                options: {
                    name: 'level_index'
                }
            });

            await db.collection('dialogue_metrics').createIndex({
                keys: {
                    createTime: -1
                },
                options: {
                    name: 'time_index'
                }
            });

            console.log('dialogue_metrics索引创建成功');
        } else {
            console.log('dialogue_metrics集合已存在');
        }

        return true;
    } catch (error) {
        console.error('创建dialogue_metrics集合失败:', error);
        throw error;
    }
}

// 创建层级切换日志集合
async function createLevelSwitchLogsCollection() {
    try {
        // 检查集合是否存在
        const collections = await db.listCollections().get();
        const collectionNames = collections.data.map(collection => collection.name);

        if (!collectionNames.includes('level_switch_logs')) {
            await db.createCollection('level_switch_logs');
            console.log('创建level_switch_logs集合成功');

            // 创建索引
            await db.collection('level_switch_logs').createIndex({
                keys: {
                    _openid: 1
                },
                options: {
                    name: 'openid_index'
                }
            });

            await db.collection('level_switch_logs').createIndex({
                keys: {
                    fromLevel: 1,
                    toLevel: 1
                },
                options: {
                    name: 'level_transition_index'
                }
            });

            await db.collection('level_switch_logs').createIndex({
                keys: {
                    createTime: -1
                },
                options: {
                    name: 'time_index'
                }
            });

            console.log('level_switch_logs索引创建成功');
        } else {
            console.log('level_switch_logs集合已存在');
        }

        return true;
    } catch (error) {
        console.error('创建level_switch_logs集合失败:', error);
        throw error;
    }
}

// 创建系统性能集合
async function createSystemPerformanceCollection() {
    try {
        // 检查集合是否存在
        const collections = await db.listCollections().get();
        const collectionNames = collections.data.map(collection => collection.name);

        if (!collectionNames.includes('system_performance')) {
            await db.createCollection('system_performance');
            console.log('创建system_performance集合成功');

            // 创建索引
            await db.collection('system_performance').createIndex({
                keys: {
                    timestamp: -1
                },
                options: {
                    name: 'time_index'
                }
            });

            console.log('system_performance索引创建成功');
        } else {
            console.log('system_performance集合已存在');
        }

        return true;
    } catch (error) {
        console.error('创建system_performance集合失败:', error);
        throw error;
    }
}

// 创建初始系统性能记录
async function createInitialPerformanceRecord() {
    try {
        // 检查是否已有记录
        const existingRecords = await db.collection('system_performance').count();

        if (existingRecords.total === 0) {
            // 创建初始记录
            await db.collection('system_performance').add({
                data: {
                    timestamp: new Date(),
                    responseTime: 500, // 初始平均响应时间（毫秒）
                    requestCount: 0,   // 请求数量
                    errorCount: 0,     // 错误数量
                    uptime: 100,       // 系统正常运行时间百分比
                    cpuUsage: 10,      // CPU使用率百分比
                    memoryUsage: 20,   // 内存使用率百分比
                    notes: '系统初始化记录'
                }
            });

            console.log('创建初始系统性能记录成功');
        } else {
            console.log('系统性能记录已存在，跳过初始化');
        }

        return true;
    } catch (error) {
        console.error('创建初始系统性能记录失败:', error);
        throw error;
    }
}

// 创建三问仪式指标集合
async function createThreeQuestionsMetricsCollection() {
    try {
        // 检查集合是否存在
        const collections = await db.listCollections().get();
        const collectionNames = collections.data.map(collection => collection.name);

        if (!collectionNames.includes('three_questions_metrics')) {
            await db.createCollection('three_questions_metrics');
            console.log('创建three_questions_metrics集合成功');

            // 创建索引
            await db.collection('three_questions_metrics').createIndex({
                keys: {
                    _openid: 1
                },
                options: {
                    name: 'openid_index'
                }
            });

            await db.collection('three_questions_metrics').createIndex({
                keys: {
                    userLevel: 1
                },
                options: {
                    name: 'level_index'
                }
            });

            await db.collection('three_questions_metrics').createIndex({
                keys: {
                    createTime: -1
                },
                options: {
                    name: 'time_index'
                }
            });

            await db.collection('three_questions_metrics').createIndex({
                keys: {
                    completed: 1
                },
                options: {
                    name: 'completed_index'
                }
            });

            await db.collection('three_questions_metrics').createIndex({
                keys: {
                    requestedDiary: 1
                },
                options: {
                    name: 'diary_request_index'
                }
            });

            console.log('three_questions_metrics索引创建成功');
        } else {
            console.log('three_questions_metrics集合已存在');
        }

        return true;
    } catch (error) {
        console.error('创建three_questions_metrics集合失败:', error);
        throw error;
    }
}

// 创建用户消息集合
async function createUserMessagesCollection() {
    try {
        // 检查集合是否存在
        const collections = await db.listCollections().get();
        const collectionNames = collections.data.map(collection => collection.name);

        if (!collectionNames.includes('user_messages')) {
            await db.createCollection('user_messages');
            console.log('创建user_messages集合成功');

            // 创建索引
            await db.collection('user_messages').createIndex({
                keys: {
                    openid: 1
                },
                options: {
                    name: 'openid_index'
                }
            });

            await db.collection('user_messages').createIndex({
                keys: {
                    type: 1
                },
                options: {
                    name: 'type_index'
                }
            });

            await db.collection('user_messages').createIndex({
                keys: {
                    createTime: -1
                },
                options: {
                    name: 'time_index'
                }
            });

            await db.collection('user_messages').createIndex({
                keys: {
                    isRead: 1
                },
                options: {
                    name: 'read_status_index'
                }
            });

            console.log('user_messages索引创建成功');
        } else {
            console.log('user_messages集合已存在');
        }

        return true;
    } catch (error) {
        console.error('创建user_messages集合失败:', error);
        throw error;
    }
}