<view class="container" bindtap="toggleNav">
  <!-- 背景 -->
  <image 
    class="background-image" 
    src="cloud://cloudbase-8gji862jcfb501e7.636c-cloudbase-8gji862jcfb501e7-1365531166/images/homeBG.png" 
    mode="aspectFill"
  ></image>
  <view class="overlay"></view>

  <!-- 顶部导航栏 (可显隐) -->
  <view class="custom-nav {{navVisible ? 'visible' : ''}}" style="height: {{menuButtonInfo.height}}px; padding-top: {{menuButtonInfo.top}}px;" catchtap="noop">
    <view class="nav-back" catchtap="navigateBack">
      <view class="back-icon"></view>
    </view>
    <view class="nav-title">{{story.title}}</view>
  </view>

  <!-- 故事内容区 -->
  <scroll-view class="story-scroll" scroll-y>
    <view class="story-content-card">
      <view class="story-title">{{story.title}}</view>
      <view class="story-divider"></view>
      <view class="story-text">
        <block wx:for="{{story.paragraphs}}" wx:key="index">
          <text class="paragraph" user-select>{{item}}</text>
        </block>
      </view>
      
      <!-- 章节切换 -->
      <view class="chapter-nav">
        <view class="nav-btn prev {{isFirstChapter ? 'disabled' : ''}}" catchtap="prevChapter">« 上一章</view>
        <view class="nav-btn next {{isLastChapter ? 'disabled' : ''}}" catchtap="nextChapter">下一章 »</view>
      </view>
    </view>
  </scroll-view>

  <!-- 底部音频播放器 -->
  <view class="audio-player" catchtap="noop">
    <view class="play-button {{isPlaying ? 'playing' : ''}}" catchtap="togglePlay">
      <view class="icon-play"></view>
      <view class="icon-pause"></view>
    </view>
    <view class="progress-bar">
      <view class="time-current">{{currentTime}}</view>
      <view class="progress-track" catchtap="onProgressTap" catchtouchmove="onProgressMove" catchtouchstart="onProgressStart" catchtouchend="onProgressEnd">
        <view class="progress-fill" style="{{progressStyle}}"></view>
        <view class="progress-thumb" style="left: {{progress}}%"></view>
      </view>
      <view class="time-duration">{{duration}}</view>
    </view>
    <view class="timer-button" catchtap="toggleTimerSheet">
      <image class="timer-icon" src="cloud://cloudbase-8gji862jcfb501e7.636c-cloudbase-8gji862jcfb501e7-1365531166/images/icon_moon.png"></image>
    </view>
  </view>

  <!-- 定时器选择弹窗 -->
  <view class="action-sheet-mask" wx:if="{{showTimerSheet}}" bindtap="toggleTimerSheet"></view>
  <view class="action-sheet {{showTimerSheet ? 'show' : ''}}" catchtap="noop">
    <view class="sheet-item" data-type="15min" bindtap="setTimer">15分钟后停止</view>
    <view class="sheet-item" data-type="30min" bindtap="setTimer">30分钟后停止</view>
    <view class="sheet-item" data-type="chapter" bindtap="setTimer">本章播放完后停止</view>
    <view class="sheet-item cancel" data-type="off" bindtap="setTimer">关闭定时</view>
  </view>

</view>