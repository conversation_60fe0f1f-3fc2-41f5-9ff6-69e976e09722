<!-- 层级系统监控仪表板页面 -->
<view class="dashboard-container">
  <!-- 页面标题 -->
  <view class="header">
    <text class="title">层级系统监控仪表板</text>
    <view class="refresh-info" wx:if="{{refreshTime}}">
      <text class="refresh-time">更新时间: {{refreshTime}}</text>
      <button class="refresh-btn" size="mini" bindtap="onRefresh">刷新</button>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading}}">
    <text>数据加载中...</text>
  </view>

  <!-- 错误状态 -->
  <view class="error" wx:if="{{error}}">
    <text class="error-text">{{error}}</text>
    <button class="retry-btn" bindtap="onRefresh">重试</button>
  </view>

  <!-- 仪表板内容 -->
  <view class="dashboard-content" wx:if="{{!loading && !error && dashboardData}}">
    
    <!-- 概览卡片 -->
    <view class="overview-section">
      <text class="section-title">系统概览</text>
      <view class="overview-cards">
        <view class="overview-card">
          <text class="card-title">总用户数</text>
          <text class="card-value">{{dashboardData.overview.totalUsers}}</text>
        </view>
        <view class="overview-card">
          <text class="card-title">今日对话</text>
          <text class="card-value">{{dashboardData.overview.todayDialogues}}</text>
        </view>
        <view class="overview-card">
          <text class="card-title">今日切换</text>
          <text class="card-value">{{dashboardData.overview.todaySwitches}}</text>
        </view>
        <view class="overview-card {{dashboardData.overview.systemHealth === 'healthy' ? 'healthy' : 'warning'}}">
          <text class="card-title">系统状态</text>
          <text class="card-value">{{dashboardData.overview.systemHealth === 'healthy' ? '正常' : '警告'}}</text>
        </view>
      </view>
    </view>

    <!-- 用户层级分布 -->
    <view class="section">
      <view class="section-header">
        <text class="section-title">用户层级分布</text>
        <button class="detail-btn" size="mini" bindtap="viewLevelDistribution">详情</button>
      </view>
      <view class="level-distribution">
        <view class="level-item" wx:for="{{levelDistribution}}" wx:key="level">
          <view class="level-info">
            <text class="level-name">Level {{item.level}} - {{item.name}}</text>
            <text class="level-count">{{item.count}}人 ({{item.percentage}}%)</text>
          </view>
          <view class="level-bar">
            <view class="level-progress" style="width: {{item.percentage}}%"></view>
          </view>
        </view>
        <view class="no-data" wx:if="{{levelDistribution.length === 0}}">
          <text>暂无用户层级数据</text>
        </view>
      </view>
    </view>

    <!-- 对话质量指标 -->
    <view class="section">
      <text class="section-title">对话质量指标 (24小时)</text>
      <view class="quality-metrics">
        <view class="metric-item">
          <text class="metric-label">平均响应时间</text>
          <text class="metric-value">{{qualityMetrics.averageResponseTime || 0}}ms</text>
        </view>
        <view class="metric-item">
          <text class="metric-label">平均对话长度</text>
          <text class="metric-value">{{qualityMetrics.averageDialogueLength || 0}}字符</text>
        </view>
        <view class="metric-item">
          <text class="metric-label">用户满意度</text>
          <text class="metric-value">{{qualityMetrics.userSatisfactionScore || 0}}/5</text>
        </view>
        <view class="metric-item">
          <text class="metric-label">完成率</text>
          <text class="metric-value">{{qualityMetrics.completionRate || 0}}%</text>
        </view>
        <view class="metric-item">
          <text class="metric-label">错误率</text>
          <text class="metric-value">{{qualityMetrics.errorRate || 0}}%</text>
        </view>
      </view>
    </view>

    <!-- 异常检测 -->
    <view class="section">
      <view class="section-header">
        <text class="section-title">异常检测 (24小时)</text>
        <button class="detail-btn" size="mini" bindtap="viewAnomalies">详情</button>
      </view>
      <view class="anomaly-summary">
        <view class="anomaly-item high" wx:if="{{anomalies.high > 0}}">
          <text class="anomaly-label">高危异常</text>
          <text class="anomaly-count">{{anomalies.high}}</text>
        </view>
        <view class="anomaly-item medium" wx:if="{{anomalies.medium > 0}}">
          <text class="anomaly-label">中危异常</text>
          <text class="anomaly-count">{{anomalies.medium}}</text>
        </view>
        <view class="anomaly-item low" wx:if="{{anomalies.low > 0}}">
          <text class="anomaly-label">低危异常</text>
          <text class="anomaly-count">{{anomalies.low}}</text>
        </view>
        <view class="no-anomaly" wx:if="{{anomalies.total === 0}}">
          <text>✅ 暂无异常</text>
        </view>
      </view>
    </view>

    <!-- 系统性能 -->
    <view class="section">
      <view class="section-header">
        <text class="section-title">系统性能 (24小时)</text>
        <button class="detail-btn" size="mini" bindtap="viewPerformance">详情</button>
      </view>
      <view class="performance-metrics">
        <view class="perf-item">
          <text class="perf-label">平均响应时间</text>
          <text class="perf-value">{{performanceData.averageResponseTime || 0}}ms</text>
        </view>
        <view class="perf-item">
          <text class="perf-label">系统正常运行时间</text>
          <text class="perf-value">{{performanceData.systemUptime || 0}}%</text>
        </view>
        <view class="perf-item">
          <text class="perf-label">错误率</text>
          <text class="perf-value">{{performanceData.errorRate || 0}}%</text>
        </view>
        <view class="perf-item">
          <text class="perf-label">吞吐量</text>
          <text class="perf-value">{{performanceData.throughput || 0}}/min</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="actions">
      <button class="action-btn" bindtap="exportData">导出数据</button>
      <button class="action-btn" bindtap="settingsMonitor">监控设置</button>
    </view>
  </view>

  <!-- 空数据状态 -->
  <view class="empty-state" wx:if="{{!loading && !error && !dashboardData}}">
    <text>暂无监控数据</text>
    <button class="retry-btn" bindtap="onRefresh">重新加载</button>
  </view>
</view>