Page({
  data: {
    dialogueTheme: '',
    aiSummary: '',
    threeQuestions: [],
    threeAnswers: [],
    emotionWord: '',
    tomorrowMessage: ''
  },

  onLoad(options) {
    const { 
      theme, 
      summary, 
      emotionKeyword, 
      tomorrowMessage 
    } = options

    // 解码并设置数据
    this.setData({
      dialogueTheme: decodeURIComponent(theme),
      aiSummary: decodeURIComponent(summary),
      emotionWord: emotionKeyword ? decodeURIComponent(emotionKeyword) : '成长',
      tomorrowMessage: tomorrowMessage ? decodeURIComponent(tomorrowMessage) : '相信自己，每一天都是新的开始。'
    })

    // 从 localStorage 获取对话内容
    const dialogueContent = wx.getStorageSync('dialogueContent') || []
    
    // 清理 localStorage
    wx.removeStorageSync('dialogueContent')

    // 将对话内容存储在实例属性中，以便在 fetchDialogueSummary 后使用
    this.dialogueContent = dialogueContent

    this.fetchDialogueSummary()
  },

  fetchDialogueSummary() {
    wx.showLoading({ title: '生成总结...' })

    wx.cloud.callFunction({
      name: 'generateDialogueSummary',
      data: {
        theme: this.data.dialogueTheme,
        summary: this.data.aiSummary
      }



      
    }).then(res => {
      wx.hideLoading()
      
      if (res.result.success) {
        console.log('从 generateDialogueSummary 返回的三问和答案:', {
          threeQuestions: res.result.threeQuestions,
          threeAnswers: res.result.threeAnswers
        })
        this.setData({
          threeQuestions: res.result.threeQuestions || [],
          threeAnswers: res.result.threeAnswers || [],
          emotionWord: res.result.emotionWord || '成长',
          tomorrowMessage: res.result.tomorrowMessage || '相信自己，每一天都是新的开始。'
        })
        console.log('修改后的数据:', {
          theme: this.data.dialogueTheme,
          summary: this.data.aiSummary,
          emotionKeyword: this.data.emotionWord,
          tomorrowMessage: this.data.tomorrowMessage,
          dialogueContent: this.dialogueContent || [],
          updateMode: 'modify'
        })

        // 调用云函数保存三问记录
        return wx.cloud.callFunction({
          name: 'saveThreeQuestions',
          data: {
            theme: this.data.dialogueTheme,
            summary: this.data.aiSummary,
            questions: this.data.threeQuestions, // 新增：传递 questions
            answers: this.data.threeAnswers,     // 新增：传递 answers
            emotionKeyword: this.data.emotionWord,
            tomorrowMessage: this.data.tomorrowMessage,
            dialogueContent: this.dialogueContent || [],
            updateMode: 'modify'
          }
        })
      } else {
        wx.showToast({
          title: '获取总结失败',
          icon: 'none'
        })
        return Promise.reject(new Error('获取总结失败'))
      }
    }).then(saveRes => {
      console.log('保存三问记录结果:', saveRes)
    }).catch(err => {
      wx.hideLoading()
      console.error('获取或保存总结失败', err)
      wx.showToast({
        title: '获取总结失败',
        icon: 'none'
      })
    })
  },

  shareDialogue() {
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })
  },

  backToHome() {
    wx.redirectTo({
      url: '/pages/home/<USER>'
    });
  }
}) 