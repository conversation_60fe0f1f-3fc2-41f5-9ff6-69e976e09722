/* 数据迁移管理页面样式 */
.migration-admin {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 页面标题 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding: 20rpx 0;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.refresh-btn {
  display: flex;
  align-items: center;
  padding: 10rpx 20rpx;
  background-color: #007aff;
  color: white;
  border-radius: 10rpx;
  font-size: 28rpx;
}

.refresh-btn:disabled {
  background-color: #ccc;
}

/* 状态卡片 */
.status-card {
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.status-indicator {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: white;
}

.status-indicator.complete {
  background-color: #34c759;
}

.status-indicator.pending {
  background-color: #ff9500;
}

.sync-indicator.syncing {
  background-color: #007aff;
}

.sync-indicator.idle {
  background-color: #8e8e93;
}

/* 状态内容 */
.status-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.status-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.status-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.status-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.status-value.success {
  color: #34c759;
}

.status-value.warning {
  color: #ff9500;
}

/* 进度条 */
.progress-container {
  margin-top: 30rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.progress-bar {
  flex: 1;
  height: 12rpx;
  background-color: #e5e5ea;
  border-radius: 6rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #34c759;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 24rpx;
  color: #666;
  min-width: 80rpx;
  text-align: right;
}

/* 操作按钮 */
.action-section {
  margin-bottom: 30rpx;
}

.action-group {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.action-btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: bold;
  border: none;
}

.action-btn.primary {
  background-color: #007aff;
  color: white;
}

.action-btn.danger {
  background-color: #ff3b30;
  color: white;
}

.action-btn.secondary {
  background-color: #8e8e93;
  color: white;
}

.action-btn:disabled {
  background-color: #ccc !important;
  color: #999 !important;
}

/* 日志部分 */
.logs-section {
  background-color: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background-color: #f8f9fa;
  cursor: pointer;
}

.logs-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.toggle-icon {
  font-size: 24rpx;
  color: #666;
  transition: transform 0.3s ease;
}

.toggle-icon.expanded {
  transform: rotate(180deg);
}

.logs-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.logs-content.show {
  max-height: 1000rpx;
}

.empty-logs {
  padding: 60rpx;
  text-align: center;
  color: #999;
  font-size: 28rpx;
}

/* 日志项 */
.log-item {
  border-bottom: 1rpx solid #e5e5ea;
  padding: 30rpx;
}

.log-item:last-child {
  border-bottom: none;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.log-operation {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.log-status {
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
  font-size: 22rpx;
  color: white;
}

.log-status.success {
  background-color: #34c759;
}

.log-status.failed {
  background-color: #ff3b30;
}

.log-time {
  font-size: 24rpx;
  color: #666;
}

.log-details {
  background-color: #f8f9fa;
  padding: 20rpx;
  border-radius: 8rpx;
}

.log-content {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
  word-break: break-all;
}

/* 加载遮罩 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  color: white;
  font-size: 28rpx;
}
