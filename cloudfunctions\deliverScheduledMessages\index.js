const cloud = require('wx-server-sdk')

cloud.init({
    env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 指数退避延迟函数
function exponentialBackoffDelay(retryCount) {
    return Math.min(1000 * Math.pow(2, retryCount), 30000) // 最大30秒
}

// 发送消息通知（支持多种通知方式）
async function sendNotification(openid, message, messageData = {}) {
    const results = []
    let hasSuccess = false

    try {
        // 方式1: 保存到用户消息中心（主要方式）
        try {
            await db.collection('user_messages').add({
                data: {
                    openid,
                    type: 'tomorrow_message',
                    title: '来自昨天的你',
                    content: message,
                    isRead: false,
                    priority: 'normal',
                    sourceType: messageData.sourceType || 'tomorrow_message',
                    sourceId: messageData.sourceId || null,
                    createTime: db.serverDate()
                }
            })

            console.log(`消息已保存到用户 ${openid} 的消息中心`)
            results.push({ channel: 'message_center', success: true })
            hasSuccess = true

        } catch (error) {
            console.error('保存到消息中心失败:', error)
            results.push({ channel: 'message_center', success: false, error: error.message })
        }

        // 方式2: 发送订阅消息（如果用户已订阅）
        try {
            // 检查用户是否有有效的订阅
            const subscriptionResult = await checkUserSubscription(openid)

            if (subscriptionResult.hasValidSubscription) {
                const subscribeResult = await sendSubscribeMessage(openid, message, messageData)
                results.push({ channel: 'subscribe_message', success: subscribeResult.success, error: subscribeResult.error })

                if (subscribeResult.success) {
                    hasSuccess = true
                }
            } else {
                results.push({ channel: 'subscribe_message', success: false, error: 'No valid subscription' })
            }

        } catch (error) {
            console.error('发送订阅消息失败:', error)
            results.push({ channel: 'subscribe_message', success: false, error: error.message })
        }

        // 方式3: 小程序内推送（如果用户在线）
        try {
            const pushResult = await sendInAppPush(openid, message, messageData)
            results.push({ channel: 'in_app_push', success: pushResult.success, error: pushResult.error })

            if (pushResult.success) {
                hasSuccess = true
            }

        } catch (error) {
            console.error('发送应用内推送失败:', error)
            results.push({ channel: 'in_app_push', success: false, error: error.message })
        }

        return {
            success: hasSuccess,
            channels: results,
            primaryChannel: hasSuccess ? 'message_center' : 'none'
        }

    } catch (error) {
        console.error('发送通知失败:', error)
        return {
            success: false,
            error: error.message,
            channels: results
        }
    }
}

// 检查用户订阅状态
async function checkUserSubscription(openid) {
    try {
        // 这里可以查询用户的订阅记录
        // 暂时返回默认值，实际实现需要根据具体的订阅管理逻辑
        return {
            hasValidSubscription: false,
            subscriptionTypes: []
        }
    } catch (error) {
        console.error('检查用户订阅状态失败:', error)
        return {
            hasValidSubscription: false,
            subscriptionTypes: []
        }
    }
}

// 发送订阅消息
async function sendSubscribeMessage(openid, message, messageData) {
    try {
        // 这里实现订阅消息发送逻辑
        // 需要配置订阅消息模板

        // 示例代码（需要根据实际模板配置）
        /*
        const result = await cloud.openapi.subscribeMessage.send({
            touser: openid,
            template_id: 'YOUR_TEMPLATE_ID',
            page: 'pages/threeQuestions/index',
            data: {
                thing1: { value: '来自昨天的你' },
                thing2: { value: message.substring(0, 20) },
                time3: { value: new Date().toLocaleString() }
            }
        })
        */

        console.log(`订阅消息发送成功: ${openid}`)
        return { success: true }

    } catch (error) {
        console.error('发送订阅消息失败:', error)
        return { success: false, error: error.message }
    }
}

// 发送应用内推送
async function sendInAppPush(openid, message, messageData) {
    try {
        // 这里可以实现实时推送逻辑
        // 比如通过WebSocket或者其他实时通信方式

        // 暂时记录推送尝试
        console.log(`尝试发送应用内推送: ${openid}`)

        // 实际实现中可能需要检查用户是否在线
        return { success: false, error: 'In-app push not implemented' }

    } catch (error) {
        console.error('发送应用内推送失败:', error)
        return { success: false, error: error.message }
    }
}

// 处理单个消息的投递
async function deliverMessage(messageDoc) {
    const { _id, openid, message, retryCount, maxRetries, sourceType, sourceId } = messageDoc

    try {
        // 发送通知
        const result = await sendNotification(openid, message, { sourceType, sourceId })

        if (result.success) {
            // 投递成功，更新状态
            await db.collection('scheduled_messages').doc(_id).update({
                data: {
                    status: 'delivered',
                    deliveredTime: db.serverDate(),
                    updateTime: db.serverDate(),
                    deliveryChannels: result.channels,
                    primaryChannel: result.primaryChannel,
                    totalRetries: retryCount
                }
            })

            console.log(`消息 ${_id} 投递成功，使用渠道: ${result.primaryChannel}`)
            return { success: true, channels: result.channels }

        } else {
            // 投递失败，检查是否需要重试
            if (retryCount < maxRetries) {
                const newRetryCount = retryCount + 1
                const nextRetryTime = new Date(Date.now() + exponentialBackoffDelay(newRetryCount))

                await db.collection('scheduled_messages').doc(_id).update({
                    data: {
                        status: 'retry_pending',
                        retryCount: newRetryCount,
                        nextRetryTime: nextRetryTime,
                        lastError: result.error,
                        updateTime: db.serverDate()
                    }
                })

                console.log(`消息 ${_id} 投递失败，安排第 ${newRetryCount} 次重试，时间: ${nextRetryTime}`)
                return { success: false, willRetry: true }

            } else {
                // 超过最大重试次数，标记为失败
                await db.collection('scheduled_messages').doc(_id).update({
                    data: {
                        status: 'failed',
                        lastError: result.error,
                        updateTime: db.serverDate()
                    }
                })

                console.log(`消息 ${_id} 投递失败，已超过最大重试次数`)
                return { success: false, willRetry: false }
            }
        }

    } catch (error) {
        console.error(`处理消息 ${_id} 时发生异常:`, error)

        // 更新错误状态
        await db.collection('scheduled_messages').doc(_id).update({
            data: {
                status: 'error',
                lastError: error.message,
                updateTime: db.serverDate()
            }
        })

        return { success: false, error: error.message }
    }
}

exports.main = async (event, context) => {
    console.log('开始执行定时消息投递任务')

    try {
        const now = new Date()

        // 查询需要投递的消息（包括首次投递和重试）
        const pendingMessages = await db.collection('scheduled_messages')
            .where(_.or([
                // 首次投递：状态为pending且到达投递时间
                _.and([
                    { status: 'pending' },
                    { scheduledTime: _.lte(now) }
                ]),
                // 重试投递：状态为retry_pending且到达重试时间
                _.and([
                    { status: 'retry_pending' },
                    { nextRetryTime: _.lte(now) }
                ])
            ]))
            .get()

        console.log(`找到 ${pendingMessages.data.length} 条待投递消息`)

        if (pendingMessages.data.length === 0) {
            return {
                success: true,
                message: '没有需要投递的消息',
                processedCount: 0
            }
        }

        // 处理每条消息
        const results = []
        for (const messageDoc of pendingMessages.data) {
            const result = await deliverMessage(messageDoc)
            results.push({
                messageId: messageDoc._id,
                ...result
            })
        }

        // 统计结果
        const successCount = results.filter(r => r.success).length
        const failedCount = results.filter(r => !r.success && !r.willRetry).length
        const retryCount = results.filter(r => !r.success && r.willRetry).length

        console.log(`投递完成: 成功 ${successCount}, 失败 ${failedCount}, 重试 ${retryCount}`)

        return {
            success: true,
            processedCount: pendingMessages.data.length,
            successCount,
            failedCount,
            retryCount,
            results
        }

    } catch (error) {
        console.error('定时消息投递任务执行失败:', error)
        return {
            success: false,
            error: error.message,
            errorCode: error.errCode
        }
    }
}