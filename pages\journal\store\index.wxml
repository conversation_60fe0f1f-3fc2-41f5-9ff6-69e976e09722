<view class="store-container">
  <!-- 顶部信息栏 -->
  <view class="top-info-bar">
    <view class="info-block">
      <image src="/components/IconLightball/icon.png" class="info-icon pulse" mode="aspectFit" />
      <text class="info-text">光点：{{balance}}</text>
    </view>
    <view class="info-block">
      <image src="/components/IconVipstar/icon.png" class="info-icon pulse" mode="aspectFit" />
      <text class="info-text">{{vipStatus}}</text>
    </view>
  </view>

  <!-- 顶部slogan -->
  <view class="slogan-wrapper">
    <view class="slogan-background"></view>
    <view class="slogan">「铺子一直开着，光点从不打烊。」</view>
  </view>

  <!-- 商品卡片：初光礼 -->
  <view class="card big-card primary-card">
    <view class="corner-tag glow-tag large-tag">新人限购一次</view>
    <view class="card-divider"></view>
    <image src="cloud://cloudbase-8gji862jcfb501e7.636c-cloudbase-8gji862jcfb501e7-1365531166/images/shop/gift.png" class="card-img glow-border" mode="widthFix" />
    <view class="card-content">
      <view class="card-title glow-text">初光礼</view>
      <view class="glow-desc">✶ 微光从这里开始，而你就是为自己点灯的人。</view>
      <view class="card-price">￥0.01 / 12光点</view>
      <button class="card-button glow-button" data-product="chuguang" bindtap="handlePay">立即拥有</button>
    </view>
  </view>

  <!-- 商品卡片：星光卡 -->
  <view class="card big-card">
    <view class="corner-tag glow-tag large-tag">专属副本</view>
    <view class="card-divider"></view>
    <image src="cloud://cloudbase-8gji862jcfb501e7.636c-cloudbase-8gji862jcfb501e7-1365531166/images/shop/vip.png" class="card-img glow-border" mode="widthFix" />
    <view class="card-content">
      <view class="card-title glow-text">星光卡</view>
      <view class="glow-desc">✶ 有些旅程没人知道，但你知道你一直在走。</view>
      <view class="card-price">￥68 / 每日送4光点</view>
      <button class="card-button glow-button" data-product="xingguang" bindtap="handlePay">解锁专属旅程</button>
    </view>
  </view>

  <!-- 商品卡片：小光盒 & 光点仓 -->
  <view class="card-row">
    <view class="card small-card">
      <image src="cloud://cloudbase-8gji862jcfb501e7.636c-cloudbase-8gji862jcfb501e7-1365531166/images/shop/box.png" class="card-img glow-border" mode="widthFix" />
      <view class="card-content">
        <view class="card-title glow-text">小光盒</view>
        <view class="glow-desc">✶ 光不必多，有一点留给自己就很好。</view>
        <view class="card-price">￥30 / 30光点</view>
        <button class="card-button glow-button" data-product="xiaoguang" bindtap="handlePay">立即购买</button>
      </view>
    </view>
    <view class="card small-card">
      <image src="cloud://cloudbase-8gji862jcfb501e7.636c-cloudbase-8gji862jcfb501e7-1365531166/images/shop/jar.png" class="card-img glow-border" mode="widthFix" />
      <view class="card-content">
        <view class="card-title glow-text">光点仓</view>
        <view class="glow-desc">✶ 你早就知道自己会一直走下去。</view>
        <view class="card-price">￥198 / 240光点</view>
        <button class="card-button glow-button" data-product="guangcang" bindtap="handlePay">立刻充能</button>
      </view>
    </view>
  </view>

  <!-- 页面收尾祝福语 -->
  <view class="page-end">这个铺子里的每个光点，都是有人曾为自己留过的光。</view>
</view>

<view class="journal-container">
  <view class="header">
    <text class="title">星轨日记</text>
    <view wx:if="{{!hasTodayDiary}}" class="today-reminder">
      今日还未记录星轨日记，快去对话吧！
    </view>
  </view>

  <scroll-view scroll-y class="diary-list">
    <block wx:for="{{diaries}}" wx:key="_id">
      <view 
        class="diary-item" 
        data-id="{{item._id}}"
        bindtap="navigateToDiaryDetail"
      >
        <view class="diary-header">
          <text class="diary-theme">{{item.dialogueTheme}}</text>
          <text class="diary-date">{{utils.formatTime(item.createTime)}}</text>
        </view>

        <view class="diary-summary">
          <text class="summary-label">AI总结：</text>
          <text class="summary-text">{{item.aiSummary}}</text>
        </view>

        <view class="diary-emotion">
          <text class="emotion-label">此刻感受：</text>
          <text class="emotion-text">{{item.emotionWord}}</text>
        </view>

        <view class="diary-tomorrow-message">
          <text class="message-label">给明天的话：</text>
          <text class="message-text">{{item.tomorrowMessage}}</text>
        </view>

        <view wx:if="{{item.isVip}}" class="vip-badge">VIP</view>
      </view>
    </block>
  </scroll-view>
</view>

<wxs module="utils">
module.exports = {
  formatTime: function(timestamp) {
    var date = getDate(timestamp);
    return date.getFullYear() + '-' + 
           (date.getMonth() + 1).toString().padStart(2, '0') + '-' + 
           date.getDate().toString().padStart(2, '0')
  }
}
</wxs>