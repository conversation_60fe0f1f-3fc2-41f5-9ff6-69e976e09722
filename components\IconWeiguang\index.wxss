:host {
    display: block;
    width: 400rpx;
    height: 400rpx;
  }
  
  .icon-weiguang {
    width: 100%;
    height: 100%;
    object-fit: contain;
    opacity: 0.97;
  
    filter: drop-shadow(0 0 20rpx rgba(255, 255, 220, 0.25))
            drop-shadow(0 0 40rpx rgba(255, 255, 230, 0.15))
            drop-shadow(0 0 80rpx rgba(255, 255, 255, 0.1));
  
    animation: soulGlow 4.8s ease-in-out infinite;
    transform-origin: center center;
  }
  
  @keyframes soulGlow {
    0%, 100% {
      transform: scale(1);
      filter: drop-shadow(0 0 16rpx rgba(255, 255, 220, 0.2))
              drop-shadow(0 0 32rpx rgba(255, 255, 230, 0.12))
              drop-shadow(0 0 64rpx rgba(255, 255, 255, 0.08));
      opacity: 0.94;
    }
    50% {
      transform: scale(1.04);
      filter: drop-shadow(0 0 28rpx rgba(255, 255, 240, 0.3))
              drop-shadow(0 0 56rpx rgba(255, 255, 255, 0.2))
              drop-shadow(0 0 100rpx rgba(255, 255, 255, 0.1));
      opacity: 1;
    }
  }
  