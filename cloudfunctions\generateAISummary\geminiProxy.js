const cloud = require('wx-server-sdk')
const axios = require('axios')

async function callGemini(options) {
  try {
    const { 
      prompt, 
      temperature = 0.7, 
      maxOutputTokens = 200 
    } = options

    // 调用你现有的 Gemini 代理服务
    const response = await cloud.callFunction({
      name: 'geminiProxy',
      data: {
        userQuestion: prompt,
        aiRoleInstruction: '你是一个温暖、富有洞察力的总结者。用简洁、温和的语言总结对话。',
        temperature,
        maxOutputTokens
      }
    })

    // 检查响应
    if (response.result && response.result.response) {
      return response.result.response
    } else {
      throw new Error('Gemini API 返回异常')
    }
  } catch (error) {
    console.error('Gemini API 调用错误', error)
    throw error
  }
}

module.exports = {
  callGemini
} 