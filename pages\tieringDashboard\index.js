// 层级系统监控仪表板页面
Page({
    data: {
        dashboardData: null,
        loading: true,
        error: null,
        refreshTime: null,

        // 图表数据
        levelDistribution: [],
        qualityMetrics: {},
        anomalies: [],
        performanceData: {}
    },

    onLoad() {
        this.loadDashboardData();
    },

    onShow() {
        // 页面显示时刷新数据
        this.loadDashboardData();
    },

    // 加载仪表板数据
    async loadDashboardData() {
        this.setData({ loading: true, error: null });

        try {
            const result = await wx.cloud.callFunction({
                name: 'tieringAnalytics',
                data: { type: 'getDashboardData' }
            });

            if (result.result.success) {
                const data = result.result.data;

                // 处理层级分布数据
                const levelDistribution = Object.entries(data.levelDistribution || {})
                    .filter(([level, info]) => level !== 'undefined' && info.count > 0)
                    .map(([level, info]) => ({
                        level: parseInt(level),
                        name: info.name,
                        count: info.count,
                        percentage: info.percentage
                    }))
                    .sort((a, b) => a.level - b.level);

                this.setData({
                    dashboardData: data,
                    levelDistribution,
                    qualityMetrics: data.qualityMetrics || {},
                    anomalies: data.anomalies || {},
                    performanceData: data.performance || {},
                    refreshTime: new Date().toLocaleString(),
                    loading: false
                });

                console.log('仪表板数据加载成功:', data);
            } else {
                throw new Error(result.result.error || '获取数据失败');
            }
        } catch (error) {
            console.error('加载仪表板数据失败:', error);
            this.setData({
                error: error.message || '加载数据失败',
                loading: false
            });

            wx.showToast({
                title: '数据加载失败',
                icon: 'error'
            });
        }
    },

    // 手动刷新数据
    onRefresh() {
        wx.showLoading({ title: '刷新中...' });
        this.loadDashboardData().finally(() => {
            wx.hideLoading();
        });
    },

    // 查看详细的层级分布
    viewLevelDistribution() {
        if (!this.data.levelDistribution.length) {
            wx.showToast({
                title: '暂无数据',
                icon: 'none'
            });
            return;
        }

        const content = this.data.levelDistribution
            .map(item => `${item.name}: ${item.count}人 (${item.percentage}%)`)
            .join('\n');

        wx.showModal({
            title: '用户层级分布详情',
            content: content,
            showCancel: false
        });
    },

    // 查看异常详情
    viewAnomalies() {
        const anomalies = this.data.anomalies;
        if (!anomalies.total) {
            wx.showToast({
                title: '暂无异常',
                icon: 'none'
            });
            return;
        }

        const content = `总计: ${anomalies.total}个异常\n高危: ${anomalies.high}个\n中危: ${anomalies.medium}个\n低危: ${anomalies.low}个`;

        wx.showModal({
            title: '异常检测详情',
            content: content,
            showCancel: false
        });
    },

    // 查看性能详情
    viewPerformance() {
        const perf = this.data.performanceData;
        if (!Object.keys(perf).length) {
            wx.showToast({
                title: '暂无性能数据',
                icon: 'none'
            });
            return;
        }

        const content = `平均响应时间: ${perf.averageResponseTime || 0}ms\n系统正常运行时间: ${perf.systemUptime || 0}%\n错误率: ${perf.errorRate || 0}%\n吞吐量: ${perf.throughput || 0}请求/分钟`;

        wx.showModal({
            title: '系统性能详情',
            content: content,
            showCancel: false
        });
    },

    // 导出数据（模拟功能）
    exportData() {
        wx.showModal({
            title: '导出数据',
            content: '数据导出功能正在开发中，敬请期待！',
            showCancel: false
        });
    },

    // 设置监控参数
    settingsMonitor() {
        wx.showModal({
            title: '监控设置',
            content: '监控参数设置功能正在开发中，敬请期待！',
            showCancel: false
        });
    }
});