const cloud = require('wx-server-sdk')

cloud.init({
    env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 创建定时消息数据库表和索引
async function initScheduledMessagesDatabase() {
    try {
        // 创建 scheduled_messages 集合
        await db.createCollection('scheduled_messages')
        console.log('scheduled_messages 集合创建成功')

        // 创建 user_messages 集合
        await db.createCollection('user_messages')
        console.log('user_messages 集合创建成功')

        // 创建索引以提高查询性能
        const scheduledMessagesCollection = db.collection('scheduled_messages')
        const userMessagesCollection = db.collection('user_messages')

        // 为 scheduled_messages 创建索引
        await scheduledMessagesCollection.createIndex({
            keys: { openid: 1, status: 1 },
            name: 'openid_status_index'
        })

        await scheduledMessagesCollection.createIndex({
            keys: { scheduledTime: 1, status: 1 },
            name: 'scheduledTime_status_index'
        })

        await scheduledMessagesCollection.createIndex({
            keys: { nextRetryTime: 1, status: 1 },
            name: 'nextRetryTime_status_index'
        })

        await scheduledMessagesCollection.createIndex({
            keys: { sourceType: 1, sourceId: 1 },
            name: 'source_index'
        })

        // 为 user_messages 创建索引
        await userMessagesCollection.createIndex({
            keys: { openid: 1, createTime: -1 },
            name: 'openid_createTime_index'
        })

        await userMessagesCollection.createIndex({
            keys: { openid: 1, isRead: 1 },
            name: 'openid_isRead_index'
        })

        await userMessagesCollection.createIndex({
            keys: { type: 1 },
            name: 'type_index'
        })

        console.log('数据库索引创建成功')

        return {
            success: true,
            message: '定时消息数据库初始化完成'
        }

    } catch (error) {
        // 如果集合已存在，忽略错误
        if (error.errCode === -501011) {
            console.log('集合已存在，跳过创建')
            return {
                success: true,
                message: '定时消息数据库已存在'
            }
        }

        console.error('初始化定时消息数据库失败:', error)
        return {
            success: false,
            error: error.message,
            errorCode: error.errCode
        }
    }
}

// 清理过期的已投递消息（保留30天）
async function cleanupDeliveredMessages() {
    try {
        const thirtyDaysAgo = new Date()
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

        const result = await db.collection('scheduled_messages')
            .where({
                status: 'delivered',
                deliveredTime: db.command.lt(thirtyDaysAgo)
            })
            .remove()

        console.log(`清理了 ${result.stats.removed} 条过期的已投递消息`)

        return {
            success: true,
            removedCount: result.stats.removed
        }

    } catch (error) {
        console.error('清理过期消息失败:', error)
        return {
            success: false,
            error: error.message
        }
    }
}

// 获取定时消息统计信息
async function getScheduledMessagesStats() {
    try {
        const stats = {}

        // 统计各状态的消息数量
        const statusCounts = await Promise.all([
            db.collection('scheduled_messages').where({ status: 'pending' }).count(),
            db.collection('scheduled_messages').where({ status: 'delivered' }).count(),
            db.collection('scheduled_messages').where({ status: 'failed' }).count(),
            db.collection('scheduled_messages').where({ status: 'retry_pending' }).count()
        ])

        stats.pending = statusCounts[0].total
        stats.delivered = statusCounts[1].total
        stats.failed = statusCounts[2].total
        stats.retryPending = statusCounts[3].total
        stats.total = stats.pending + stats.delivered + stats.failed + stats.retryPending

        // 统计今日投递数量
        const today = new Date()
        today.setHours(0, 0, 0, 0)
        const tomorrow = new Date(today)
        tomorrow.setDate(tomorrow.getDate() + 1)

        const todayDelivered = await db.collection('scheduled_messages')
            .where({
                status: 'delivered',
                deliveredTime: db.command.gte(today).and(db.command.lt(tomorrow))
            })
            .count()

        stats.todayDelivered = todayDelivered.total

        return {
            success: true,
            stats
        }

    } catch (error) {
        console.error('获取统计信息失败:', error)
        return {
            success: false,
            error: error.message
        }
    }
}

exports.main = async (event, context) => {
    console.log('定时消息数据库管理请求:', JSON.stringify(event))

    const { action = 'init' } = event

    try {
        switch (action) {
            case 'init':
                return await initScheduledMessagesDatabase()

            case 'cleanup':
                return await cleanupDeliveredMessages()

            case 'stats':
                return await getScheduledMessagesStats()

            default:
                return {
                    success: false,
                    error: '不支持的操作类型',
                    supportedActions: ['init', 'cleanup', 'stats']
                }
        }

    } catch (error) {
        console.error('数据库管理操作失败:', error)
        return {
            success: false,
            error: error.message,
            errorCode: error.errCode
        }
    }
}