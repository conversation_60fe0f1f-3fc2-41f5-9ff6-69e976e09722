:host {
    display: block;
    width: 100%;
    height: 100%;
  }
  
  .icon-img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    opacity: 0.96;
  
    /* 更柔和的微光层叠 */
    filter: drop-shadow(0 0 8rpx rgba(255, 255, 200, 0.1))
            drop-shadow(0 0 16rpx rgba(255, 255, 220, 0.08));
  
    /* 柔和呼吸缩放 + 微闪 */
    animation: elegantPulse 4.2s ease-in-out infinite;
    transform-origin: center center;
  }
  
  @keyframes elegantPulse {
    0%, 100% {
      transform: scale(1);
      filter: drop-shadow(0 0 6rpx rgba(255, 255, 200, 0.08))
              drop-shadow(0 0 12rpx rgba(255, 255, 220, 0.05));
      opacity: 0.92;
    }
    50% {
      transform: scale(1.03);
      filter: drop-shadow(0 0 12rpx rgba(255, 255, 220, 0.18))
              drop-shadow(0 0 24rpx rgba(255, 255, 230, 0.12));
      opacity: 1;
    }
  }
  
       