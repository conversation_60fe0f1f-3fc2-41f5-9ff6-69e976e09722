/**
 * Progressive Loader
 * Handles progressive loading and rendering of large constellations
 */

class ProgressiveLoader {
    constructor() {
        // Loading configuration
        this.batchSize = 10; // Stars to load per batch
        this.loadDelay = 50; // ms between batches
        this.maxConcurrentLoads = 3; // Maximum concurrent loading operations

        // Loading state
        this.loadingQueue = [];
        this.activeLoads = new Set();
        this.loadedStars = new Map();
        this.isLoading = false;

        // Performance tracking
        this.loadMetrics = {
            totalStars: 0,
            loadedStars: 0,
            loadingTime: 0,
            averageLoadTime: 0,
            batchesProcessed: 0
        };

        // Priority loading
        this.priorityQueue = [];
        this.normalQueue = [];

        // Callbacks
        this.onBatchLoaded = null;
        this.onLoadComplete = null;
        this.onLoadProgress = null;
    }

    /**
     * Initialize the progressive loader
     * @param {Object} options - Configuration options
     */
    initialize(options = {}) {
        this.batchSize = options.batchSize || this.batchSize;
        this.loadDelay = options.loadDelay || this.loadDelay;
        this.maxConcurrentLoads = options.maxConcurrentLoads || this.maxConcurrentLoads;

        // Set callbacks
        this.onBatchLoaded = options.onBatchLoaded || null;
        this.onLoadComplete = options.onLoadComplete || null;
        this.onLoadProgress = options.onLoadProgress || null;

        console.log('ProgressiveLoader initialized:', {
            batchSize: this.batchSize,
            loadDelay: this.loadDelay,
            maxConcurrentLoads: this.maxConcurrentLoads
        });

        return { success: true };
    }

    /**
     * Load stars progressively
     * @param {Array} stars - Stars to load
     * @param {Object} options - Loading options
     */
    loadStarsProgressively(stars, options = {}) {
        if (!stars || stars.length === 0) {
            return Promise.resolve([]);
        }

        const startTime = performance.now();
        this.loadMetrics.totalStars = stars.length;
        this.loadMetrics.loadedStars = 0;
        this.loadMetrics.batchesProcessed = 0;

        // Prioritize stars based on options
        const prioritizedStars = this.prioritizeStarsForLoading(stars, options);

        // Create loading batches
        const batches = this.createLoadingBatches(prioritizedStars);

        return new Promise((resolve, reject) => {
            this.isLoading = true;
            const loadedStars = [];
            let processedBatches = 0;

            const processBatch = async (batchIndex) => {
                if (batchIndex >= batches.length) {
                    // All batches processed
                    this.isLoading = false;
                    this.loadMetrics.loadingTime = performance.now() - startTime;
                    this.loadMetrics.averageLoadTime = this.loadMetrics.loadingTime / this.loadMetrics.totalStars;

                    if (this.onLoadComplete) {
                        this.onLoadComplete(loadedStars, this.loadMetrics);
                    }

                    resolve(loadedStars);
                    return;
                }

                const batch = batches[batchIndex];

                try {
                    // Process batch
                    const processedBatch = await this.processBatch(batch, options);
                    loadedStars.push(...processedBatch);

                    // Update metrics
                    this.loadMetrics.loadedStars += processedBatch.length;
                    this.loadMetrics.batchesProcessed++;
                    processedBatches++;

                    // Trigger callbacks
                    if (this.onBatchLoaded) {
                        this.onBatchLoaded(processedBatch, {
                            batchIndex,
                            totalBatches: batches.length,
                            loadedCount: this.loadMetrics.loadedStars,
                            totalCount: this.loadMetrics.totalStars
                        });
                    }

                    if (this.onLoadProgress) {
                        this.onLoadProgress({
                            loaded: this.loadMetrics.loadedStars,
                            total: this.loadMetrics.totalStars,
                            percentage: (this.loadMetrics.loadedStars / this.loadMetrics.totalStars) * 100,
                            batchesProcessed: processedBatches,
                            totalBatches: batches.length
                        });
                    }

                    // Schedule next batch
                    setTimeout(() => {
                        processBatch(batchIndex + 1);
                    }, this.loadDelay);

                } catch (error) {
                    console.error('Batch processing error:', error);
                    reject(error);
                }
            };

            // Start processing batches
            processBatch(0);
        });
    }

    /**
     * Load stars in viewport first (priority loading)
     * @param {Array} stars - All stars
     * @param {Object} viewport - Viewport bounds
     * @param {Object} mapNavigation - Map navigation handler
     */
    loadViewportStarsFirst(stars, viewport, mapNavigation) {
        const visibleBounds = mapNavigation.getVisibleBounds();

        // Separate visible and non-visible stars
        const visibleStars = [];
        const nonVisibleStars = [];

        stars.forEach(star => {
            if (this.isStarInViewport(star, visibleBounds, viewport)) {
                visibleStars.push(star);
            } else {
                nonVisibleStars.push(star);
            }
        });

        // Load visible stars first, then non-visible
        return this.loadStarsProgressively([...visibleStars, ...nonVisibleStars], {
            priorityMode: 'viewport'
        });
    }

    /**
     * Prioritize stars for loading
     * @param {Array} stars - Stars to prioritize
     * @param {Object} options - Prioritization options
     */
    prioritizeStarsForLoading(stars, options = {}) {
        if (options.priorityMode === 'viewport') {
            // Already prioritized by viewport
            return stars;
        }

        // Default prioritization: recent stars first
        return stars.sort((a, b) => {
            const timeA = new Date(a.createTime || 0).getTime();
            const timeB = new Date(b.createTime || 0).getTime();
            return timeB - timeA; // Most recent first
        });
    }

    /**
     * Create loading batches
     * @param {Array} stars - Stars to batch
     */
    createLoadingBatches(stars) {
        const batches = [];
        for (let i = 0; i < stars.length; i += this.batchSize) {
            batches.push(stars.slice(i, i + this.batchSize));
        }
        return batches;
    }

    /**
     * Process a single batch
     * @param {Array} batch - Batch of stars to process
     * @param {Object} options - Processing options
     */
    async processBatch(batch, options = {}) {
        const processedStars = [];

        for (const star of batch) {
            try {
                // Simulate processing time for heavy operations
                if (options.simulateProcessing) {
                    await this.simulateProcessing();
                }

                // Process star (add any necessary data transformations)
                const processedStar = await this.processStar(star, options);
                processedStars.push(processedStar);

                // Cache the loaded star
                this.loadedStars.set(star.id, processedStar);

            } catch (error) {
                console.error('Error processing star:', star.id, error);
                // Continue with other stars in batch
            }
        }

        return processedStars;
    }

    /**
     * Process a single star
     * @param {Object} star - Star to process
     * @param {Object} options - Processing options
     */
    async processStar(star, options = {}) {
        // Add loading timestamp
        const processedStar = {
            ...star,
            loadedAt: Date.now(),
            processed: true
        };

        // Add any additional processing based on options
        if (options.includeMetadata) {
            processedStar.metadata = {
                loadOrder: this.loadMetrics.loadedStars + 1,
                batchIndex: this.loadMetrics.batchesProcessed,
                processingTime: performance.now()
            };
        }

        return processedStar;
    }

    /**
     * Check if star is in viewport
     * @param {Object} star - Star object
     * @param {Object} bounds - Viewport bounds
     * @param {Object} viewport - Viewport dimensions
     */
    isStarInViewport(star, bounds, viewport) {
        if (!star.starStyle) return false;

        const starX = (star.starStyle.left / 100) * viewport.width;
        const starY = (star.starStyle.top / 100) * viewport.height;

        return (
            starX >= bounds.left &&
            starX <= bounds.right &&
            starY >= bounds.top &&
            starY <= bounds.bottom
        );
    }

    /**
     * Simulate processing time (for testing)
     */
    simulateProcessing() {
        return new Promise(resolve => {
            setTimeout(resolve, Math.random() * 10); // 0-10ms random delay
        });
    }

    /**
     * Cancel current loading operation
     */
    cancelLoading() {
        this.isLoading = false;
        this.loadingQueue = [];
        this.activeLoads.clear();

        console.log('Progressive loading cancelled');
    }

    /**
     * Get loading progress
     */
    getProgress() {
        return {
            isLoading: this.isLoading,
            loaded: this.loadMetrics.loadedStars,
            total: this.loadMetrics.totalStars,
            percentage: this.loadMetrics.totalStars > 0
                ? (this.loadMetrics.loadedStars / this.loadMetrics.totalStars) * 100
                : 0,
            batchesProcessed: this.loadMetrics.batchesProcessed,
            metrics: { ...this.loadMetrics }
        };
    }

    /**
     * Get loaded stars
     */
    getLoadedStars() {
        return Array.from(this.loadedStars.values());
    }

    /**
     * Check if star is loaded
     * @param {string} starId - Star ID
     */
    isStarLoaded(starId) {
        return this.loadedStars.has(starId);
    }

    /**
     * Get loaded star by ID
     * @param {string} starId - Star ID
     */
    getLoadedStar(starId) {
        return this.loadedStars.get(starId);
    }

    /**
     * Clear loaded stars cache
     */
    clearCache() {
        this.loadedStars.clear();
        this.resetMetrics();
    }

    /**
     * Reset loading metrics
     */
    resetMetrics() {
        this.loadMetrics = {
            totalStars: 0,
            loadedStars: 0,
            loadingTime: 0,
            averageLoadTime: 0,
            batchesProcessed: 0
        };
    }

    /**
     * Update configuration
     * @param {Object} config - New configuration
     */
    updateConfig(config) {
        if (config.batchSize !== undefined) {
            this.batchSize = config.batchSize;
        }
        if (config.loadDelay !== undefined) {
            this.loadDelay = config.loadDelay;
        }
        if (config.maxConcurrentLoads !== undefined) {
            this.maxConcurrentLoads = config.maxConcurrentLoads;
        }

        console.log('ProgressiveLoader configuration updated:', config);
    }

    /**
     * Set callbacks
     * @param {Object} callbacks - Callback functions
     */
    setCallbacks(callbacks) {
        if (callbacks.onBatchLoaded) {
            this.onBatchLoaded = callbacks.onBatchLoaded;
        }
        if (callbacks.onLoadComplete) {
            this.onLoadComplete = callbacks.onLoadComplete;
        }
        if (callbacks.onLoadProgress) {
            this.onLoadProgress = callbacks.onLoadProgress;
        }
    }
}

module.exports = ProgressiveLoader;