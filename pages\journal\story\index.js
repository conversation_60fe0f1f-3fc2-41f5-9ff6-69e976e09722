const app = getApp();

Page({
  data: {
    menuButtonInfo: {},
    characters: [],
    animationData: [],
    loading: true
  },

  onLoad: function () {
    this.setData({
      menuButtonInfo: app.globalData.menuButtonInfo
    });
    this.loadCharacters();
  },

  // 加载角色列表
  loadCharacters: function () {
    wx.showLoading({
      title: '加载中...'
    });

    wx.cloud.callFunction({
      name: 'getStoryData',
      data: {
        type: 'getCharacterList'
      }
    }).then(res => {
      wx.hideLoading();
      if (res.result && res.result.success) {
        this.setData({
          characters: res.result.data,
          loading: false
        });
        this.playEntryAnimation();
      } else {
        wx.showToast({
          title: '加载失败',
          icon: 'none'
        });
        console.error('获取角色列表失败:', res.result);
      }
    }).catch(err => {
      wx.hideLoading();
      wx.showToast({
        title: '网络错误',
        icon: 'none'
      });
      console.error('调用云函数失败:', err);
    });
  },

  playEntryAnimation: function () {
    const animation = wx.createAnimation({
      duration: 800,
      timingFunction: 'ease-out',
    });
    let animations = [];
    this.data.characters.forEach((_, index) => {
      animation.opacity(1).translateY(0).step({ delay: index * 150 });
      animations[index] = animation.export();
    });
    this.setData({
      animationData: animations
    });
  },

  navigateToChapter: function (event) {
    const characterId = event.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/journal/chapter/index?id=${characterId}`
    });
  },

  navigateBack: function () {
    wx.navigateBack();
  }
});