@import "../../app.wxss";

/* ===== 页面结构 ===== */
.container {
  width: 100vw;
  height: 100vh;
  position: relative;
  overflow: hidden;
}

.bg {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}

.content {
  position: relative;
  z-index: 2;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  box-sizing: border-box;
}

.title {
  font-size: 72rpx;
  color: #ffffff;
  text-shadow: 0 0 20rpx rgba(255, 255, 255, 0.5);
  margin-bottom: 20rpx;
}

.subtitle {
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 100rpx;
}

.login-area {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.login-btn {
  width: 80% !important;
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 44rpx;
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  font-size: 32rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.tips {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  border: 6rpx solid rgba(255, 255, 255, 0.8);
  margin-bottom: 20rpx;
}

.nickname {
  font-size: 36rpx;
  color: #ffffff;
  margin-bottom: 10rpx;
}

.welcome-back {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}
