.journal-page {
    position: relative;
    width: 100vw;
    min-height: 100vh;
    overflow: hidden;
    background-color: #000;
  }
  
  .background-image {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100vw;
    height: 100vh;
    z-index: 0;
    object-fit: cover;
    pointer-events: none;
  }
  
  .content-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    min-height: 100vh;
    padding: 100rpx 0;
    box-sizing: border-box;
    position: relative;
    z-index: 1;
  }
  
  .grid {
    padding: 0 48rpx;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 64rpx;
    z-index: 2;
  }
  
  @keyframes floatUp {
    0% { transform: translateY(0rpx); }
    50% { transform: translateY(-6rpx); }
    100% { transform: translateY(0rpx); }
  }
  
  .card {
    background: radial-gradient(circle at 50% 20%, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.01));
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 36rpx;
    padding: 36rpx 24rpx;
    text-align: center;
    box-shadow:
      inset 0 0 8rpx rgba(255, 255, 255, 0.08),
      0 0 36rpx rgba(255, 255, 255, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    backdrop-filter: blur(6rpx);
    animation: floatUp 6s ease-in-out infinite;
  }
  
  .card:hover {
    transform: translateY(-10rpx) scale(1.03);
    box-shadow:
      inset 0 0 10rpx rgba(255, 255, 255, 0.15),
      0 0 64rpx rgba(255, 255, 255, 0.25);
  }
  
  .card-image {
    width: 100%;
    height: 280rpx;
    object-fit: cover;
    border-radius: 28rpx;
    margin-bottom: 24rpx;
    box-shadow: 0 0 16rpx rgba(255, 255, 255, 0.15);
  }
  
  .card-text {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12rpx;
  }
  
  .card-title {
    font-size: 44rpx;
    color: #F5F5F5;
    font-weight: 600;
    text-shadow:
      0 0 6rpx rgba(255, 255, 255, 0.25),
      0 0 3rpx rgba(0, 0, 0, 0.4);
    letter-spacing: 2rpx;
  }
  
  .card-slogan {
    font-size: 24rpx;
    color: rgba(240, 240, 240, 0.65);
    font-weight: 300;
    letter-spacing: 1rpx;
    text-align: center;
    text-shadow:
      0 0 8rpx rgba(255, 255, 255, 0.15),
      0 0 4rpx rgba(255, 255, 255, 0.05);
  }