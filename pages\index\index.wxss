.container {
  padding: 48px;
  font-size: 20px;
  position: relative;
  width: 100%;
  height: 100vh;
}

.test-entry {
  position: fixed;
  bottom: 80rpx;
  right: 40rpx;
  z-index: 9999;
}

.test-button {
  background: rgba(72, 52, 212, 0.9);
  color: white;
  font-size: 28rpx;
  padding: 16rpx 32rpx;
  border-radius: 40rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(10px);
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.3);
  min-width: 180rpx;
}

.test-button:active {
  transform: scale(0.98);
  background: rgba(72, 52, 212, 1);
}
