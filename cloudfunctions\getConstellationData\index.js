const cloud = require('wx-server-sdk')

cloud.init({
    env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 错误类型定义
const ERROR_TYPES = {
    VALIDATION_ERROR: 'validation_error',
    DATABASE_ERROR: 'database_error',
    NETWORK_ERROR: 'network_error',
    SYSTEM_ERROR: 'system_error',
    TIMEOUT_ERROR: 'timeout_error',
    PERMISSION_ERROR: 'permission_error'
}

// 创建错误响应
function createErrorResponse(errorType, message, details = {}) {
    return {
        success: false,
        error: message,
        errorType,
        timestamp: new Date().toISOString(),
        details
    }
}

// 记录错误到日志系统
async function logError(error, context, additionalData = {}) {
    try {
        const errorLog = {
            timestamp: new Date().toISOString(),
            context,
            error: {
                message: error.message,
                stack: error.stack,
                code: error.errCode || error.code
            },
            additionalData
        }

        console.error(`[错误日志] ${context}:`, errorLog)
    } catch (logError) {
        console.error('记录错误日志失败:', logError)
    }
}

// 格式化星座数据
function formatConstellationData(records) {
    const stars = []
    let constellationMetadata = null

    records.forEach(record => {
        if (record.starPosition) {
            const star = {
                id: record._id,
                position: record.starPosition,
                metadata: {
                    dialogueTheme: record.theme || '未知主题',
                    coreFeeling: record.dailyFeeling || record.emotionKeyword || '',
                    lightingDate: record.createTime || record.completedTime,
                    hasGeneratedDiary: Boolean(record.diaryContent),
                    emotionKeyword: record.emotionKeyword || '',
                    starKeyword: record.starKeyword || record.emotionKeyword || '',
                    dialogueContent: record.dialogueContent || []
                },
                visualProperties: {
                    size: 1.0,
                    brightness: 1.0,
                    color: getEmotionColor(record.emotionKeyword || ''),
                    glowIntensity: 0.8
                },
                createdAt: record.createTime || record.completedTime,
                updatedAt: record.lastPositionUpdate || record.updateTime
            }

            stars.push(star)
        }

        // 如果记录包含完整的星座数据，使用最新的一个
        if (record.constellationData && (!constellationMetadata ||
            new Date(record.constellationData.updatedAt) > new Date(constellationMetadata.updatedAt))) {
            constellationMetadata = record.constellationData
        }
    })

    // 计算星座边界
    const bounds = calculateConstellationBounds(stars)

    return {
        userId: records[0]?.openid || '',
        stars,
        metadata: constellationMetadata || {
            totalStars: stars.length,
            firstStarDate: stars.length > 0 ? Math.min(...stars.map(s => new Date(s.createdAt))) : null,
            lastStarDate: stars.length > 0 ? Math.max(...stars.map(s => new Date(s.createdAt))) : null,
            constellationBounds: bounds
        },
        settings: {
            defaultZoomLevel: 1.0,
            centerPoint: bounds ? {
                x: (bounds.minX + bounds.maxX) / 2,
                y: (bounds.minY + bounds.maxY) / 2
            } : { x: 0.5, y: 0.5 }
        },
        version: constellationMetadata?.version || 1,
        createdAt: stars.length > 0 ? Math.min(...stars.map(s => new Date(s.createdAt))) : new Date(),
        updatedAt: stars.length > 0 ? Math.max(...stars.map(s => new Date(s.updatedAt || s.createdAt))) : new Date()
    }
}

// 计算星座边界
function calculateConstellationBounds(stars) {
    if (stars.length === 0) {
        return null
    }

    let minX = Infinity, maxX = -Infinity
    let minY = Infinity, maxY = -Infinity

    stars.forEach(star => {
        const x = star.position.x
        const y = star.position.y

        minX = Math.min(minX, x)
        maxX = Math.max(maxX, x)
        minY = Math.min(minY, y)
        maxY = Math.max(maxY, y)
    })

    return { minX, maxX, minY, maxY }
}

// 获取情感关键词对应的颜色
function getEmotionColor(emotion) {
    const emotionColors = {
        '开心': '#FFD700',
        '快乐': '#FFD700',
        '喜悦': '#FFD700',
        '平静': '#87CEEB',
        '安静': '#87CEEB',
        '宁静': '#87CEEB',
        '困惑': '#DDA0DD',
        '迷茫': '#DDA0DD',
        '纠结': '#DDA0DD',
        '焦虑': '#F0A0A0',
        '担心': '#F0A0A0',
        '紧张': '#F0A0A0',
        '感动': '#FFB6C1',
        '温暖': '#FFB6C1',
        '治愈': '#98FB98'
    }

    return emotionColors[emotion] || '#FFFFFF'
}

exports.main = async (event, context) => {
    const startTime = Date.now()
    let operationContext = 'getConstellationData'

    try {
        console.log('收到的事件参数:', JSON.stringify(event))

        const {
            includeMetadata = true,    // 是否包含元数据
            includeVisualProps = true, // 是否包含视觉属性
            limit = 50,               // 最大星星数量
            sortBy = 'createTime',    // 排序字段
            sortOrder = 'desc'        // 排序顺序
        } = event

        // 获取用户身份
        const wxContext = cloud.getWXContext()
        const openid = wxContext.OPENID

        if (!openid) {
            return createErrorResponse(ERROR_TYPES.PERMISSION_ERROR, '用户身份验证失败')
        }

        try {
            // 查询用户的所有三问记录，包含星座位置数据
            const query = db.collection('three_questions_records')
                .where({
                    openid: openid,
                    starPosition: db.command.exists(true) // 只获取有位置数据的记录
                })
                .orderBy(sortBy, sortOrder)
                .limit(limit)

            const result = await query.get()

            console.log(`找到 ${result.data.length} 条星座记录`)

            if (result.data.length === 0) {
                return {
                    success: true,
                    constellation: {
                        userId: openid,
                        stars: [],
                        metadata: {
                            totalStars: 0,
                            firstStarDate: null,
                            lastStarDate: null,
                            constellationBounds: null
                        },
                        settings: {
                            defaultZoomLevel: 1.0,
                            centerPoint: { x: 0.5, y: 0.5 }
                        },
                        version: 1,
                        createdAt: new Date(),
                        updatedAt: new Date()
                    },
                    totalRecords: 0
                }
            }

            // 格式化星座数据
            const constellation = formatConstellationData(result.data)

            // 根据参数过滤数据
            if (!includeMetadata) {
                constellation.stars.forEach(star => {
                    delete star.metadata.dialogueContent
                })
            }

            if (!includeVisualProps) {
                constellation.stars.forEach(star => {
                    delete star.visualProperties
                })
            }

            return {
                success: true,
                constellation,
                totalRecords: result.data.length,
                queryParams: {
                    includeMetadata,
                    includeVisualProps,
                    limit,
                    sortBy,
                    sortOrder
                }
            }

        } catch (dbError) {
            console.error('数据库查询失败:', dbError)

            await logError(dbError, operationContext, {
                openid,
                limit,
                sortBy,
                sortOrder,
                executionTime: Date.now() - startTime
            })

            if (dbError.message.includes('timeout')) {
                return createErrorResponse(ERROR_TYPES.TIMEOUT_ERROR, '数据库查询超时，请重试')
            } else if (dbError.message.includes('network') || dbError.errCode === -1) {
                return createErrorResponse(ERROR_TYPES.NETWORK_ERROR, '网络连接失败，请检查网络后重试')
            } else {
                return createErrorResponse(ERROR_TYPES.DATABASE_ERROR, '数据库查询失败，请稍后重试', {
                    errorCode: dbError.errCode,
                    errorMessage: dbError.message
                })
            }
        }

    } catch (outerError) {
        console.error('云函数执行发生严重错误:', outerError)

        await logError(outerError, 'getConstellationData_critical', {
            executionTime: Date.now() - startTime,
            originalContext: operationContext
        })

        return createErrorResponse(ERROR_TYPES.SYSTEM_ERROR, '系统发生严重错误，请联系技术支持', {
            errorCode: outerError.errCode || 'CRITICAL_ERROR',
            timestamp: new Date().toISOString()
        })
    }
}