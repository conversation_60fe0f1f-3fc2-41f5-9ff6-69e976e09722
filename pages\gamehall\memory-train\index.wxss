page {
  height: 100vh;
  overflow: hidden;
  background-color: #000;
  color: #eaeaea;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
}

.page-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.background-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.background-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.75); /* 加深遮罩 */
  z-index: 2;
}

.ui-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 3;
  display: flex;
  flex-direction: column;
  padding: 0 40rpx;
  padding-top: calc(var(--status-bar-height) + 40rpx);
  box-sizing: border-box;
}

/* --- 2. 【V5.0 新增】跳过按钮 --- */
.skip-button-wrapper {
  position: absolute;
  top: calc(var(--status-bar-height) + 20rpx);
  right: 40rpx;
  z-index: 10;
}
.skip-button {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.skip-arrow {
  width: 20rpx;
  height: 20rpx;
  border-right: 4rpx solid rgba(255, 255, 255, 0.5);
  border-bottom: 4rpx solid rgba(255, 255, 255, 0.5);
  transform: rotate(-45deg);
}

/* --- 3. 叙事区 --- */
.narrative-scroll {
  flex: 1;
  min-height: 0;
  margin-bottom: 40rpx;
}

.narrative-area {
  transition: opacity 0.5s ease-out;
}

.text-line {
  /* 【V5.0 质感】电影字幕美学 */
  font-family: "Georgia", "SourceHanSerifCN-Regular", serif;
  font-size: 38rpx; /* 略微增大字体 */
  color: #eaeaea;
  line-height: 1.85;
  letter-spacing: 3rpx; /* 增加字间距，营造稀疏感 */
  margin-bottom: 30rpx;
  /* 增强文字辉光，使其在黑暗中更柔和、更立体 */
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.7), 0 0 10rpx rgba(255, 255, 255, 0.1);

  opacity: 0;
  transform: translateY(20rpx);
  animation: text-fade-in 1.2s cubic-bezier(0.25, 1, 0.5, 1) forwards;
}

@keyframes text-fade-in {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.scroll-padding-bottom {
  height: 35vh; /* 增加安全留白 */
}

/* --- 4. 选择区 --- */
.choice-area-wrapper {
  flex-shrink: 0;
  width: 100%;
  padding-bottom: calc(env(safe-area-inset-bottom) + 20rpx);
}

.choice-area {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  opacity: 0;
  transform: translateY(50rpx);
  transition: all 0.8s cubic-bezier(0.25, 1, 0.5, 1);
}

.choice-area.fade-in {
  opacity: 1;
  transform: translateY(0);
}

.option-button {
  width: 100%;
  padding: 32rpx;
  /* 【V5.0 质感】使用带有微妙杂色纹理的背景 */
  background-color: rgba(26, 26, 26, 0.8);
  backdrop-filter: blur(15px);
  border-radius: 24rpx;
  border: 1px solid rgba(255, 215, 0, 0.25);
  color: #cccccc;
  text-align: left;
  transition: all 0.2s ease-out;
  /* 【V5.0 修复】确保按钮宽度计算正确，不会溢出 */
  box-sizing: border-box;
}

.option-button:active {
  transform: scale(0.97);
  border-color: rgba(255, 215, 0, 0.8);
  box-shadow: 0 0 30rpx rgba(255, 215, 0, 0.5);
}

.option-main-text {
  font-size: 32rpx;
  color: #e0e0e0;
  display: block;
  margin-bottom: 16rpx;
  line-height: 1.5; /* 增加主选项行高 */
}

.option-sub-text {
  font-size: 28rpx; /* 增大内心独白字体，提升清晰度 */
  color: #a0a0a0; /* 提高颜色亮度 */
  font-style: normal; /* 去掉斜体，更易读 */
  display: block;
  line-height: 1.5;
}

/* --- 5. 整体淡出动画 --- */
.fade-out {
  opacity: 0 !important;
  transition: opacity 0.5s ease-out;
}
