/**
 * Information Display System
 * Manages star information cards and diary access
 */

class InformationDisplay {
    constructor() {
        this.currentCard = null;
        this.isCardVisible = false;
    }

    /**
     * Show star information card
     * @param {Object} starData - Star data containing metadata
     * @param {Object} position - Position to show the card
     * @returns {Object} Card display result
     */
    showStarInfo(starData, position = null) {
        try {
            // Validate star data
            if (!starData) {
                throw new Error('Star data is required');
            }

            // Format the star information
            const formattedInfo = this.formatStarInfo(starData);

            // Create card data structure
            const cardData = {
                id: starData.id,
                title: starData.theme || '未知主题',
                date: this.formatStarDate(starData.createTime || starData.lightingDate),
                emotion: starData.emotionKeyword || starData.coreFeeling || '',
                summary: starData.summary || '',
                hasGeneratedDiary: starData.hasGeneratedDiary || false,
                diaryId: starData.diaryId || null,
                position: position,
                ...formattedInfo
            };

            this.currentCard = cardData;
            this.isCardVisible = true;

            console.log('显示星星信息卡片:', cardData);

            return {
                success: true,
                cardData: cardData,
                shouldShowDiaryButton: cardData.hasGeneratedDiary
            };

        } catch (error) {
            console.error('显示星星信息失败:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Format star date to Chinese format
     * @param {Date|string|number} timestamp - Date timestamp
     * @returns {string} Formatted date string
     */
    formatStarDate(timestamp) {
        try {
            if (!timestamp) {
                return '未知时间';
            }

            const date = new Date(timestamp);

            // Check if date is valid
            if (isNaN(date.getTime())) {
                return '未知时间';
            }

            const year = date.getFullYear();
            const month = date.getMonth() + 1;
            const day = date.getDate();

            return `${year} 年 ${month} 月 ${day} 日`;

        } catch (error) {
            console.error('日期格式化失败:', error);
            return '未知时间';
        }
    }

    /**
     * Format star information for display
     * @param {Object} starData - Raw star data
     * @returns {Object} Formatted information
     */
    formatStarInfo(starData) {
        const formatted = {
            displayTitle: starData.theme || '未知主题',
            displayDate: this.formatStarDate(starData.createTime || starData.lightingDate),
            displayEmotion: starData.emotionKeyword || starData.coreFeeling || '',
            displaySummary: starData.summary || '',
            emotionColor: this.getEmotionColor(starData.emotionKeyword || starData.coreFeeling || ''),
            hasContent: !!(starData.summary || starData.dialogueContent),
            contentPreview: this.generateContentPreview(starData)
        };

        return formatted;
    }

    /**
     * Generate content preview for the card
     * @param {Object} starData - Star data
     * @returns {string} Content preview
     */
    generateContentPreview(starData) {
        if (starData.summary) {
            return starData.summary.length > 50
                ? starData.summary.substring(0, 50) + '...'
                : starData.summary;
        }

        if (starData.answers && starData.answers.length > 0) {
            const firstAnswer = starData.answers[0];
            if (firstAnswer) {
                return firstAnswer.length > 30
                    ? firstAnswer.substring(0, 30) + '...'
                    : firstAnswer;
            }
        }

        return '点击查看详细内容';
    }

    /**
     * Get emotion color based on keyword
     * @param {string} emotion - Emotion keyword
     * @returns {string} Color hex code
     */
    getEmotionColor(emotion) {
        const emotionColors = {
            '开心': '#FFD700',
            '快乐': '#FFD700',
            '喜悦': '#FFD700',
            '兴奋': '#FF6B6B',
            '激动': '#FF6B6B',
            '平静': '#87CEEB',
            '安静': '#87CEEB',
            '宁静': '#87CEEB',
            '放松': '#98FB98',
            '困惑': '#DDA0DD',
            '迷茫': '#DDA0DD',
            '纠结': '#DDA0DD',
            '思考': '#DDA0DD',
            '焦虑': '#F0A0A0',
            '担心': '#F0A0A0',
            '紧张': '#F0A0A0',
            '害怕': '#F0A0A0',
            '感动': '#FFB6C1',
            '温暖': '#FFB6C1',
            '治愈': '#98FB98',
            '满足': '#98FB98',
            '悲伤': '#B0C4DE',
            '难过': '#B0C4DE',
            '失落': '#B0C4DE',
            '愤怒': '#FF4500',
            '生气': '#FF4500',
            '烦躁': '#FF4500'
        };

        return emotionColors[emotion] || '#FFFFFF';
    }

    /**
     * Create diary access button data
     * @param {Object} starData - Star data
     * @returns {Object} Button configuration
     */
    createDiaryButton(starData) {
        if (!starData.hasGeneratedDiary) {
            return {
                show: false,
                disabled: true,
                text: '暂无日记',
                action: null
            };
        }

        return {
            show: true,
            disabled: false,
            text: '查看日记',
            action: 'viewDiary',
            diaryId: starData.diaryId || starData.id,
            starId: starData.id
        };
    }

    /**
     * Handle card interactions
     * @param {string} action - Action type
     * @param {Object} starData - Star data
     * @returns {Object} Action result
     */
    handleCardInteraction(action, starData) {
        try {
            switch (action) {
                case 'viewDiary':
                    return this.handleViewDiary(starData);

                case 'close':
                    return this.closeCard();

                case 'expand':
                    return this.expandCard(starData);

                default:
                    console.warn('未知的卡片交互动作:', action);
                    return {
                        success: false,
                        error: '未知的交互动作'
                    };
            }
        } catch (error) {
            console.error('处理卡片交互失败:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Handle view diary action
     * @param {Object} starData - Star data
     * @returns {Object} Navigation result
     */
    handleViewDiary(starData) {
        try {
            if (!starData.hasGeneratedDiary) {
                return {
                    success: false,
                    error: '该星星没有生成日记'
                };
            }

            // Prepare navigation data
            const navigationData = {
                diaryId: starData.diaryId || starData.id,
                starId: starData.id,
                date: starData.createTime || starData.lightingDate,
                theme: starData.theme,
                emotion: starData.emotionKeyword || starData.coreFeeling
            };

            console.log('准备跳转到日记页面:', navigationData);

            return {
                success: true,
                action: 'navigate',
                target: '/pages/journal/reader/index',
                data: navigationData
            };

        } catch (error) {
            console.error('处理查看日记失败:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Close information card
     * @returns {Object} Close result
     */
    closeCard() {
        this.currentCard = null;
        this.isCardVisible = false;

        return {
            success: true,
            action: 'close'
        };
    }

    /**
     * Expand card to show more details
     * @param {Object} starData - Star data
     * @returns {Object} Expand result
     */
    expandCard(starData) {
        try {
            // Create expanded card data with more details
            const expandedData = {
                ...this.currentCard,
                expanded: true,
                fullSummary: starData.summary || '',
                questions: starData.questions || [],
                answers: starData.answers || [],
                tomorrowMessage: starData.tomorrowMessage || '',
                dialogueContent: starData.dialogueContent || []
            };

            this.currentCard = expandedData;

            return {
                success: true,
                action: 'expand',
                cardData: expandedData
            };

        } catch (error) {
            console.error('展开卡片失败:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Get current card state
     * @returns {Object} Current card information
     */
    getCurrentCard() {
        return {
            isVisible: this.isCardVisible,
            cardData: this.currentCard
        };
    }

    /**
     * Update card position
     * @param {Object} newPosition - New position coordinates
     */
    updateCardPosition(newPosition) {
        if (this.currentCard) {
            this.currentCard.position = newPosition;
        }
    }

    /**
     * Check if card is currently visible
     * @returns {boolean} Visibility state
     */
    isCardCurrentlyVisible() {
        return this.isCardVisible;
    }

    /**
     * Generate card animation configuration
     * @param {string} animationType - Type of animation
     * @returns {Object} Animation configuration
     */
    getCardAnimation(animationType = 'fadeIn') {
        const animations = {
            fadeIn: {
                duration: 300,
                easing: 'ease-out',
                keyframes: [
                    { opacity: 0, transform: 'scale(0.8)' },
                    { opacity: 1, transform: 'scale(1)' }
                ]
            },
            fadeOut: {
                duration: 200,
                easing: 'ease-in',
                keyframes: [
                    { opacity: 1, transform: 'scale(1)' },
                    { opacity: 0, transform: 'scale(0.8)' }
                ]
            },
            slideUp: {
                duration: 300,
                easing: 'ease-out',
                keyframes: [
                    { opacity: 0, transform: 'translateY(20px)' },
                    { opacity: 1, transform: 'translateY(0)' }
                ]
            }
        };

        return animations[animationType] || animations.fadeIn;
    }
}

module.exports = InformationDisplay;