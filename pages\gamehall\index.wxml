<view class="page-container">
  <!-- 背景图 -->
  <image class="background-image" src="cloud://cloudbase-8gji862jcfb501e7.636c-cloudbase-8gji862jcfb501e7-1365531166/images/homeBG.png" mode="aspectFill"></image>

  <!-- 自定义导航栏 -->
  <view class="custom-nav" style="background-color: rgba(12, 12, 12, {{navOpacity}});">
    <view class="nav-content">
      <!-- 左侧容器 (返回按钮) -->
      <view class="nav-left">
        <view class="nav-back-button" bindtap="navigateBack">
          <text class="nav-back-arrow">&lt;</text>
        </view>
      </view>
      <!-- 中间容器 (标题) -->
      <view class="nav-center">
        <text class="nav-title">微光游戏厅</text>
      </view>
      <!-- 右侧容器 (占位) -->
      <view class="nav-right"></view>
    </view>
  </view>

  <!-- 页面主要内容 -->
  <view class="content-wrapper">
    <!-- 【V3.13 核心修复】这里不再需要占位符了 -->
    
    <!-- 顶部品牌引导区 -->
    <view class="hero-section">
      <view class="hero-logo-placeholder"></view>
      <text class="hero-slogan">你的选择，将绘成独一无二的灵魂星图。</text>
    </view>

    <!-- 故事盒子 -->
    <view class="section">
      <view class="section-title">
        <view class="section-icon-wrapper">
          <image class="section-icon" src="/icon_box.png" mode="aspectFit"></image>
        </view>
        <view class="section-title-text">
          <text class="main-title">故事盒子</text>
          <text class="sub-title">在既定的命运中，探寻内心的真实选择</text>
        </view>
      </view>

      <view class="game-card-list">
        <view 
          class="game-card" 
          wx:for="{{gameList}}" 
          wx:key="id" 
          data-game="{{item}}" 
          bindtap="navigateToGame"
        >
          <!-- 左侧封面图 -->
          <image class="card-visual" src="{{item.coverImage}}" mode="aspectFill"></image>
          
          <!-- 右侧信息区 -->
          <view class="card-info">
            <!-- 上半部分：标题和Slogan -->
            <view class="card-header">
              <text class="game-title">{{item.title}}</text>
              <text class="game-slogan">{{item.slogan}}</text>
            </view>
            <!-- 下半部分：标签、时长和价格 -->
            <view class="card-footer">
              <view class="game-tags">
                <text class="tag" wx:for="{{item.tags}}" wx:for-item="tag" wx:key="*this">{{tag}}</text>
              </view>
              <view class="game-meta">
                <view class="meta-item">
                  <text class="meta-icon">⏳</text>
                  <text>{{item.duration}}</text>
                </view>
                <view class="meta-item cost">
                  <view class="point-icon"></view>
                  <text class="cost-number">{{item.cost}}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 灵魂剧场 -->
    <view class="section">
      <view class="section-title">
        <view class="section-icon-wrapper">
          <image class="section-icon" src="/icon_feather.png" mode="aspectFit"></image>
        </view>
        <view class="section-title-text">
          <text class="main-title">灵魂剧场</text>
          <text class="sub-title">在无垠的可能性中，亲手写下你的灵魂剧本</text>
        </view>
      </view>
      <view class="coming-soon-card">
        <view class="planet"></view>
        <text class="coming-soon-main">AI互动剧本即将开放</text>
        <text class="coming-soon-sub">你的每一句话，都将成为宇宙的回响</text>
      </view>
    </view>

    <!-- 页脚 -->
    <view class="page-footer">
      <text>更多故事，正在星海中孕育...</text>
    </view>
  </view>
</view>

