Page({
  data: {
    balance: 0,
    vipStatus: "未开通",
    paying: false,
    diaries: [],
    hasTodayDiary: false
  },

  onLoad() {
    wx.cloud.callFunction({
      name: 'getUserInfo',
      success: res => {
        const user = res.result;
        this.setData({
          vipStatus: user.isVIP ? '已开通星光卡' : '未开通'
        });
      },
      fail: err => {
        console.error("获取用户数据失败：", err);
      }
    });
    this.fetchStarTrackDiaries()
  },

  onShow() {
    this.getUserPoints();
  },

  getUserPoints() {
    wx.cloud.callFunction({
      name: 'user',
      data: { type: 'getSelf' },
      success: res => {
        console.log('从云函数获取到的用户数据：', res);
        if (res.result && res.result.data && res.result.data.points !== undefined) {
          const newBalance = res.result.data.points;
          this.setData({ balance: newBalance });
          console.log('setData 后的 balance 值：', this.data.balance);
        } else {
          console.warn('云函数返回数据不完整或 points 字段缺失', res);
        }
      },
      fail: err => {
        console.error('获取用户光点失败', err);
      }
    });
  },

  handlePay(e) {
    if (this.data.paying) return;

    const product = e.currentTarget.dataset.product;
    const productMap = {
      chuguang: { description: "初光礼", amount: 1, productId: "light_12" },
      xingguang: { description: "星光卡", amount: 6800, productId: "vip_month" },
      xiaoguang: { description: "小光盒", amount: 3000, productId: "light_30" },
      guangcang: { description: "光点仓", amount: 19800, productId: "light_240" }
    };

    const item = productMap[product];
    if (!item) {
      wx.showToast({ title: "商品未定义", icon: "none" });
      return;
    }

    this.setData({ paying: true });
    wx.showLoading({ title: "正在拉起支付…" });

    wx.cloud.callFunction({
      name: 'getOpenid',
      env: 'cloudbase-8gji862jcfb501e7',
      success: res => {
        const openid = res.result.openid;
        const outTradeNo = this.generateOutTradeNo();

        wx.cloud.callFunction({
          name: 'payment',
          data: {
            productId: item.productId
          },
          success: res => {
            wx.hideLoading();
            const result = res.result;

            if (!result || result.code !== 0 || !result.data) {
              wx.showToast({ title: "支付参数错误", icon: "none" });
              console.error("返回的支付参数不完整：", result);
              this.setData({ paying: false });
              return;
            }

            const payData = result.data;
            wx.requestPayment({
              timeStamp: payData.timeStamp,
              nonceStr: payData.nonceStr,
              package: payData.package,
              signType: payData.signType || 'RSA',
              paySign: payData.paySign,
              success: () => {
                wx.showToast({ title: "支付成功", icon: "success" });
                wx.redirectTo({ url: '/pages/store/index' });
                // 支付成功后刷新光点余额
                this.getUserPoints();
              },
              fail: err => {
                wx.showToast({ title: "支付已取消", icon: "none" });
                console.warn("用户取消支付：", err);
              },
              complete: () => {
                this.setData({ paying: false });
              }
            });
          },
          fail: err => {
            wx.hideLoading();
            this.setData({ paying: false });
            wx.showToast({ title: "下单失败", icon: "none" });
            console.error("调用支付函数失败：", err);
          }
        });
      },
      fail: err => {
        wx.hideLoading();
        this.setData({ paying: false });
        wx.showToast({ title: "身份获取失败", icon: "none" });
        console.error("获取 openid 失败：", err);
      }
    });
  },

  generateOutTradeNo() {
    const now = new Date();
    const timestamp = now.getFullYear().toString()
      + (now.getMonth() + 1).toString().padStart(2, '0')
      + now.getDate().toString().padStart(2, '0')
      + now.getHours().toString().padStart(2, '0')
      + now.getMinutes().toString().padStart(2, '0')
      + now.getSeconds().toString().padStart(2, '0');
    const random = Math.floor(Math.random() * 1000000).toString().padStart(6, '0');
    return `${timestamp}${random}`;
  },

  // 获取星轨日记列表
  fetchStarTrackDiaries() {
    wx.cloud.callFunction({
      name: 'getStarTrackDiary',
      data: {
        type: 'list',
        openid: wx.getStorageSync('openid'),
        limit: 10,
        offset: 0
      }
    }).then(res => {
      if (res.result && res.result.success) {
        this.setData({
          diaries: res.result.diaries || [],
          hasTodayDiary: res.result.hasTodayDiary || false
        })
      } else {
        // 如果获取失败，设置默认值，不显示错误提示
        this.setData({
          diaries: [],
          hasTodayDiary: false
        })
        console.log('日记集合可能不存在，使用默认值')
      }
    }).catch(err => {
      console.error('获取星轨日记错误', err)
      // 数据库集合不存在时，设置默认值
      this.setData({
        diaries: [],
        hasTodayDiary: false
      })
      console.log('数据库集合不存在，使用默认值')
    })
  },

  // 跳转到日记详情页
  navigateToDiaryDetail(e) {
    const diaryId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/journal/detail/index?id=${diaryId}`
    })
  },

  // 创建星轨日记
  createStarTrackDiary(dialogueData) {
    wx.cloud.callFunction({
      name: 'createStarTrackDiary',
      data: {
        openid: wx.getStorageSync('openid'),
        aiSummary: dialogueData.aiSummary,
        emotionWord: dialogueData.emotionWord,
        tomorrowMessage: dialogueData.tomorrowMessage,
        dialogueContent: dialogueData.dialogueContent,
        dialogueTheme: dialogueData.dialogueTheme
      }
    }).then(res => {
      if (res.result.success) {
        wx.showToast({
          title: '日记保存成功',
          icon: 'success'
        })
        this.fetchStarTrackDiaries()
      } else {
        wx.showToast({
          title: '日记保存失败',
          icon: 'none'
        })
      }
    }).catch(err => {
      console.error('创建星轨日记错误', err)
    })
  }
});
