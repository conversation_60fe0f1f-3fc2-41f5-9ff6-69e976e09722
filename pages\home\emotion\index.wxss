page {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
  background-color: black;
}

.container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  z-index: 0;
}

.background {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: -1;
  opacity: 1;
  transition: opacity 2s ease-in-out;
  object-fit: cover;
  filter: brightness(0.85);
  transform: translateY(-15%);
}

.overlay {
  z-index: 10;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  min-height: 100vh;
  padding: 20px 24px 0; /* ✅ 原来是60px，已上移 */
}

.content-box {
  max-width: 480px;
  width: 100%;
  text-align: center;
  color: white;
  font-size: 17px;
  line-height: 2.2em;
  font-family: "Noto Serif SC", "Alibaba PuHuiTi", sans-serif;
}

.text-line {
  opacity: 0;
  animation: fadeIn 1s ease-in forwards;
  margin-bottom: 12px;
  text-shadow: 0 0 6px rgba(255, 255, 255, 0.3);
}

.options {
  margin-top: 24px;
  width: 100%;
}

.option-card {
  background: rgba(255, 255, 255, 0.05);
  color: #e3e6f0;
  font-size: 15px;
  border-radius: 12px;
  padding: 10px 18px;
  margin-bottom: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  transition: all 0.3s ease-in-out;
  font-family: "Noto Serif SC", serif;
  width: 100%;
  box-sizing: border-box;
  text-align: left;
  line-height: 1.8em;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
