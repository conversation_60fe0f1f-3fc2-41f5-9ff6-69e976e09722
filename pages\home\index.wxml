<view class="container">
  <!-- 背景图 -->
  <image class="background" src="cloud://cloudbase-8gji862jcfb501e7.636c-cloudbase-8gji862jcfb501e7-1365531166/images/homeBG.png" mode="aspectFill" />

  <!-- 顶部主标 -->
  <view class="main-logo" bindtap="toggleLightName">
    <!-- ✅【重要修正】根据你的2.0版本，这里使用的是image标签，而不是IconWeiguang组件 -->
    <image class="weiguang-circle" src="cloud://cloudbase-8gji862jcfb501e7.636c-cloudbase-8gji862jcfb501e7-1365531166/images/IconWeiguang.png" mode="aspectFit" />
  </view>

  <!-- 微光昵称 -->
  <view wx:if="{{showLightName}}" class="light-name">
    您的微光 · {{lightName}}
  </view>

  <!-- 主体内容排布区 -->
  <view class="content">
    <!-- 星光对话 -->
    <view class="icon-block" bindtap="goDialogue" data-target="dialogue">
      <view class="icon-wrapper">
        <IconDialogue class="icon-glow" />
      </view>
      <view class="icon-button">星光对话</view>
    </view>

    <!-- 微光游戏厅 + 星轨日记 -->
    <view class="bottom-icons">
      <view class="icon-block" bindtap="goGamehall" data-target="gamehall">
        <view class="icon-wrapper">
          <IconGamehall class="icon-glow" />
        </view>
        <view class="icon-button">微光游戏厅</view>
      </view>
      <view class="icon-block" bindtap="goDiary" data-target="diary">
        <view class="icon-wrapper">
          <IconDiary class="icon-glow" />
        </view>
        <view class="icon-button">星轨日记</view>
      </view>
    </view>
  </view>

  <!-- ✅【新增】签到页测试入口 -->
  <!-- 使用 navigator 组件进行页面跳转，无需修改 js 文件 -->
  <!-- 我把它放在了帮助按钮的前面，这样在视觉上更符合逻辑 -->
  <navigator class="checkin-entry-icon" url="/pages/checkin/index">✨</navigator>

  <!-- 帮助按钮 -->
  <view wx:if="{{showHelpButton}}" class="help-button" bindtap="onHelpButtonTap">
    <view class="help-icon">🙋</view>
  </view>

  <!-- ✅【新增】新人引导组件 -->
  <welcome-guide
    wx:if="{{showGuide}}"
    user-name="{{userName}}"
    weiguang-name="{{lightName}}"
    elements-info="{{guideElementsInfo}}"
    bind:guideFinished="onGuideFinished"
  ></welcome-guide>
</view>
