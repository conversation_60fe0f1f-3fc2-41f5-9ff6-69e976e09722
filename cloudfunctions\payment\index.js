const crypto = require('crypto');
const fs = require('fs');
const https = require('https');
const axios = require('axios');
const cloud = require('wx-server-sdk');
cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });
const db = cloud.database();

// ===== 微信商户配置 =====
const appid = 'wx4678da2fc1978a86';
const mchid = '1720353587';
const serial_no = '1CAF243926D5F2701381AACD0DF12FF24B653AF8';
const apiV3Key = 'uQ3bTx8Lq9aWv7HZg4PxN1JeKd2Mu5Ft';

// 商品配置
const PRODUCTS = {
  'point_12': {
    price: 100, // 1元，单位分
    name: '12光点充值包'
  },
  'point_30': {
    price: 2800, // 28元，单位分
    name: '30光点充值包'
  },
  'point_50': {
    price: 4500, // 45元，单位分
    name: '50光点充值包'
  },
  'vip_month': {
    price: 6800, // 68元，单位分
    name: '月度会员'
  }
};

// ⚠️ 请确保 cert/key 文件已上传并能被读取
const cert = fs.readFileSync('./apiclient_cert.pem', 'utf8');
const key = fs.readFileSync('./apiclient_key.pem', 'utf8');

function generateNonceStr(length = 32) {
  return crypto.randomBytes(length / 2).toString('hex');
}

function buildSign({ method, url, timestamp, nonceStr, body }) {
  const message = `${method}\n${url}\n${timestamp}\n${nonceStr}\n${body}\n`;
  const sign = crypto.createSign('RSA-SHA256');
  sign.update(message);
  sign.end();
  return sign.sign(key, 'base64');
}

function buildPaySign({ appId, timeStamp, nonceStr, packageStr }) {
  const message = `${appId}\n${timeStamp}\n${nonceStr}\n${packageStr}\n`;
  const sign = crypto.createSign('RSA-SHA256');
  sign.update(message);
  sign.end();
  return sign.sign(key, 'base64');
}

exports.main = async (event, context) => {
  const { OPENID } = cloud.getWXContext();
  const { productId } = event;

  try {
    // 获取商品信息
    const product = PRODUCTS[productId];
    if (!product) {
      return {
        code: -1,
        msg: '商品不存在'
      };
    }

    // 生成订单号
    const outTradeNo = 'ORDER_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

    const url = '/v3/pay/transactions/jsapi';
    const fullUrl = 'https://api.mch.weixin.qq.com' + url;
    const timestamp = Math.floor(Date.now() / 1000).toString();
    const nonceStr = generateNonceStr();

    const requestBody = {
      appid,
      mchid,
      description: product.name,
      out_trade_no: outTradeNo,
      notify_url: 'https://cloudbase-8gji862jcfb501e7-1365531166.ap-shanghai.app.tcloudbase.com/payNotify',
      amount: { total: product.price, currency: 'CNY' },
      payer: { openid: OPENID }
    };

    const bodyStr = JSON.stringify(requestBody);
    const signature = buildSign({
      method: 'POST',
      url,
      timestamp,
      nonceStr,
      body: bodyStr
    });

    const authorization = `WECHATPAY2-SHA256-RSA2048 mchid="${mchid}",nonce_str="${nonceStr}",timestamp="${timestamp}",serial_no="${serial_no}",signature="${signature}"`;

    // 创建订单
    await db.collection('orders').add({
      data: {
        _id: outTradeNo,
        userId: OPENID,
        productId,
        productName: product.name,
        amount: product.price,
        status: 'PENDING',
        createTime: new Date(),
        updateTime: new Date()
      }
    });

    // 调用支付接口
    const result = await axios.post(fullUrl, requestBody, {
      httpsAgent: new https.Agent({ cert, key }),
      headers: {
        'Content-Type': 'application/json',
        Authorization: authorization
      }
    });

    const prepay_id = result.data.prepay_id;
    if (!prepay_id) {
      return {
        code: -1,
        msg: '统一下单成功但未返回prepay_id',
        detail: result.data
      };
    }

    // 返回支付参数
    const packageStr = `prepay_id=${prepay_id}`;
    const payTimeStamp = Math.floor(Date.now() / 1000).toString();
    const payNonceStr = generateNonceStr();
    const paySign = buildPaySign({
      appId: appid,
      timeStamp: payTimeStamp,
      nonceStr: payNonceStr,
      packageStr
    });

    return {
      code: 0,
      data: {
        timeStamp: payTimeStamp,
        nonceStr: payNonceStr,
        package: packageStr,
        signType: 'RSA',
        paySign
      }
    };
  } catch (err) {
    console.error('支付创建失败：', err);
    return {
      code: -1,
      msg: err.message || '支付失败'
    };
  }
}; 