const crypto = require('crypto');
const fs = require('fs');
const https = require('https');
const axios = require('axios');
const cloud = require('wx-server-sdk');
cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV });
const db = cloud.database();

// ===== 微信商户配置 =====
const appid = 'wx4678da2fc1978a86';
const mchid = '1720353587';
const serial_no = '1CAF243926D5F2701381AACD0DF12FF24B653AF8';
const apiV3Key = 'uQ3bTx8Lq9aWv7HZg4PxN1JeKd2Mu5Ft';

// ⚠️ 请确保 cert/key 文件已上传并能被读取
const cert = fs.readFileSync('./apiclient_cert.pem', 'utf8');
const key = fs.readFileSync('./apiclient_key.pem', 'utf8');

function generateNonceStr(length = 32) {
  return crypto.randomBytes(length / 2).toString('hex');
}

function buildSign({ method, url, timestamp, nonceStr, body }) {
  const message = `${method}\n${url}\n${timestamp}\n${nonceStr}\n${body}\n`;
  const sign = crypto.createSign('RSA-SHA256');
  sign.update(message);
  sign.end();
  return sign.sign(key, 'base64');
}

function buildPaySign({ appId, timeStamp, nonceStr, packageStr }) {
  const message = `${appId}\n${timeStamp}\n${nonceStr}\n${packageStr}\n`;
  const sign = crypto.createSign('RSA-SHA256');
  sign.update(message);
  sign.end();
  return sign.sign(key, 'base64');
}

exports.main = async (event) => {
  const { openid, outTradeNo, description, amount, productId } = event;
  if (!openid || !outTradeNo || !description || !amount || !productId) {
    return {
      success: false,
      message: '缺少必要参数：openid, outTradeNo, description, amount, productId'
    };
  }

  const url = '/v3/pay/transactions/jsapi';
  const fullUrl = 'https://api.mch.weixin.qq.com' + url;
  const timestamp = Math.floor(Date.now() / 1000).toString();
  const nonceStr = generateNonceStr();

  const requestBody = {
    appid,
    mchid,
    description,
    out_trade_no: outTradeNo,
    notify_url: 'https://cloudbase-8gji862jcfb501e7-1365531166.ap-shanghai.app.tcloudbase.com/payCallback', // ✅ 替换为你自己的回调地址
    amount: { total: amount, currency: 'CNY' },
    payer: { openid }
  };

  const bodyStr = JSON.stringify(requestBody);
  const signature = buildSign({
    method: 'POST',
    url,
    timestamp,
    nonceStr,
    body: bodyStr
  });

  const authorization = `WECHATPAY2-SHA256-RSA2048 mchid="${mchid}",nonce_str="${nonceStr}",timestamp="${timestamp}",serial_no="${serial_no}",signature="${signature}"`;

  try {
    const result = await axios.post(fullUrl, requestBody, {
      httpsAgent: new https.Agent({ cert, key }),
      headers: {
        'Content-Type': 'application/json',
        Authorization: authorization
      }
    });

    const prepay_id = result.data.prepay_id;
    if (!prepay_id) {
      return {
        success: false,
        message: '统一下单成功但未返回 prepay_id',
        detail: result.data
      };
    }

    // 记录订单，状态暂时为 pending（由微信支付回调更新为 paid）
    const orders = db.collection('orders');
    await orders.add({
      data: {
        _openid: openid,
        orderId: outTradeNo,
        productId,
        amount: amount / 100,
        timestamp: new Date(),
        status: 'pending'
      }
    });

    // ✅ 返回微信支付参数（必须包含以下字段）
    const packageStr = `prepay_id=${prepay_id}`;
    const payTimeStamp = Math.floor(Date.now() / 1000).toString();
    const payNonceStr = generateNonceStr();
    const paySign = buildPaySign({
      appId: appid,
      timeStamp: payTimeStamp,
      nonceStr: payNonceStr,
      packageStr
    });

    return {
      timeStamp: payTimeStamp,
      nonceStr: payNonceStr,
      package: packageStr,
      signType: 'RSA',
      paySign
    };
  } catch (err) {
    return {
      success: false,
      message: '微信支付下单失败',
      error: err.response?.data || err.message
    };
  }
};

