<view class="page-container">
  <!-- 1. 背景层 -->
  <image class="background-image" src="cloud://cloudbase-8gji862jcfb501e7.636c-cloudbase-8gji862jcfb501e7-1365531166/images/homeBG.png" mode="aspectFill"></image>
  <view class="background-overlay"></view>

  <!-- 2. UI层 -->
  <view class="ui-wrapper">
    <!-- 【V5.0 新增】跳过按钮 -->
    <view class="skip-button-wrapper" wx:if="{{skipAvailable && !showOptions}}">
      <view class="skip-button" bindtap="handleSkip">
        <view class="skip-arrow"></view>
        <view class="skip-arrow" style="margin-left: -12rpx;"></view>
      </view>
    </view>

    <!-- 叙事区 -->
    <scroll-view class="narrative-scroll" scroll-y="true" scroll-into-view="{{'line-' + (displayedText.length - 1)}}" scroll-with-animation="true">
      <view class="narrative-area {{textFadeOut ? 'fade-out' : ''}}">
        <block wx:for="{{displayedText}}" wx:key="id">
          <view id="{{'line-' + index}}" class="text-line">{{item.content}}</view>
        </block>
        <view class="scroll-padding-bottom"></view>
      </view>
    </scroll-view>

    <!-- 选择区 -->
    <view class="choice-area-wrapper">
      <view class="choice-area {{showOptions ? 'fade-in' : ''}} {{optionsFadeOut ? 'fade-out' : ''}}">
        <block wx:if="{{showOptions}}">
          <view 
            class="option-button" 
            wx:for="{{options}}" 
            wx:key="index"
            data-next="{{item.next}}"
            data-index="{{index}}"
            bindtap="handleSelectOption"
          >
            <text class="option-main-text">{{item.text}}</text>
            <text class="option-sub-text">{{item.subtext}}</text>
          </view>
        </block>
      </view>
    </view>
  </view>
</view>