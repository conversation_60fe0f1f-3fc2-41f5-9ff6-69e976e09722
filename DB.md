# 微光小程序 - 星光对话系统

## 项目概述
微光是一个基于微信小程序的AI对话和个人成长辅导平台，致力于为用户提供温暖、智能的心灵陪伴。

## 云数据库设计

### 数据库表结构

#### 1. users 用户信息表
| 字段名 | 类型 | 描述 |
|--------|------|------|
| `_openid` | String | 用户唯一标识 (主键) |
| `userName` | String | 用户名 |
| `isVip` | Boolean | VIP状态 |
| `vipExpirationDate` | Date | VIP到期日期 |

#### 2. star_track_diary 星轨日记表
| 字段名 | 类型 | 描述 |
|--------|------|------|
| `_id` | String | 日记唯一标识 (主键) |
| `_openid` | String | 用户标识 (外键) |
| `dialogueTheme` | String | 对话主题 |
| `aiSummary` | String | AI总结 |
| `emotionWord` | String | 情感关键词 |
| `tomorrowMessage` | String | 明日寄语 |
| `createdAt` | Date | 创建时间 |

#### 3. three_questions_records 三问仪式记录表
| 字段名 | 类型 | 描述 |
|--------|------|------|
| `_id` | String | 记录唯一标识 (主键) |
| `_openid` | String | 用户标识 (外键) |
| `dialogueTheme` | String | 对话主题 |
| `question1` | String | 第一个问题 |
| `question2` | String | 第二个问题 |
| `question3` | String | 第三个问题 |
| `createdAt` | Date | 创建时间 |

#### 4. orders 订单表
| 字段名 | 类型 | 描述 |
|--------|------|------|
| `_id` | String | 订单唯一标识 (主键) |
| `_openid` | String | 用户标识 (外键) |
| `orderId` | String | 外部订单号 |
| `productName` | String | 产品名称 |
| `amount` | Number | 订单金额 |
| `status` | String | 订单状态 |
| `createdAt` | Date | 创建时间 |

#### 5. resources 资源表
| 字段名 | 类型 | 描述 |
|--------|------|------|
| `_id` | String | 资源唯一标识 (主键) |
| `name` | String | 资源名称 |
| `price` | Number | 资源价格 |
| `description` | String | 资源描述 |

### 数据库关系设计

```mermaid
erDiagram
    users ||--o{ star_track_diary : "creates"
    users ||--o{ three_questions_records : "generates"
    users ||--o{ orders : "places"
    
    users {
        string _openid PK
        string userName
        boolean isVip
        date vipExpirationDate
    }
    
    star_track_diary {
        string _id PK
        string _openid FK
        string dialogueTheme
        string aiSummary
        string emotionWord
        string tomorrowMessage
        date createdAt
    }
    
    three_questions_records {
        string _id PK
        string _openid FK
        string dialogueTheme
        string question1
        string question2
        string question3
        date createdAt
    }
    
    orders {
        string _id PK
        string _openid FK
        string orderId
        string productName
        number amount
        string status
        date createdAt
    }
    
    resources {
        string _id PK
        string name
        number price
        string description
    }
```

### 设计原则

1. 使用 `_openid` 作为用户唯一标识
2. 表之间通过 `_openid` 建立关联
3. 支持用户 VIP 状态管理
4. 记录用户对话、日记和订单历史

### 性能与安全建议

- 在 `_openid`、`createdAt` 等常用查询字段上添加索引
- 定期清理过期和无效数据
- 实施数据脱敏和访问控制
- 遵守用户数据隐私保护规范

## 技术栈

- 微信小程序
- 云开发
- Gemini AI API
- JavaScript

## 项目特色

- 个性化AI对话
- 星轨日记系统
- VIP会员服务
- 三问仪式
- 智能总结功能 