Page({
  data: {
    lines: [
      "这个名字真好听，我记住了。",
      "你的微光已经被点亮。"
    ],
    visibleLines: [false, false],
    showHalo: false,
    showGlow: false,
    showStars: false,
    showFinalButton: false
  },

  onLoad() {
    const LAG = 3000;
    const timeouts = [];

    this.data.lines.forEach((_, i) => {
      timeouts.push(setTimeout(() => {
        const updated = [...this.data.visibleLines];
        updated[i] = true;
        this.setData({ visibleLines: updated });
      }, i * LAG));
    });

    const totalTextTime = this.data.lines.length * LAG;

    timeouts.push(setTimeout(() => {
      this.setData({ showHalo: true, showGlow: true });
    }, totalTextTime + 500));

    timeouts.push(setTimeout(() => {
      this.setData({ showStars: true });
    }, totalTextTime + 1500));

    timeouts.push(setTimeout(() => {
      this.setData({ showFinalButton: true });
    }, totalTextTime + 9000));
  },

  async onNext() {
    // 获取命名信息
    const lightName = wx.getStorageSync('lightName') || '';
    const userName = wx.getStorageSync('userName') || '';

    // 获取前置七问答案
    const userAnswers = wx.getStorageSync('userAnswers') || [];

    wx.showLoading({ title: '正在保存...' });

    try {
      // 更新用户名称到用户表
      await wx.cloud.callFunction({
        name: 'user',
        data: {
          type: 'updateName',
          lightName,
          userName
        }
      });

      // 如果有前置七问答案，进行层级判定
      if (userAnswers.length > 0) {
        // 将答案文本转换为索引（A=0, B=1, C=2...）
        const answerIndexes = userAnswers.map(answer => {
          if (typeof answer === 'string') {
            // 提取选项字母并转换为索引
            const match = answer.match(/^([A-E])\./);
            if (match) {
              return match[1].charCodeAt(0) - 'A'.charCodeAt(0);
            }
          }
          return 0; // 默认值
        });

        await wx.cloud.callFunction({
          name: 'user',
          data: {
            type: 'judgeLevel',
            answers: answerIndexes
          }
        });

        // 清除本地存储的答案
        wx.removeStorageSync('userAnswers');
      }

      wx.hideLoading();

      wx.redirectTo({
        url: '/pages/home/<USER>'
      });
    } catch (error) {
      console.error('保存用户信息失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '保存失败，请重试',
        icon: 'none'
      });
    }
  }
});
