page {
  background-color: #0c0c0c;
  color: #aaaaaa;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
}

:root {
  --glimmer-gold: #ffd700;
  --glimmer-glow: rgba(255, 215, 0, 0.5);
}

/* --- 2. 页面整体布局 --- */
.page-container {
  position: relative;
  width: 100%;
}

.background-image {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.content-wrapper {
  /* 【V3.13 核心修复】直接在内容容器上增加上边距，替代占位符 */
  padding: calc(88rpx + var(--status-bar-height)) 40rpx 0;
  box-sizing: border-box;
}

/* --- 3. 自定义导航栏 --- */
.custom-nav {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  padding-top: var(--status-bar-height);
  z-index: 10;
  transition: background-color 0.3s ease;
  box-sizing: border-box;
}

.nav-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx; /* 固定内容高度 */
}

/* 左、中、右三栏布局 */
.nav-left,
.nav-right {
  width: 120rpx; /* 给左右两边一个固定的宽度 */
  flex-shrink: 0;
}

.nav-center {
  flex-grow: 1;
  text-align: center;
}

.nav-left {
  display: flex;
  justify-content: flex-start;
}

/* 返回按钮可点击区域 */
.nav-back-button {
  width: 80rpx;
  height: 88rpx; /* 与导航栏内容区等高 */
  display: flex;
  align-items: center;
  justify-content: center;
  padding-left: 20rpx; /* 增加一点内边距，让箭头更靠左 */
}

/* 返回箭头样式 */
.nav-back-arrow {
  color: #ffffff;
  font-size: 48rpx; /* 增大字体，使其更清晰 */
  font-weight: 300; /* 使用细体，更有设计感 */
  line-height: 1; /* 确保垂直居中 */
}

.nav-title {
  color: var(--glimmer-gold);
  font-size: 34rpx;
  font-weight: bold;
  text-shadow: 0 0 16rpx var(--glimmer-glow);
}

/* 【V3.13 核心修复】不再需要占位符样式 */
/* .nav-placeholder { ... } */

/* --- 4. 顶部品牌引导区 --- */
.hero-section {
  padding: 100rpx 0;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.hero-logo-placeholder {
  width: 80rpx;
  height: 80rpx;
  background: var(--glimmer-gold);
  clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
  box-shadow: 0 0 25rpx var(--glimmer-glow);
  margin-bottom: 50rpx;
  animation: subtle-rotate 10s linear infinite;
}
@keyframes subtle-rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.hero-slogan {
  font-size: 32rpx;
  color: #aaaaaa;
  line-height: 1.7;
  letter-spacing: 2rpx;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* --- 5. 内容区块 --- */
.section {
  margin-top: 40rpx;
}
.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}
.section-icon-wrapper {
  width: 72rpx;
  height: 72rpx;
  margin-right: 30rpx;
  flex-shrink: 0;
}
.section-icon {
  width: 100%;
  height: 100%;
  filter: drop-shadow(0 0 12rpx var(--glimmer-glow));
}
.section-title-text {
  display: flex;
  flex-direction: column;
}
.main-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #ffffff;
}
.sub-title {
  font-size: 24rpx;
  color: #aaaaaa;
  margin-top: 8rpx;
}

/* --- 6. 游戏卡片 --- */
.game-card {
  display: flex;
  margin-bottom: 40rpx;
  background: rgba(26, 26, 26, 0.85);
  backdrop-filter: blur(10px);
  border-radius: 24rpx;
  border: 1px solid transparent;
  background-clip: padding-box, border-box;
  background-origin: padding-box, border-box;
  background-image: linear-gradient(
      to right,
      rgba(26, 26, 26, 0.85),
      rgba(26, 26, 26, 0.85)
    ),
    linear-gradient(135deg, rgba(255, 215, 0, 0.4), rgba(255, 215, 0, 0.1));
  overflow: hidden;
  transition: all 0.3s ease;
}
.game-card:active {
  transform: scale(0.98);
  background-image: linear-gradient(
      to right,
      rgba(26, 26, 26, 0.85),
      rgba(26, 26, 26, 0.85)
    ),
    linear-gradient(135deg, rgba(255, 215, 0, 0.7), rgba(255, 215, 0, 0.3));
}
.card-visual {
  width: 220rpx;
  height: 280rpx;
  flex-shrink: 0;
}
.card-info {
  flex: 1;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.card-footer {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-top: 25rpx;
}
.game-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #efefef;
  margin-bottom: 25rpx;
  display: block;
}
.game-slogan {
  font-size: 24rpx;
  color: #aaaaaa;
  line-height: 1.6;
}
.game-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
  width: 100%;
}
.tag {
  padding: 8rpx 16rpx;
  background: #333333;
  border-radius: 30rpx;
  font-size: 22rpx;
  color: #bbbbbb;
}
.game-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-top: 25rpx;
}
.meta-item {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #aaaaaa;
}
.meta-icon {
  margin-right: 10rpx;
  font-size: 28rpx;
}
.meta-item.cost {
  color: var(--glimmer-gold);
}
.cost-number {
  font-weight: bold;
  font-size: 36rpx;
  text-shadow: 0 0 10rpx var(--glimmer-glow);
}
.point-icon {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  margin-right: 12rpx;
  background: linear-gradient(135deg, #fff0a8, #ffd700);
  box-shadow: 0 0 20rpx var(--glimmer-glow);
  transition: box-shadow 0.3s ease;
}
.game-card:active .point-icon {
  box-shadow: 0 0 30rpx rgba(255, 215, 0, 0.9);
}

/* --- 7. 即将开放模块 --- */
.coming-soon-card {
  height: 250rpx;
  border-radius: 24rpx;
  background: rgba(26, 26, 26, 0.85);
  backdrop-filter: blur(10px);
  border: 1rpx dashed rgba(255, 215, 0, 0.2);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  text-align: center;
}
.planet {
  width: 80rpx;
  height: 80rpx;
  background: radial-gradient(circle at 30% 30%, #555, #111);
  border-radius: 50%;
  margin-bottom: 20rpx;
  animation: breathe 4s ease-in-out infinite;
  box-shadow: 0 0 20rpx rgba(170, 170, 170, 0.1);
}
@keyframes breathe {
  0%,
  100% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
}
.coming-soon-main {
  font-size: 30rpx;
  color: #cccccc;
}
.coming-soon-sub {
  font-size: 24rpx;
  color: #888888;
  margin-top: 10rpx;
}

/* --- 8. 页脚 --- */
.page-footer {
  padding: 100rpx 0;
  text-align: center;
  font-size: 24rpx;
  color: #555555;
}
