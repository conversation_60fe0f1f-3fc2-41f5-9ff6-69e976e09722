Page({
  data: {
    userName: '用户', // 用户名
    userOpenid: '', // 用户openid
    userPoints: 0, // 用户光点数量
    userLevel: 3, // 用户层级，默认为3
    userLevelName: '挣扎层', // 用户层级名称
    dialogueStarted: false, // 控制是否开始对话
    dialogueThemes: [
      '工作迷茫',
      '情感困惑',
      '个人成长',
      '人生方向',
      '压力与焦虑',
      '自我认知',
      '梦想与现实',
      '随心而聊'
    ],
    selectedTheme: '', // 选中的主题
    dialogueTheme: '', // 对话主题
    dialogueContent: [], // 对话内容
    typingContent: [], // 用于存储打字效果的内容
    dialogueRound: 1, // 对话轮数
    maxDialogueRound: 4, // 最大轮数（从配置文件加载）

    // 配置相关
    config: {
      maxDialogueRound: 4,
      pointsCost: 6,
      typingSpeed: 50,
      dialogueSettings: {
        enableTypingEffect: true,
        autoScrollThreshold: 2,
        summaryMaxLength: 100
      },
      messages: {
        pointsInsufficient: "光点不足，是否前往光点铺子充值？",
        dialogueFinishing: "对话即将结束，正在生成总结",
        analysisComplete: "分析完成，开始对话"
      }
    },
    aiSummary: '', // AI总结
    inputText: '', // 用户输入
    isInputDisabled: false, // 控制输入是否可用
    scrollToView: '', // 用于控制滚动到底部的视图
    scrollTop: 0, // 用于控制滚动位置
    isTyping: false, // 控制是否正在打字
    isFinishingDialogue: false, // 控制是否正在完成对话
    isWaitingToFinish: false, // 控制是否等待完成打字效果

    // 结束语相关
    showEndingMessage: false, // 是否显示结束语
    endingMessage: '', // 结束语内容
    isGeneratingEnding: false, // 是否正在生成结束语
    endingGenerated: false, // 结束语是否已生成

    // 前置七问相关
    showPreQuestions: false, // 是否显示前置七问
    preQuestions: [
      {
        question: "最近一周，你的心情更像是？",
        options: [
          "A. 阴雨连绵，总觉得压抑沉重",
          "B. 多云转晴，有起伏但还算稳定",
          "C. 晴空万里，大部分时候都很轻松愉快"
        ]
      },
      {
        question: "面对困难时，你通常会？",
        options: [
          "A. 感到焦虑，经常想要逃避",
          "B. 有些紧张，但会努力去面对",
          "C. 保持冷静，相信自己能解决"
        ]
      },
      {
        question: "对于未来，你的感受是？",
        options: [
          "A. 迷茫困惑，不知道方向在哪里",
          "B. 有些不确定，但愿意慢慢探索",
          "C. 充满期待，对未来有清晰的规划"
        ]
      },
      {
        question: "在人际关系中，你觉得自己？",
        options: [
          "A. 经常感到孤独，难以与他人建立深度连接",
          "B. 有几个知心朋友，关系还算和谐",
          "C. 社交能力强，能够轻松与各种人相处"
        ]
      },
      {
        question: "对于自己的能力，你认为？",
        options: [
          "A. 经常怀疑自己，觉得很多事情做不好",
          "B. 有时自信有时怀疑，处于中等水平",
          "C. 对自己很有信心，相信能够胜任大部分挑战"
        ]
      },
      {
        question: "当遇到挫折时，你会？",
        options: [
          "A. 很难从中恢复，情绪低落很久",
          "B. 需要一些时间调整，但最终能够重新振作",
          "C. 能够快速调整心态，从挫折中学习成长"
        ]
      },
      {
        question: "对于生活的整体满意度，你觉得？",
        options: [
          "A. 很不满意，觉得生活缺乏意义和乐趣",
          "B. 一般般，有好有坏，还在寻找平衡",
          "C. 比较满意，觉得生活充实有意义"
        ]
      }
    ],
    currentQuestionIndex: 0, // 当前问题索引
    userAnswers: [], // 用户答案
  },

  onLoad() {
    // 初始化页面
    // 加载配置文件
    this.loadConfig()
    // 获取用户信息
    this.getUserInfo()
    // 获取用户openid
    this.getUserOpenid()
    // 检查用户level状态
    this.checkUserLevel()
  },

  onShow() {
    // 页面显示时刷新光点余额
    this.refreshUserPoints()
    // 检查层级变更
    this.checkAndHandleLevelChange()
  },

  // 刷新用户光点余额
  refreshUserPoints() {
    wx.cloud.callFunction({
      name: 'user',
      data: {
        type: 'getSelf'
      }
    }).then(res => {
      console.log('刷新光点余额', res.result.data)
      if (res.result && res.result.data && res.result.data.points !== undefined) {
        this.setData({
          userPoints: res.result.data.points
        })
        console.log('光点余额已更新为:', res.result.data.points)
      }
    }).catch(err => {
      console.error('刷新光点余额失败', err)
    })
  },

  // 加载配置文件
  loadConfig() {
    try {
      // 从配置模块加载配置
      const config = require('../../utils/dialogueConfig.js')

      this.setData({
        config: config,
        maxDialogueRound: config.maxDialogueRound
      })

      console.log('配置文件加载成功:', config)
    } catch (error) {
      console.error('加载配置文件失败，使用默认配置:', error)
      // 使用默认配置
      console.log('使用默认配置:', this.data.config)
    }
  },

  // 获取用户信息的方法
  getUserInfo() {
    wx.cloud.callFunction({
      name: 'user',
      data: {
        type: 'getSelf'
      }
    }).then(res => {
      console.log('getUserInfo', res.result.data)
      if (res.result && res.result.data) {
        this.setData({
          userName: res.result.data.userName || '用户',
          userPoints: res.result.data.points || 0
        })
      } else {
        this.setData({
          userName: '用户',
          userPoints: 0
        })
        console.warn('获取用户信息失败', res.result)
      }
    }).catch(err => {
      console.error('调用获取用户信息云函数失败', err)
      this.setData({
        userName: '用户',
        userPoints: 0
      })
    })
  },

  // 获取用户openid的方法
  getUserOpenid() {
    wx.cloud.callFunction({
      name: 'getOpenid'
    }).then(res => {
      console.log('getUserOpenid', res.result.openid)
      if (res.result && res.result.openid) {
        this.setData({
          userOpenid: res.result.openid
        })
      }
    }).catch(err => {
      console.error('获取用户openid失败', err)
    })
  },

  // 检查用户level状态
  checkUserLevel() {
    wx.cloud.callFunction({
      name: 'user',
      data: {
        type: 'getLevel'
      }
    }).then(res => {
      console.log('checkUserLevel', res.result)
      if (res.result && res.result.success) {
        const userLevel = res.result.level
        const userLevelName = res.result.levelName
        console.log('用户层级:', userLevel, userLevelName)

        // 更新用户层级信息
        this.setData({
          userLevel: userLevel || 3,
          userLevelName: userLevelName || '挣扎层'
        })

        // 如果level为空或是旧的字符串格式，显示前置七问
        if (!userLevel || typeof userLevel === 'string') {
          this.setData({
            showPreQuestions: true
          })
        }
      } else {
        // 获取用户层级失败，使用默认层级并显示前置七问
        this.setData({
          userLevel: 3,
          userLevelName: '挣扎层',
          showPreQuestions: true
        })
      }
    }).catch(err => {
      console.error('检查用户level失败', err)
      // 出错时使用默认层级并显示前置七问，确保用户能正常使用
      this.setData({
        userLevel: 3,
        userLevelName: '挣扎层',
        showPreQuestions: true
      })
    })
  },

  // 选择前置问题答案
  selectPreAnswer(e) {
    const answerIndex = e.currentTarget.dataset.index
    const updatedAnswers = [...this.data.userAnswers, answerIndex]

    this.setData({
      userAnswers: updatedAnswers
    })

    // 如果还有下一题，继续下一题
    if (this.data.currentQuestionIndex < this.data.preQuestions.length - 1) {
      this.setData({
        currentQuestionIndex: this.data.currentQuestionIndex + 1
      })
    } else {
      // 所有问题回答完毕，提交答案进行层级判定
      this.submitPreAnswers()
    }
  },

  // 提交前置七问答案
  async submitPreAnswers() {
    wx.showLoading({ title: '正在分析...' })

    try {
      // 先进行旧的层级判定（保持兼容性）
      const judgeRes = await wx.cloud.callFunction({
        name: 'user',
        data: {
          type: 'judgeLevel',
          answers: this.data.userAnswers
        }
      })

      if (judgeRes.result && judgeRes.result.success) {
        console.log('旧层级判定成功:', judgeRes.result.level)

        // 基于前置七问结果设置新的数字层级
        const answers = this.data.userAnswers || [];
        let total = 0;
        for (let i = 0; i < answers.length; i++) {
          total += (answers[i] + 1); // A=1分，B=2分，C=3分
        }

        let newLevel = 3; // 默认层级
        let newLevelName = '挣扎层';

        if (total <= 14) {
          newLevel = 2;
          newLevelName = '徘徊层';
        } else if (total <= 17) {
          newLevel = 3;
          newLevelName = '挣扎层';
        } else {
          newLevel = 4;
          newLevelName = '主人翁层';
        }

        // 设置新的数字层级
        const setLevelRes = await wx.cloud.callFunction({
          name: 'user',
          data: {
            type: 'setLevel',
            level: newLevel,
            levelName: newLevelName
          }
        });

        if (setLevelRes.result && setLevelRes.result.success) {
          console.log('新层级设置成功:', newLevel, newLevelName);

          // 更新本地层级信息
          this.setData({
            userLevel: newLevel,
            userLevelName: newLevelName
          });
        }

        wx.hideLoading()

        // 隐藏前置七问，显示对话主题选择
        this.setData({
          showPreQuestions: false
        })

        wx.showToast({
          title: this.data.config.messages.analysisComplete,
          icon: 'success'
        })
      } else {
        console.error('层级判定失败:', judgeRes.result)
        wx.hideLoading()
        wx.showToast({
          title: '分析失败，请重试',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('提交前置七问答案失败:', error)
      wx.hideLoading()
      wx.showToast({
        title: '提交失败，请重试',
        icon: 'none'
      })
    }
  },

  // 选择对话主题
  async selectTheme(e) {
    const theme = e.currentTarget.dataset.theme
    const requiredPoints = this.data.config.pointsCost // 从配置文件读取光点消耗

    // 检查光点是否足够
    if (this.data.userPoints < requiredPoints) {
      wx.showModal({
        title: '光点不足',
        content: `${this.data.config.messages.pointsInsufficient.replace('光点不足', `开始星光对话需要${requiredPoints}个光点，您当前有${this.data.userPoints}个光点`)}`,
        confirmText: '去充值',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            // 跳转到光点铺子
            wx.navigateTo({
              url: '/pages/journal/store/index'
            })
          }
        }
      })
      return
    }

    // 光点足够，扣除光点并开始对话
    wx.showLoading({ title: '正在扣除光点...' })

    try {
      const res = await wx.cloud.callFunction({
        name: 'user',
        data: {
          type: 'consumePoints',
          points: requiredPoints
        }
      })

      wx.hideLoading()

      if (res.result && res.result.success) {
        // 更新本地光点数量
        this.setData({
          userPoints: this.data.userPoints - requiredPoints,
          selectedTheme: theme
        })

        wx.showToast({
          title: `已扣除${requiredPoints}个光点`,
          icon: 'success',
          duration: 1500
        })

        // 延迟一下再开始对话，让用户看到扣除提示
        setTimeout(() => {
          this.startDialogue()
        }, 1500)
      } else {
        wx.showToast({
          title: '光点扣除失败，请重试',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('扣除光点失败:', error)
      wx.hideLoading()
      wx.showToast({
        title: '光点扣除失败，请重试',
        icon: 'none'
      })
    }
  },

  // 开始对话
  async startDialogue() {
    if (!this.data.selectedTheme) {
      wx.showToast({
        title: '请选择对话主题',
        icon: 'none'
      })
      return
    }

    this.setData({
      dialogueStarted: true,
      dialogueTheme: this.data.selectedTheme,
      dialogueContent: [],
    })

    wx.showLoading({ title: '准备对话...' })

    try {
      console.log('=== 开始星光对话 ===')
      console.log('对话主题:', this.data.dialogueTheme)
      console.log('用户openid:', this.data.userOpenid)
      console.log('是否包含历史:', true)

      // 获取用户历史记录作为对话背景
      let userHistoryContext = []
      try {
        const historyRes = await wx.cloud.callFunction({
          name: 'getUserHistory',
          data: { openid: this.data.userOpenid, limit: 5 }
        })
        console.log('历史记录获取结果:', historyRes.result)

        if (historyRes.result.success && historyRes.result.history.length > 0) {
          console.log('找到历史记录数量:', historyRes.result.history.length)

          // 只传递dialogueContent数组，让AI基于具体对话内容提出更精准的问题
          userHistoryContext = historyRes.result.history.map(record => ({
            dialogueContent: record.dialogueContent || [] // 只传递完整的对话内容
          }))

          historyRes.result.history.forEach((record, index) => {
            console.log(`历史记录 ${index + 1}:`, {
              theme: record.theme,
              summary: record.summary?.substring(0, 50) + '...',
              emotionKeyword: record.emotionKeyword,
              createTime: record.createTime
            })
          })
        } else {
          console.log('没有找到历史记录或获取失败')
        }
      } catch (historyError) {
        console.error('获取历史记录失败:', historyError)
      }

      console.log('传递给AI的历史背景:', userHistoryContext)

      // 获取用户信息，确保有正确的lightName和userName
      let lightName = '微光';
      let userName = this.data.userName || '用户';

      try {
        const userInfoRes = await wx.cloud.callFunction({
          name: 'user',
          data: {
            type: 'getSelf'
          }
        });

        if (userInfoRes.result && userInfoRes.result.data) {
          lightName = userInfoRes.result.data.lightName || '微光';
          userName = userInfoRes.result.data.userName || '用户';
        }
      } catch (userInfoError) {
        console.error('获取用户信息失败:', userInfoError);
      }

      const res = await wx.cloud.callFunction({
        name: 'geminiProxy',
        data: {
          userQuestion: `(提示：微光名字=${lightName}， 用户名字=${userName})现在要开始关于"${this.data.dialogueTheme}"的内心对话。请基于我的历史记录，以内心独白的方式提出一个温暖而有关联性的开场问题。要体现对我过往经历的理解和关心，用内心思考的语气，避免"你"的称呼。不超过50个字。`,
          openid: this.data.userOpenid,
          includeHistory: true,
          dialogueContext: [], // 开场问题时传递空数组，让云函数自己获取历史记录
          dialogueTheme: this.data.dialogueTheme,
          userLevel: this.data.userLevel // 传递用户层级参数
        }
      })

      console.log('geminiProxy 云函数调用结果:', res.result)

      wx.hideLoading()

      // 添加更严格的类型检查，兼容不同的响应字段
      const guideMessage = (res.result && res.result.success)
        ? (res.result.reply || res.result.response || `我是你心中的微光，关于${this.data.dialogueTheme}，想和我聊聊什么？`)
        : `我是你心中的微光，关于${this.data.dialogueTheme}，想和我聊聊什么？`

      const trimmedGuideMessage = typeof guideMessage === 'string' ? guideMessage.trim() : guideMessage

      this.setData({
        dialogueContent: [
          { role: 'ai', content: '' }
        ],
        dialogueRound: 1
      })

      // 使用打字效果显示首条AI消息
      this.startTypingEffect(trimmedGuideMessage, 0)
    } catch (error) {
      console.error('AI引导问题生成错误：', error)
      // 兜底处理
      this.setData({
        dialogueContent: [
          { role: 'ai', content: `我是你心中的微光，关于${this.data.dialogueTheme}，想和我聊聊什么？` }
        ],
        dialogueRound: 1
      })

      wx.showToast({
        title: '对话初始化失败',
        icon: 'none'
      })
    }
  },

  // 输入事件处理方法
  onInputChange(e) {
    this.setData({
      inputText: e.detail.value
    })
  },

  // 过滤 Markdown 符号的方法
  filterMarkdownSymbols(text) {
    if (typeof text !== 'string') return text

    // 过滤常见的 Markdown 符号
    return text
      .replace(/\*{1,3}/g, '')     // 粗体、斜体、粗斜体
      .replace(/_{1,3}/g, '')      // 下划线
      .replace(/`{1,3}/g, '')      // 行内代码
      .replace(/~{2}/g, '')        // 删除线
      .replace(/\[([^\]]+)\]\([^\)]+\)/g, '$1')  // 链接
      .replace(/#{1,6}\s*/g, '')   // 标题
      .replace(/>\s*/g, '')        // 引用
      .replace(/\n/g, ' ')         // 换行替换为空格
      .trim()
  },

  // 打字效果方法
  startTypingEffect(text, index) {
    if (this.data.isTyping) return

    this.setData({
      isTyping: true
      // 不要重置 isWaitingToFinish，保持之前的状态
    })

    const typingSpeed = this.data.config.typingSpeed // 从配置文件读取打字速度
    const dialogueContent = this.data.dialogueContent
    const newDialogueContent = [...dialogueContent]

    // 如果是最后一条消息，添加新的 AI 消息
    if (index === dialogueContent.length) {
      newDialogueContent.push({ role: 'ai', content: '' })
    }

    const typeText = () => {
      if (!text || text.length === 0) {
        this.setData({
          dialogueContent: newDialogueContent,
          isTyping: false
        })

        this.updateDialogueScroll()

        // 如果是第7轮对话完成，且等待完成标志为 true，则触发结束语生成
        if (this.data.dialogueRound >= this.data.maxDialogueRound && this.data.isWaitingToFinish && !this.data.endingGenerated && !this.data.isGeneratingEnding) {
          // 隐藏输入框
          this.setData({
            isInputDisabled: true
          })

          // 延迟一下再生成结束语，让用户看到最后的AI回复
          setTimeout(() => {
            // 再次检查是否已经在生成或已生成，防止重复
            if (!this.data.endingGenerated && !this.data.isGeneratingEnding) {
              this.generateEndingMessage()
            }
          }, 1000)
        }

        return
      }

      const currentText = newDialogueContent[index].content
      const nextChar = text[0]

      newDialogueContent[index].content = currentText + nextChar

      this.setData({
        dialogueContent: newDialogueContent
      })

      // 每个字符都滚动，确保用户能看到新内容
      this.updateDialogueScroll()

      // 移除已打印的第一个字符
      text = text.slice(1)

      // 继续打字
      setTimeout(typeText, typingSpeed)
    }

    // 开始打字
    typeText()
  },

  // 发送消息方法
  async sendMessage() {
    const userInput = this.filterMarkdownSymbols(this.data.inputText.trim())

    // 检查输入是否为空
    if (!userInput) {
      wx.showToast({
        title: '请输入消息',
        icon: 'none'
      })
      return
    }

    // 检查是否已达到最大对话轮数且结束语已生成
    if (this.data.dialogueRound >= this.data.maxDialogueRound && this.data.endingGenerated) {
      wx.showToast({
        title: '对话已结束',
        icon: 'none'
      })
      return
    }

    // 禁用输入并清空输入框
    this.setData({
      isInputDisabled: true,
      inputText: ''
    })

    // 更新对话内容，添加用户消息
    const updatedDialogueContent = [...this.data.dialogueContent, { role: 'user', content: userInput }]

    this.setData({
      dialogueContent: updatedDialogueContent
    })

    // 立即滚动到最新消息
    this.updateDialogueScroll()

    // 添加AI思考中的占位消息
    const dialogueWithThinking = [...updatedDialogueContent, {
      role: 'ai',
      content: '',
      isThinking: true
    }]

    this.setData({
      dialogueContent: dialogueWithThinking,
      isAiThinking: true
    })

    // 滚动到思考消息
    this.updateDialogueScroll()

    // 开始思考动画
    this.startThinkingAnimation()

    try {
      // 获取用户信息，确保有正确的lightName和userName
      let lightName = '微光';
      let userName = this.data.userName || '用户';

      try {
        const userInfoRes = await wx.cloud.callFunction({
          name: 'user',
          data: {
            type: 'getSelf'
          }
        });

        if (userInfoRes.result && userInfoRes.result.data) {
          lightName = userInfoRes.result.data.lightName || '微光';
          userName = userInfoRes.result.data.userName || '用户';
        }
      } catch (userInfoError) {
        console.error('获取用户信息失败:', userInfoError);
      }

      const res = await wx.cloud.callFunction({
        name: 'geminiProxy',
        data: {
          userQuestion: `${userInput}`,
          openid: this.data.userOpenid,
          includeHistory: true,
          dialogueContext: updatedDialogueContent,
          dialogueTheme: this.data.dialogueTheme,
          userLevel: this.data.userLevel // 传递用户层级参数
        }
      })

      // 严格的类型和存在性检查，兼容不同的响应字段
      if (res.result && res.result.success) {
        const aiReply = this.filterMarkdownSymbols(res.result.reply || res.result.response || '抱歉，我暂时无法给出回复。')
        const trimmedReply = typeof aiReply === 'string' ? aiReply.trim() : '抱歉，我暂时无法给出回复。'

        const finalDialogueContent = [...updatedDialogueContent, { role: 'ai', content: '' }]

        // 增加对话轮数
        const newDialogueRound = this.data.dialogueRound + 1

        this.setData({
          dialogueContent: finalDialogueContent,
          dialogueRound: newDialogueRound,
          isInputDisabled: newDialogueRound >= this.data.maxDialogueRound // 如果达到最大轮数，保持输入禁用
        })

        // 检查是否达到最大对话轮数
        if (newDialogueRound >= this.data.maxDialogueRound) {
          // 设置等待完成标志，准备生成结束语
          this.setData({
            isWaitingToFinish: true
          })
        }

        // 开始打字效果
        this.startTypingEffect(trimmedReply, finalDialogueContent.length - 1)
      } else {
        // 错误处理
        console.error('AI响应失败:', res.result)

        wx.showToast({
          title: 'AI响应生成失败',
          icon: 'none'
        })

        this.setData({
          isInputDisabled: false
        })
      }
    } catch (error) {
      console.error('发送消息完整错误：', error)
      wx.showToast({
        title: '消息发送失败',
        icon: 'none'
      })
      this.setData({
        isInputDisabled: false
      })
    }
  },

  // 完成对话并跳转到三问仪式 - 已弃用，现在通过结束语流程进入三问
  async finishDialogue() {
    console.warn('finishDialogue 方法已弃用，现在通过结束语流程进入三问仪式')

    // 如果还没有生成结束语，先生成结束语
    if (!this.data.endingGenerated && !this.data.isGeneratingEnding) {
      this.generateEndingMessage()
    } else if (this.data.endingGenerated) {
      // 如果已经有结束语，直接进入三问环节
      this.proceedToThreeQuestions()
    } else {
      // 如果正在生成中，等待生成完成
      console.log('结束语正在生成中，请稍候...')
      wx.showToast({
        title: '结束语生成中，请稍候',
        icon: 'loading'
      })
    }
  },

  // 保存对话数据到localStorage
  saveDialogueDataToStorage(summary) {
    try {
      // 验证数据完整性
      if (!this.data.dialogueContent || this.data.dialogueContent.length === 0) {
        throw new Error('对话内容为空')
      }

      wx.setStorageSync('dialogueContent', this.data.dialogueContent)
      wx.setStorageSync('dialogueTheme', this.data.dialogueTheme || '内心对话')
      wx.setStorageSync('dialogueSummary', summary || '今天的对话很有意义')

      console.log('对话数据已保存到localStorage:', {
        contentLength: this.data.dialogueContent.length,
        theme: this.data.dialogueTheme,
        summaryLength: summary ? summary.length : 0
      })
    } catch (error) {
      console.error('保存对话数据到localStorage失败:', error)
      // 即使保存失败，也要继续流程，但要记录错误
      wx.showToast({
        title: '数据保存异常',
        icon: 'none',
        duration: 1000
      })
    }
  },

  // 跳转到三问仪式页面
  navigateToThreeQuestions(summary) {
    const summaryToUse = summary || '今天的对话很有意义'
    const themeToUse = this.data.dialogueTheme || '内心对话'

    wx.navigateTo({
      url: `/pages/threeQuestions/index?theme=${encodeURIComponent(themeToUse)}&summary=${encodeURIComponent(summaryToUse)}`,
      success: (res) => {
        console.log('成功跳转到三问仪式页面')
        // 重置完成对话状态
        this.setData({
          isFinishingDialogue: false
        })
      },
      fail: (err) => {
        console.error('跳转到三问仪式页面失败:', err)
        this.handleNavigationFailure(err)
      }
    })
  },

  // 处理总结生成失败
  handleSummaryGenerationFailure(errorMessage) {
    console.error('处理总结生成失败:', errorMessage)
    wx.hideLoading()

    // 使用默认总结
    const defaultSummary = `今天关于"${this.data.dialogueTheme}"的对话很有意义，感谢你的分享。`

    // 保存数据并跳转
    this.saveDialogueDataToStorage(defaultSummary)
    this.navigateToThreeQuestions(defaultSummary)

    // 显示用户友好的提示
    wx.showToast({
      title: '正在为你准备三问环节',
      icon: 'success',
      duration: 1500
    })
  },

  // 处理页面跳转失败
  handleNavigationFailure(error) {
    console.error('页面跳转失败处理:', error)

    // 重置状态
    this.setData({
      isFinishingDialogue: false
    })

    // 显示错误提示并提供重试选项
    wx.showModal({
      title: '页面跳转失败',
      content: '无法进入三问环节，是否重试？',
      confirmText: '重试',
      cancelText: '返回',
      success: (res) => {
        if (res.confirm) {
          // 重试跳转，使用更简单的参数
          wx.navigateTo({
            url: `/pages/threeQuestions/index?theme=${encodeURIComponent(this.data.dialogueTheme || '内心对话')}`,
            fail: (retryErr) => {
              console.error('重试跳转仍然失败:', retryErr)
              wx.showToast({
                title: '跳转失败，请稍后重试',
                icon: 'none'
              })
            }
          })
        }
      }
    })
  },

  // 添加导航到三问仪式的方法
  navigateToThreeQuestions() {
    // 生成AI总结
    this.generateAISummary().then(() => {
      wx.navigateTo({
        url: `/pages/threeQuestions/index?theme=${encodeURIComponent(this.data.dialogueTheme)}&summary=${encodeURIComponent(this.data.aiSummary)}`
      })
    })
  },

  // 修改 generateAISummary 方法，使用 filterMarkdownSymbols
  async generateAISummary() {
    try {
      // 获取用户信息，确保有正确的lightName和userName
      let lightName = '微光';
      let userName = this.data.userName || '用户';

      try {
        const userInfoRes = await wx.cloud.callFunction({
          name: 'user',
          data: {
            type: 'getSelf'
          }
        });

        if (userInfoRes.result && userInfoRes.result.data) {
          lightName = userInfoRes.result.data.lightName || '微光';
          userName = userInfoRes.result.data.userName || '用户';
        }
      } catch (userInfoError) {
        console.error('获取用户信息失败:', userInfoError);
      }

      const res = await wx.cloud.callFunction({
        name: 'geminiProxy',
        data: {
          userQuestion: `请为这段关于"${this.data.dialogueTheme}"的对话生成一个温暖、富有洞察力的总结。结合我的成长历程，总结应该充满希望和鼓励。`,
          openid: this.data.userOpenid,
          includeHistory: true,
          dialogueContext: this.data.dialogueContent,
          dialogueTheme: this.data.dialogueTheme,
          userLevel: this.data.userLevel
        }
      })

      if (res.result.success) {
        const aiSummary = this.filterMarkdownSymbols(res.result.reply || '在这段对话中，你展现了内心的勇气和成长。每一个困惑都是通向智慧的桥梁。')
        this.setData({
          aiSummary: aiSummary
        })
      } else {
        this.setData({
          aiSummary: '在这段对话中，你展现了内心的勇气和成长。每一个困惑都是通向智慧的桥梁。'
        })
      }
    } catch (error) {
      console.error('生成AI总结失败', error)
      this.setData({
        aiSummary: '在这段对话中，你展现了内心的勇气和成长。每一个困惑都是通向智慧的桥梁。'
      })
    }
  },

  // 生成快速对话总结用于结束语生成
  generateQuickDialogueSummary() {
    const dialogueContent = this.data.dialogueContent;
    const dialogueTheme = this.data.dialogueTheme;

    // 提取用户的关键表达
    const userMessages = dialogueContent
      .filter(msg => msg.role === 'user')
      .map(msg => msg.content);

    // 提取AI的关键回应
    const aiMessages = dialogueContent
      .filter(msg => msg.role === 'ai')
      .map(msg => msg.content);

    // 构建简要总结
    let quickSummary = `关于"${dialogueTheme}"的对话中，`;

    if (userMessages.length > 0) {
      // 取最后一条用户消息作为核心关注点
      const lastUserMessage = userMessages[userMessages.length - 1];
      const coreContent = lastUserMessage.substring(0, 50);
      quickSummary += `用户主要表达了对${coreContent}的思考，`;
    }

    if (aiMessages.length > 0) {
      // 分析AI回复的情感倾向
      const allAiContent = aiMessages.join(' ');
      let emotionalTone = '温暖支持';

      if (allAiContent.includes('困惑') || allAiContent.includes('迷茫')) {
        emotionalTone = '理解陪伴';
      } else if (allAiContent.includes('勇气') || allAiContent.includes('坚强')) {
        emotionalTone = '鼓励支持';
      } else if (allAiContent.includes('成长') || allAiContent.includes('进步')) {
        emotionalTone = '成长见证';
      }

      quickSummary += `微光以${emotionalTone}的方式进行了回应。`;
    }

    quickSummary += `这是一次关于${dialogueTheme}的深度内心对话。`;

    console.log('生成的快速对话总结:', quickSummary);
    return quickSummary;
  },

  // 构建基于具体对话内容的总结请求
  buildDialogueSummaryRequest() {
    const dialogueContent = this.data.dialogueContent
    const dialogueTheme = this.data.dialogueTheme

    // 提取用户的关键表达
    const userMessages = dialogueContent
      .filter(msg => msg.role === 'user')
      .map(msg => msg.content)

    // 提取AI的关键回应
    const aiMessages = dialogueContent
      .filter(msg => msg.role === 'ai')
      .map(msg => msg.content)

    // 构建包含具体对话内容的总结请求
    let summaryRequest = `请基于我们刚才关于"${dialogueTheme}"的具体对话内容，给我一个深刻而有针对性的总结。\n\n`

    summaryRequest += `【对话要点回顾】\n`

    // 添加用户的主要表达
    if (userMessages.length > 0) {
      summaryRequest += `我在对话中主要表达了：\n`
      userMessages.forEach((msg, index) => {
        summaryRequest += `${index + 1}. "${msg.substring(0, 100)}${msg.length > 100 ? '...' : ''}"\n`
      })
      summaryRequest += `\n`
    }

    // 添加AI的主要洞察
    if (aiMessages.length > 0) {
      summaryRequest += `内心的声音给出了这些洞察：\n`
      aiMessages.slice(0, 2).forEach((msg, index) => {
        summaryRequest += `${index + 1}. "${msg.substring(0, 100)}${msg.length > 100 ? '...' : ''}"\n`
      })
      summaryRequest += `\n`
    }

    summaryRequest += `【总结要求】\n`
    summaryRequest += `1. 必须基于上述具体的对话内容，不要使用通用的总结\n`
    summaryRequest += `2. 要体现出我在这次对话中的真实情感和思考过程\n`
    summaryRequest += `3. 指出我在"${dialogueTheme}"这个主题上的核心困惑或收获\n`
    summaryRequest += `4. 用温暖而有洞察力的语言，帮助我更好地理解自己\n`
    summaryRequest += `5. 结合我的历史成长轨迹，体现出连续性和成长性\n`
    summaryRequest += `6. 总结控制在100字以内，但要深刻而有针对性\n`
    summaryRequest += `7. 用内心独白的方式，避免使用"你"的称呼\n\n`

    summaryRequest += `请给我一个真正基于这次对话内容的个性化总结，而不是通用的鼓励话语。`

    console.log('构建的总结请求:', summaryRequest)
    return summaryRequest
  },

  // 更新对话内容后智能滚动
  updateDialogueScroll() {
    wx.nextTick(() => {
      // 方案1：使用scroll-into-view滚动到最新消息
      const latestIndex = this.data.dialogueContent.length - 1;
      this.setData({
        scrollToView: `msg-${latestIndex}`
      });

      // 方案2：使用scrollTop强制滚动到底部（备用方案）
      setTimeout(() => {
        const query = wx.createSelectorQuery().in(this);
        query.select('.dialogue-content').scrollOffset();
        query.exec((res) => {
          if (res[0]) {
            // 设置一个很大的scrollTop值，确保滚动到底部
            this.setData({
              scrollTop: res[0].scrollHeight + 1000
            });
          }
        });
      }, 50);

      // 方案3：双重确保滚动（针对长消息）
      setTimeout(() => {
        this.setData({
          scrollToView: `msg-${latestIndex}`,
          scrollTop: 999999 // 强制滚动到最底部
        });
      }, 200);
    });
  },

  // 层级变更反馈机制
  async handleLevelChange(newLevel, newLevelName) {
    return
    const oldLevel = this.data.userLevel;
    const oldLevelName = this.data.userLevelName;

    // 如果层级没有变化，直接返回
    if (oldLevel === newLevel) {
      return;
    }

    console.log('检测到层级变更:', `${oldLevel}(${oldLevelName}) -> ${newLevel}(${newLevelName})`);

    // 更新本地层级信息
    this.setData({
      userLevel: newLevel,
      userLevelName: newLevelName
    });

    // 显示层级变更提示
    const levelChangeMessage = this.getLevelChangeMessage(oldLevel, newLevel, oldLevelName, newLevelName);

    wx.showModal({
      title: '内心状态更新',
      content: levelChangeMessage,
      showCancel: false,
      confirmText: '我知道了',
      confirmColor: '#6b7aff',
      success: (res) => {
        if (res.confirm) {
          console.log('用户确认层级变更提示');
        }
      }
    });

    // 如果正在对话中，添加一条系统消息说明层级变更
    if (this.data.dialogueStarted && this.data.dialogueContent.length > 0) {
      const systemMessage = {
        role: 'system',
        content: `你的内心状态从"${oldLevelName}"调整为"${newLevelName}"，我会用更适合的方式陪伴你。`
      };

      const updatedDialogueContent = [...this.data.dialogueContent, systemMessage];
      this.setData({
        dialogueContent: updatedDialogueContent
      });

      // 滚动到最新消息
      this.updateDialogueScroll();
    }
  },

  // 获取层级变更提示消息
  getLevelChangeMessage(oldLevel, newLevel, oldLevelName, newLevelName) {
    const levelDescriptions = {
      1: '需要极致温柔和无条件支持',
      2: '需要鼓励和发现积极火花',
      3: '需要将痛苦转化为行动能量',
      4: '需要深度自我探索和反思',
      5: '需要思想碰撞和创造性激发'
    };

    let message = `你的内心状态从"${oldLevelName}"调整为"${newLevelName}"。\n\n`;

    if (newLevel > oldLevel) {
      message += '这表明你正在成长和进步！';
    } else if (newLevel < oldLevel) {
      message += '这是正常的情感波动，我会给你更多的理解和支持。';
    }

    message += `\n\n在"${newLevelName}"状态下，${levelDescriptions[newLevel]}。我会用更适合的方式陪伴你。`;

    return message;
  },

  // 手动设置用户层级（用于测试或管理）
  async setUserLevel(level, levelName) {
    try {
      const res = await wx.cloud.callFunction({
        name: 'user',
        data: {
          type: 'setLevel',
          level: level,
          levelName: levelName
        }
      });

      if (res.result && res.result.success) {
        console.log('层级设置成功:', res.result);

        // 触发层级变更反馈
        await this.handleLevelChange(level, levelName);

        return {
          success: true,
          message: '层级设置成功'
        };
      } else {
        console.error('层级设置失败:', res.result);
        return {
          success: false,
          message: res.result.message || '层级设置失败'
        };
      }
    } catch (error) {
      console.error('设置用户层级异常:', error);
      return {
        success: false,
        message: '设置层级时发生异常'
      };
    }
  },

  // 检查并处理层级变更（在关键时刻调用）
  async checkAndHandleLevelChange() {
    try {
      const res = await wx.cloud.callFunction({
        name: 'user',
        data: {
          type: 'getLevel'
        }
      });

      if (res.result && res.result.success) {
        const currentLevel = res.result.level;
        const currentLevelName = res.result.levelName;

        // 如果层级发生了变化，触发反馈机制
        if (currentLevel !== this.data.userLevel) {
          await this.handleLevelChange(currentLevel, currentLevelName);
        }
      }
    } catch (error) {
      console.error('检查层级变更失败:', error);
    }
  },

  // 生成结束语 - 增强版错误处理
  async generateEndingMessage() {
    // 防止重复生成
    if (this.data.isGeneratingEnding || this.data.endingGenerated) {
      return;
    }

    console.log('开始生成结束语');

    // 验证对话内容是否存在
    if (!this.data.dialogueContent || this.data.dialogueContent.length === 0) {
      console.error('对话内容为空，无法生成结束语');
      this.handleEndingGenerationFailure('对话内容为空', 'empty_dialogue');
      return;
    }

    console.log('对话内容验证通过，长度:', this.data.dialogueContent.length);

    this.setData({
      isGeneratingEnding: true
    });

    wx.showLoading({
      title: this.data.config.messages.endingGeneration || '正在生成结束语...'
    });

    // 设置超时处理
    const timeoutId = setTimeout(() => {
      if (this.data.isGeneratingEnding) {
        console.log('结束语生成超时，使用备用方案');
        wx.hideLoading();
        this.handleEndingGenerationFailure('生成超时', 'timeout');
      }
    }, 8000); // 8秒超时

    try {
      // 调用云函数生成结束语
      console.log('准备调用结束语生成，对话内容长度:', this.data.dialogueContent.length);
      console.log('对话内容预览:', this.data.dialogueContent.slice(0, 2));

      // 生成一个简要的对话总结用于结束语生成
      const dialogueSummary = this.generateQuickDialogueSummary();

      const res = await wx.cloud.callFunction({
        name: 'geminiProxy',
        data: {
          type: 'generateEndingMessage',
          dialogueContext: this.data.dialogueContent, // 修正参数名
          dialogueTheme: this.data.dialogueTheme,
          dialogueSummary: dialogueSummary, // 添加对话总结
          userLevel: this.data.userLevel,
          openid: this.data.userOpenid
        }
      });

      // 清除超时定时器
      clearTimeout(timeoutId);
      wx.hideLoading();

      if (res.result && res.result.success) {
        const endingMessage = this.filterMarkdownSymbols(res.result.endingMessage || res.result.reply || res.result.response);
        const emotionKeyword = res.result.emotionKeyword || res.result.emotion || '温暖';

        // 验证结束语内容
        if (!endingMessage || endingMessage.trim().length < 5) {
          console.error('结束语内容无效:', endingMessage);
          this.handleEndingGenerationFailure('内容无效', 'invalid_content');
          return;
        }

        console.log('结束语生成成功:', endingMessage);
        console.log('情绪关键词:', emotionKeyword);

        this.setData({
          endingMessage: endingMessage,
          isGeneratingEnding: false,
          endingGenerated: true
        });

        // 记录成功生成的指标
        this.recordEndingGenerationMetrics(true, {
          endingMessage: endingMessage,
          emotionKeyword: emotionKeyword,
          generationTime: res.result.generationTime,
          fallback: res.result.fallback || false
        });

        // 显示结束语和引导
        this.showEndingAndGuide();
      } else {
        console.error('结束语生成失败:', res.result);
        this.handleEndingGenerationFailure('AI生成失败', 'ai_generation_failed', res.result);
      }
    } catch (error) {
      // 清除超时定时器
      clearTimeout(timeoutId);
      wx.hideLoading();

      console.error('生成结束语异常:', error);

      // 根据错误类型进行分类处理
      let errorType = 'unknown_error';
      if (error.message && error.message.includes('timeout')) {
        errorType = 'network_timeout';
      } else if (error.message && error.message.includes('network')) {
        errorType = 'network_error';
      } else if (error.message && error.message.includes('function')) {
        errorType = 'cloud_function_error';
      }

      this.handleEndingGenerationFailure(error.message || '系统异常', errorType, error);
    }
  },

  // 处理结束语生成失败的统一方法
  handleEndingGenerationFailure(errorMessage, errorType, errorDetails = null) {
    console.log('处理结束语生成失败:', errorMessage, errorType);

    // 记录失败指标
    this.recordEndingGenerationMetrics(false, {
      errorMessage,
      errorType,
      errorDetails,
      fallbackUsed: true
    });

    // 使用备用结束语
    this.useFallbackEndingMessage(errorType);
  },

  // 记录结束语生成指标
  recordEndingGenerationMetrics(success, details) {
    try {
      const metrics = {
        timestamp: new Date().toISOString(),
        success: success,
        userLevel: this.data.userLevel,
        dialogueTheme: this.data.dialogueTheme,
        dialogueLength: this.data.dialogueContent ? this.data.dialogueContent.length : 0,
        openid: this.data.userOpenid,
        ...details
      };

      console.log('结束语生成指标:', metrics);

      // 可以在这里调用分析云函数记录指标
      // wx.cloud.callFunction({
      //   name: 'tieringAnalytics',
      //   data: {
      //     type: 'recordEndingGenerationMetrics',
      //     data: metrics
      //   }
      // }).catch(err => {
      //   console.error('记录结束语指标失败:', err)
      // })
    } catch (error) {
      console.error('记录结束语指标异常:', error);
    }
  },

  // 使用备用结束语 - 增强版错误处理，使用智能选择
  useFallbackEndingMessage(errorType = 'unknown') {
    console.log('使用备用结束语，错误类型:', errorType);

    let fallbackResult;
    let fallbackMessage;

    try {
      // 使用配置文件中的智能备用选择函数
      const aiPrompts = require('../../config/aiPrompts.js');
      fallbackResult = aiPrompts.endingMessage.selectFallbackMessage(
        this.data.dialogueTheme,
        this.data.userLevel,
        errorType
      );

      fallbackMessage = fallbackResult.message;
      console.log('智能选择备用结束语结果:', fallbackResult);
    } catch (selectionError) {
      console.error('智能选择备用结束语失败:', selectionError);

      // 最终紧急兜底方案
      const emergencyFallbacks = [
        '感谢你的分享，我会一直在这里陪伴你。在结束前，我还想问你几个小小的问题，好吗？',
        '你的勇气很珍贵，我会一直支持你。在结束前，我还想问你几个小小的问题，好吗？',
        '这次对话很有意义，我陪你继续前行。在结束前，我还想问你几个小小的问题，好吗？'
      ];

      fallbackMessage = emergencyFallbacks[Math.floor(Math.random() * emergencyFallbacks.length)];
      fallbackResult = {
        message: fallbackMessage,
        source: 'emergency_local',
        error: selectionError.message
      };
    }

    // 验证备用结束语的有效性
    if (!fallbackMessage || fallbackMessage.trim().length < 10) {
      console.error('备用结束语无效:', fallbackMessage);
      fallbackMessage = '感谢你的分享，我会一直在这里陪伴你。在结束前，我还想问你几个小小的问题，好吗？';
      fallbackResult.source = 'emergency_hardcoded';
    }

    console.log('最终选择的备用结束语:', {
      source: fallbackResult.source,
      theme: fallbackResult.theme,
      level: fallbackResult.level,
      message: fallbackMessage.substring(0, 50) + '...'
    });

    this.setData({
      endingMessage: fallbackMessage,
      isGeneratingEnding: false,
      endingGenerated: true
    });

    // 根据错误类型显示不同的提示信息
    const errorMessages = {
      'timeout': '结束语生成超时，已使用备用内容',
      'network_timeout': '网络超时，已使用备用内容',
      'network_error': '网络错误，已使用备用内容',
      'ai_generation_failed': 'AI生成失败，已使用备用内容',
      'invalid_content': '生成内容无效，已使用备用内容',
      'cloud_function_error': '云函数错误，已使用备用内容',
      'unknown_error': '生成失败，已使用备用内容'
    };

    const errorMessage = errorMessages[errorType] || errorMessages['unknown_error'];

    // 在开发环境显示详细错误信息，生产环境保持静默
    if (wx.getSystemInfoSync().platform === 'devtools') {
      wx.showToast({
        title: `${errorMessage} (${fallbackResult.source})`,
        icon: 'none',
        duration: 2000
      });
    } else {
      // 生产环境不显示错误提示，直接显示结束语
      console.log('生产环境，不显示错误提示');
    }

    // 记录备用方案使用情况
    this.recordFallbackUsage(errorType, fallbackResult);

    // 显示结束语和引导
    this.showEndingAndGuide();
  },

  // 记录备用方案使用情况
  recordFallbackUsage(errorType, fallbackResult) {
    try {
      const usageRecord = {
        timestamp: new Date().toISOString(),
        errorType: errorType,
        fallbackSource: fallbackResult.source,
        dialogueTheme: this.data.dialogueTheme,
        userLevel: this.data.userLevel,
        openid: this.data.userOpenid,
        dialogueLength: this.data.dialogueContent ? this.data.dialogueContent.length : 0,
        theme: fallbackResult.theme,
        level: fallbackResult.level,
        error: fallbackResult.error
      };

      console.log('备用方案使用记录:', usageRecord);

      // 可以在这里调用分析云函数记录备用方案使用情况
      // wx.cloud.callFunction({
      //   name: 'tieringAnalytics',
      //   data: {
      //     type: 'recordFallbackUsage',
      //     data: usageRecord
      //   }
      // }).catch(err => {
      //   console.error('记录备用方案使用失败:', err)
      // })
    } catch (error) {
      console.error('记录备用方案使用异常:', error);
    }
  },

  // 显示结束语和引导
  showEndingAndGuide() {
    console.log('显示结束语和引导');

    // 只使用独立的结束语显示区域，不添加到对话内容中
    // 这样避免重复显示结束语
    this.setData({
      showEndingMessage: true
    });

    // 滚动到最新消息
    this.updateDialogueScroll();
  },

  // 进入三问环节
  proceedToThreeQuestions() {
    console.log('用户选择进入三问环节')

    // 数据完整性验证
    if (!this.data.dialogueContent || this.data.dialogueContent.length === 0) {
      console.error('对话数据不完整，无法进入三问环节')
      wx.showToast({
        title: '对话数据不完整',
        icon: 'none'
      })
      return
    }

    // 验证对话主题
    const themeToUse = this.data.dialogueTheme || '内心对话'
    const summaryToUse = this.data.endingMessage || '今天的对话很有意义'

    console.log('准备跳转到三问页面，传递数据:', {
      theme: themeToUse,
      summary: summaryToUse,
      dialogueContentLength: this.data.dialogueContent.length
    })

    // 保存完整对话数据到localStorage，确保三问页面能够访问
    this.saveDialogueDataForThreeQuestions(summaryToUse)

    // 跳转到三问仪式页面
    this.navigateToThreeQuestionsFromEnding(themeToUse, summaryToUse)
  },

  // 为三问环节保存对话数据 - 增强版数据验证和传递
  saveDialogueDataForThreeQuestions(summary) {
    try {
      console.log('\n=== 保存三问环节数据 ===')

      // 验证对话内容完整性
      if (!this.data.dialogueContent || this.data.dialogueContent.length === 0) {
        throw new Error('对话内容为空')
      }

      // 验证对话内容结构
      const validatedContent = this.validateAndCleanDialogueContent(this.data.dialogueContent)
      if (validatedContent.length === 0) {
        throw new Error('对话内容验证失败')
      }

      // 验证主题
      const validatedTheme = this.data.dialogueTheme && this.data.dialogueTheme.trim().length > 0
        ? this.data.dialogueTheme.trim()
        : '内心对话'

      // 验证总结内容
      const validatedSummary = summary && summary.trim().length > 0
        ? summary.trim()
        : this.data.endingMessage || '今天的对话很有意义'

      // 保存完整的对话数据
      wx.setStorageSync('dialogueContent', validatedContent)
      wx.setStorageSync('dialogueTheme', validatedTheme)
      wx.setStorageSync('endingMessage', this.data.endingMessage || validatedSummary)
      wx.setStorageSync('dialogueSummary', validatedSummary)

      // 保存扩展的元数据，确保三问页面有足够的上下文
      const metadata = {
        userLevel: this.data.userLevel || 3,
        userLevelName: this.data.userLevelName || '挣扎层',
        userOpenid: this.data.userOpenid,
        userName: this.data.userName || '用户',
        dialogueRound: this.data.dialogueRound || 0,
        maxDialogueRound: this.data.maxDialogueRound || 4,
        createTime: new Date().toISOString(),
        dataVersion: '1.0' // 用于未来的数据格式兼容性
      }

      wx.setStorageSync('dialogueMetadata', metadata)

      // 验证保存结果
      const savedContent = wx.getStorageSync('dialogueContent')
      const savedTheme = wx.getStorageSync('dialogueTheme')
      const savedSummary = wx.getStorageSync('dialogueSummary')

      if (!savedContent || !savedTheme || !savedSummary) {
        throw new Error('数据保存验证失败')
      }

      console.log('✓ 三问环节数据保存成功:', {
        contentLength: validatedContent.length,
        theme: validatedTheme,
        summaryLength: validatedSummary.length,
        userLevel: metadata.userLevel,
        userLevelName: metadata.userLevelName,
        dataVersion: metadata.dataVersion
      })

      return {
        success: true,
        contentLength: validatedContent.length,
        theme: validatedTheme,
        summary: validatedSummary
      }

    } catch (error) {
      console.error('❌ 保存三问环节数据失败:', error)

      // 尝试保存最小必要数据作为备用方案
      try {
        const fallbackData = {
          dialogueContent: [
            { role: 'ai', content: `我是你心中的微光，关于${this.data.dialogueTheme || '内心对话'}，想和我聊聊什么？` },
            { role: 'user', content: '今天的对话很有意义' }
          ],
          dialogueTheme: this.data.dialogueTheme || '内心对话',
          dialogueSummary: '今天的对话很有意义',
          dialogueMetadata: {
            userLevel: this.data.userLevel || 3,
            userOpenid: this.data.userOpenid,
            createTime: new Date().toISOString(),
            fallback: true
          }
        }

        Object.keys(fallbackData).forEach(key => {
          wx.setStorageSync(key, fallbackData[key])
        })

        console.log('✓ 备用数据保存成功')

        wx.showToast({
          title: '数据保存异常，已使用备用方案',
          icon: 'none',
          duration: 2000
        })

        return {
          success: true,
          fallback: true,
          error: error.message
        }

      } catch (fallbackError) {
        console.error('❌ 备用数据保存也失败:', fallbackError)

        wx.showToast({
          title: '数据保存失败，可能影响三问体验',
          icon: 'none',
          duration: 2000
        })

        return {
          success: false,
          error: error.message,
          fallbackError: fallbackError.message
        }
      }
    }
  },

  // 验证和清理对话内容
  validateAndCleanDialogueContent(dialogueContent) {
    if (!Array.isArray(dialogueContent)) {
      console.warn('对话内容不是数组，尝试修复')
      return []
    }

    const cleanedContent = []

    dialogueContent.forEach((message, index) => {
      // 验证消息结构
      if (!message || typeof message !== 'object') {
        console.warn(`消息${index}结构无效，跳过`)
        return
      }

      // 验证角色
      if (!message.role || !['user', 'ai', 'system'].includes(message.role)) {
        console.warn(`消息${index}角色无效: ${message.role}，尝试修复`)
        message.role = index % 2 === 0 ? 'ai' : 'user'
      }

      // 验证内容
      if (typeof message.content !== 'string') {
        console.warn(`消息${index}内容无效，尝试修复`)
        message.content = '消息内容异常'
      }

      // 清理内容
      const cleanedMessage = {
        role: message.role,
        content: message.content.trim()
      }

      // 过滤掉空消息和思考状态消息
      if (cleanedMessage.content.length > 0 && !message.isThinking) {
        cleanedContent.push(cleanedMessage)
      }
    })

    console.log(`对话内容验证完成: ${dialogueContent.length} -> ${cleanedContent.length}`)
    return cleanedContent
  },

  // 从结束语流程跳转到三问页面
  navigateToThreeQuestionsFromEnding(theme, summary) {
    wx.navigateTo({
      url: `/pages/threeQuestions/index?theme=${encodeURIComponent(theme)}&summary=${encodeURIComponent(summary)}&fromEnding=true`,
      success: (res) => {
        console.log('成功跳转到三问仪式页面')
      },
      fail: (err) => {
        console.error('跳转到三问仪式页面失败:', err)
        this.handleThreeQuestionsNavigationFailure(err, theme, summary)
      }
    })
  },

  // 处理三问页面跳转失败
  handleThreeQuestionsNavigationFailure(error, theme, summary) {
    console.error('三问页面跳转失败处理:', error)

    wx.showModal({
      title: '页面跳转失败',
      content: '无法进入三问环节，是否重试？',
      confirmText: '重试',
      cancelText: '返回',
      success: (res) => {
        if (res.confirm) {
          // 重试跳转，使用更简单的参数
          wx.navigateTo({
            url: `/pages/threeQuestions/index?theme=${encodeURIComponent(theme)}`,
            success: () => {
              console.log('重试跳转成功')
            },
            fail: (retryErr) => {
              console.error('重试跳转仍然失败:', retryErr)
              wx.showToast({
                title: '跳转失败，请稍后重试',
                icon: 'none'
              })
            }
          })
        } else {
          // 用户选择返回，可以考虑其他操作
          console.log('用户选择不重试三问环节')
        }
      }
    })
  },

  // 跳过三问环节
  skipThreeQuestions() {
    console.log('用户选择跳过三问环节')

    wx.showModal({
      title: '确认跳过',
      content: '确定要跳过对话回顾吗？这是一个很好的自我反思机会哦～',
      confirmText: '确定跳过',
      cancelText: '再想想',
      success: (res) => {
        if (res.confirm) {
          // 用户确认跳过，返回首页或其他页面
          wx.showToast({
            title: '期待下次与你的对话',
            icon: 'success',
            duration: 2000
          })

          setTimeout(() => {
            wx.navigateBack({
              delta: 1,
              fail: () => {
                // 如果无法返回，则跳转到首页
                wx.switchTab({
                  url: '/pages/home/<USER>'
                })
              }
            })
          }, 2000)
        }
        // 如果用户选择"再想想"，则不做任何操作，让用户重新考虑
      }
    })
  },

  // 开始思考动画
  startThinkingAnimation() {
    if (!this.data.isAiThinking) return

    const thinkingTexts = ['正在思考', '正在思考.', '正在思考..', '正在思考...']
    let currentIndex = 0

    const animateThinking = () => {
      if (!this.data.isAiThinking) return

      const dialogueContent = [...this.data.dialogueContent]
      const lastIndex = dialogueContent.length - 1

      if (lastIndex >= 0 && dialogueContent[lastIndex].isThinking) {
        dialogueContent[lastIndex].content = thinkingTexts[currentIndex]

        this.setData({
          dialogueContent: dialogueContent
        })

        currentIndex = (currentIndex + 1) % thinkingTexts.length

        // 继续动画
        setTimeout(animateThinking, 500)
      }
    }

    // 开始动画
    animateThinking()
  },

  // 停止思考动画
  stopThinkingAnimation() {
    this.setData({
      isAiThinking: false
    })
  }
})

