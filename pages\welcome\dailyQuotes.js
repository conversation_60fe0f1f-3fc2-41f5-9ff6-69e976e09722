const dailyQuotes = [
  "脑子今天拒绝处理任何信息，它说它要请假，去山里当个石头。",
  "闹钟一响，灵魂往回缩了三米，身体还在床上演抓捕现场。",
  "心事攒久了，就像窗边积了太多灰，随便一阵风都能吹起来打个喷嚏。",
  "想说话的时候，字却在嘴巴里溜冰，滑倒了几次，没站起来。",
  "昨天在角落里留了句话，今天路过时，它还蹲在那里没走。",
  "脑袋像被装了旧电池，能动但总慢半拍，像在回忆里卡顿。",
  "鞋带明明系好了，却还是走丢了一点安全感。",
  "喉咙里卡着一句话，不疼，就是一直没咽下去。",
  "思绪今天变成了一只猫，窝在窗边，一动不动地晒太阳，连老鼠梦都没做。",
  "外面的宇宙静得像一张纸，但心里突然翻了个跟头，不知道踩到了哪根神经。",
  "心里有个小念头，昨天还够不着阳光，今天踮脚了。",
  "有些话不说也没事，过一阵就像雾一样自己散了，真的。",
  "熬夜不是困，是不想被明天抓住。",
  "这颗糖有点苦，偏偏包装写着“希望”。",
  "窗户没锁，风却没有进来，大概它也在门外犹豫了一阵子。",
  "镜子太干净了，连昨天藏起来的表情也映了出来。",
  "那条路今天特别窄，可能是情绪偷偷长胖了。",
  "鸡蛋煮太久，壳裂了一圈，像在自己鼓掌庆祝煮废了。",
  "点的是煎饺，送来的是汤圆，一口咬下去，连误会都甜得没脾气。",
  "起床摁了三次闹钟，第四次干脆把手机摁进了梦里，梦开始无限续杯。",
  "袜子刚套上，脚就开始想念昨天在阳台自由伸展的日子。",
  "被子掀开三秒钟，身体跟灵魂进入冷战期，谁都不想先下床。",
  "电动牙刷刚刷三秒就没电了，牙齿还站在原地等指令，这气氛比开会还凝重。",
  "风今天绕远路了，可能是怕惊动谁的心事。",
  "茶凉得刚好，像是专门等了一会儿不被喝掉。",
  "雨没有很大，但每滴都像轻轻地碰了一下屋檐。",
  "沙发陷了一小块，大概是刚才那段沉默坐过。",
  "阳光照在桌角那一点灰上，连不动的东西都在发光。",
  "午后的时间变软了，像在等一个没来的念头。",
  "地板有点冷，刚好可以安放今天这一点点不想动。",
  "书翻到一页没看完，就在那里陪着没看完的心情。",
  "好像今天什么也没做，但夜晚还是按时落下来了。",
  "窗帘没拉紧，光从缝隙里溜进来，像在悄悄看屋子里的安静。",
  "那条毛巾还搭在椅背上，好像洗完之后也不急着干。",
  "一口水没喝完，杯子就这样半满地陪着今天的心情。",
  "有些天就是这样，不需要发生什么，也觉得刚刚好。",
  "枕头还留着一点褶皱，好像梦还没完全离开。",
  "水龙头关紧了，但好像还有一滴水在犹豫要不要掉下来。",
  "闹钟停了很久，时间却还在慢慢地走，像没人催也无所谓。",
  "桌上那颗糖还没吃，像是留给一个还没想起来的心情。"
];

module.exports = dailyQuotes;
