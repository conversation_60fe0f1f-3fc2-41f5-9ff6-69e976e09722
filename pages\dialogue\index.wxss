page {
  height: 100vh;
  overflow: hidden;
}

.dialogue-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  position: relative;
}

.background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

/* 前置七问阶段 */
.pre-questions-container {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  padding: 40rpx;
  background-color: rgba(0, 0, 0, 0.6);
}

.pre-questions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 60rpx;
}

.pre-questions-title {
  font-size: 36rpx;
  font-weight: bold;
  color: white;
  flex: 1;
}

.pre-questions-progress {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  background-color: rgba(255, 255, 255, 0.1);
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
}

.pre-question-content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.pre-question-text {
  font-size: 40rpx;
  font-weight: bold;
  color: white;
  text-align: center;
  margin-bottom: 80rpx;
  line-height: 1.4;
}

.pre-options-container {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.pre-option-item {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 30rpx;
  border-radius: 20rpx;
  font-size: 28rpx;
  line-height: 1.4;
  transition: all 0.3s ease;
  cursor: pointer;
}

.pre-option-item:active {
  background-color: rgba(255, 255, 255, 0.3);
  transform: scale(0.98);
}

/* 主题选择阶段 */
.theme-selection-container {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  background-color: rgba(0, 0, 0, 0.6);
}

.points-display {
  position: absolute;
  top: 40rpx;
  right: 40rpx;
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  padding: 20rpx;
  backdrop-filter: blur(10rpx);
}

.points-text {
  color: #fff;
  font-size: 28rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 5rpx;
}

.points-cost {
  color: rgba(255, 255, 255, 0.7);
  font-size: 22rpx;
  display: block;
}

.theme-title {
  font-size: 40rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 60rpx;
  text-align: center;
}

.theme-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 30rpx;
  width: 100%;
  max-width: 800rpx;
}

.theme-item {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #fff;
  padding: 12rpx 24rpx;
  margin: 10rpx;
  border-radius: 30rpx;
  transition: all 0.3s ease;
}

.theme-item.selected {
  background-color: rgba(255, 255, 255, 0.3);
  color: #000;
  transform: scale(1.05);
}

.theme-start-btn {
  margin-top: 60rpx;
  background-color: #6b7aff;
  color: white;
  padding: 25rpx 80rpx;
  border-radius: 50rpx;
  font-weight: bold;
  font-size: 32rpx;
  box-shadow: 0 10rpx 20rpx rgba(107, 122, 255, 0.3);
}

/* 对话主界面 */
.dialogue-main {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

.dialogue-header {
  padding: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.1);
}

.dialogue-header-left {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.dialogue-theme {
  font-size: 36rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 5rpx;
}

.user-level-display {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  background-color: rgba(255, 255, 255, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dialogue-round {
  font-size: 28rpx;
  opacity: 0.8;
  color: white;
}

.dialogue-content {
  flex-grow: 1;
  overflow-y: scroll;
  padding: 20rpx;
  padding-bottom: 300rpx; /* 增加底部内边距，确保能完全避开输入框 */
  display: flex;
  flex-direction: column;
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
  box-sizing: border-box;
}

.dialogue-message-wrapper {
  width: 100%;
  display: flex;
  margin-bottom: 20rpx;
}

.ai-message-wrapper {
  flex-direction: row;
}

.user-message-wrapper {
  flex-direction: row-reverse;
}

.dialogue-avatar {
  flex-shrink: 0;
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
}

.ai-avatar {
  background-color: #6b7aff;
  color: white;
}

.user-avatar {
  background-color: #07c160;
  color: white;
}

.dialogue-message {
  flex-grow: 1;
  padding: 20rpx;
  border-radius: 20rpx;
  max-width: 70%;
  word-break: break-word;
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
}

.ai-message {
  background-color: white;
  color: #333;
  margin-right: auto;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
}

.user-message {
  background-color: #07c160;
  color: white;
  margin-left: auto;
  box-shadow: 0 4rpx 10rpx rgba(7, 193, 96, 0.3);
}

.system-message {
  background-color: rgba(255, 255, 255, 0.05);
  color: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
  font-size: 24rpx;
  font-style: italic;
  margin: 20rpx auto;
  max-width: 80%;
}

.dialogue-input-container {
  position: fixed;
  bottom: 30rpx; /* 提升位置，不贴底部 */
  left: 20rpx;
  right: 20rpx;
  display: flex;
  align-items: center;
  padding: 25rpx 30rpx; /* 增加内边距 */
  background-color: rgba(0, 0, 0, 0.8); /* 增加背景透明度 */
  border-radius: 50rpx; /* 添加圆角 */
  backdrop-filter: blur(10rpx); /* 添加毛玻璃效果 */
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3); /* 添加阴影 */
  z-index: 100;
}

.dialogue-input {
  flex-grow: 1;
  background-color: rgba(255, 255, 255, 0.9); /* 提高背景不透明度 */
  color: #333; /* 改为深色文字 */
  padding: 20rpx 30rpx; /* 增加内边距 */
  border-radius: 40rpx; /* 增加圆角 */
  margin-right: 20rpx;
  font-size: 32rpx; /* 增加字体大小 */
  height: 80rpx; /* 设置固定高度 */
  line-height: 80rpx; /* 垂直居中 */
  border: none;
  outline: none;
}

.dialogue-input::placeholder {
  color: #999; /* 占位符颜色 */
}

.send-btn {
  width: 90rpx; /* 稍微增大 */
  height: 90rpx;
  background-color: #6b7aff;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0;
  margin: 0;
  box-shadow: 0 4rpx 16rpx rgba(107, 122, 255, 0.4); /* 添加阴影 */
  transition: all 0.2s ease; /* 添加过渡效果 */
}

.send-btn:active {
  transform: scale(0.95); /* 点击时缩放效果 */
}

.send-btn:disabled {
  background-color: #b0b8ff;
  color: rgba(255, 255, 255, 0.7);
}

/* 三问仪式弹窗 */
.three-questions-modal {
  display: none;
}

/* 新增普通页面样式 */
.three-questions-result {
  padding: 20rpx;
  background-color: #f4f4f4;
  min-height: 100vh;
}

.result-container {
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin: 20rpx 0;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

.result-title {
  text-align: center;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.ai-summary,
.emotion-input,
.tomorrow-message {
  margin-bottom: 30rpx;
}

.summary-label,
.input-label {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.summary-text {
  font-size: 32rpx;
  line-height: 1.6;
  color: #333;
}

.emotion-input input,
.tomorrow-message textarea {
  background-color: #f9f9f9;
  border-radius: 10rpx;
  padding: 15rpx;
  font-size: 28rpx;
}

.result-actions {
  text-align: center;
  margin-top: 40rpx;
}

.save-btn {
  background-color: #6d9eeb;
  color: white;
  width: 60%;
  border-radius: 50rpx;
}

@font-face {
  font-family: "iconfont";
  src: url("https://at.alicdn.com/t/c/font_4308208_z4yfon8mwf.ttf")
    format("truetype");
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 48rpx;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-send:before {
  content: "\e601";
}

/* 结束语显示区域 */
.ending-message-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0.8) 0%,
    rgba(0, 0, 0, 0.95) 100%
  );
  backdrop-filter: blur(20rpx);
  padding: 40rpx 30rpx 60rpx;
  border-radius: 40rpx 40rpx 0 0;
  box-shadow: 0 -10rpx 40rpx rgba(0, 0, 0, 0.3);
  z-index: 200;
  animation: slideUp 0.4s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.ending-message-content {
  display: flex;
  align-items: flex-start;
  margin-bottom: 40rpx;
}

.ending-message-avatar {
  flex-shrink: 0;
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #6b7aff 0%, #9c88ff 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  box-shadow: 0 8rpx 20rpx rgba(107, 122, 255, 0.3);
}

.avatar-text {
  color: white;
  font-size: 24rpx;
  font-weight: bold;
}

.ending-message-bubble {
  flex: 1;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 25rpx 25rpx 25rpx 8rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10rpx);
  position: relative;
}

.ending-message-bubble::before {
  content: "";
  position: absolute;
  left: -8rpx;
  bottom: 20rpx;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 15rpx 15rpx 0;
  border-color: transparent rgba(255, 255, 255, 0.95) transparent transparent;
}

.ending-generating {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
}

.generating-text {
  color: #666;
  font-size: 28rpx;
  margin-bottom: 20rpx;
  text-align: center;
}

.generating-dots {
  display: flex;
  gap: 8rpx;
}

.dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background-color: #6b7aff;
  animation: dotPulse 1.4s infinite ease-in-out;
}

.dot1 {
  animation-delay: -0.32s;
}

.dot2 {
  animation-delay: -0.16s;
}

@keyframes dotPulse {
  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.ending-message-text {
  color: #333;
  font-size: 32rpx;
  line-height: 1.6;
  text-align: left;
  font-weight: 400;
  letter-spacing: 0.5rpx;
}

.ending-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30rpx;
}

.ending-guide-text {
  text-align: center;
  padding: 0 20rpx;
}

.ending-guide-text text {
  color: rgba(255, 255, 255, 0.9);
  font-size: 28rpx;
  line-height: 1.4;
  font-weight: 300;
}

.ending-buttons {
  display: flex;
  gap: 20rpx;
  width: 100%;
  max-width: 500rpx;
}

.ending-btn {
  flex: 1;
  padding: 25rpx 30rpx;
  border-radius: 50rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.1);
}

.ending-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.15);
}

.ending-btn-secondary {
  background-color: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: rgba(255, 255, 255, 0.9);
}

.ending-btn-secondary:active {
  background-color: rgba(255, 255, 255, 0.25);
}

.ending-btn-primary {
  background: linear-gradient(135deg, #6b7aff 0%, #9c88ff 100%);
  color: white;
  box-shadow: 0 8rpx 25rpx rgba(107, 122, 255, 0.4);
}

.ending-btn-primary:active {
  background: linear-gradient(135deg, #5a69ee 0%, #8b77ee 100%);
}

.ending-btn text {
  display: block;
  line-height: 1.2;
}

/* 打字状态提示 */
.typing-status {
  position: fixed;
  bottom: 200rpx; /* 调整位置，避免与输入框重叠 */
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 50; /* 降低层级，但仍在内容之上 */
}

.typing-status text {
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  font-size: 24rpx;
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
}
