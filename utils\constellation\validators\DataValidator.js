/**
 * 数据验证和清理工具
 * 负责验证星座数据的完整性和一致性
 */

class DataValidator {
    constructor() {
        this.validationRules = {
            starPosition: {
                x: { type: 'number', min: 0, max: 1, required: true },
                y: { type: 'number', min: 0, max: 1, required: true },
                anchorStarId: { type: 'string', required: false },
                orbitRadius: { type: 'number', min: 0, required: false },
                orbitAngle: { type: 'number', required: false }
            },
            starData: {
                id: { type: 'string', required: true },
                theme: { type: 'string', required: true, minLength: 1 },
                emotionKeyword: { type: 'string', required: false },
                createTime: { type: 'date', required: true }
            },
            constellationData: {
                userId: { type: 'string', required: true },
                stars: { type: 'array', required: true },
                metadata: { type: 'object', required: false }
            }
        }
    }

    /**
     * 验证星座位置数据
     */
    validateStarPosition(starPosition) {
        const errors = []
        const warnings = []

        if (!starPosition || typeof starPosition !== 'object') {
            errors.push('星座位置数据必须是对象类型')
            return { valid: false, errors, warnings }
        }

        // 验证必需字段
        const rules = this.validationRules.starPosition
        for (const [field, rule] of Object.entries(rules)) {
            const value = starPosition[field]

            if (rule.required && (value === undefined || value === null)) {
                errors.push(`字段 ${field} 是必需的`)
                continue
            }

            if (value !== undefined && value !== null) {
                // 类型验证
                if (rule.type === 'number' && typeof value !== 'number') {
                    errors.push(`字段 ${field} 必须是数字类型`)
                    continue
                }

                if (rule.type === 'string' && typeof value !== 'string') {
                    errors.push(`字段 ${field} 必须是字符串类型`)
                    continue
                }

                // 范围验证
                if (rule.type === 'number') {
                    if (rule.min !== undefined && value < rule.min) {
                        errors.push(`字段 ${field} 不能小于 ${rule.min}`)
                    }
                    if (rule.max !== undefined && value > rule.max) {
                        errors.push(`字段 ${field} 不能大于 ${rule.max}`)
                    }
                }

                // 特殊验证
                if (field === 'x' || field === 'y') {
                    if (isNaN(value) || !isFinite(value)) {
                        errors.push(`字段 ${field} 必须是有效的数字`)
                    }
                }
            }
        }

        // 逻辑一致性检查
        if (starPosition.anchorStarId && !starPosition.orbitRadius) {
            warnings.push('指定了锚点星星但未设置轨道半径')
        }

        if (starPosition.orbitRadius && !starPosition.anchorStarId) {
            warnings.push('设置了轨道半径但未指定锚点星星')
        }

        return {
            valid: errors.length === 0,
            errors,
            warnings
        }
    }

    /**
     * 验证星星数据
     */
    validateStarData(starData) {
        const errors = []
        const warnings = []

        if (!starData || typeof starData !== 'object') {
            errors.push('星星数据必须是对象类型')
            return { valid: false, errors, warnings }
        }

        // 验证基本字段
        const rules = this.validationRules.starData
        for (const [field, rule] of Object.entries(rules)) {
            const value = starData[field]

            if (rule.required && (!value || (typeof value === 'string' && value.trim().length === 0))) {
                errors.push(`字段 ${field} 是必需的`)
                continue
            }

            if (value !== undefined && value !== null) {
                if (rule.type === 'string' && typeof value !== 'string') {
                    errors.push(`字段 ${field} 必须是字符串类型`)
                }

                if (rule.type === 'date') {
                    const date = new Date(value)
                    if (isNaN(date.getTime())) {
                        errors.push(`字段 ${field} 必须是有效的日期`)
                    }
                }

                if (rule.minLength && typeof value === 'string' && value.length < rule.minLength) {
                    errors.push(`字段 ${field} 长度不能少于 ${rule.minLength} 个字符`)
                }
            }
        }

        // 验证星座位置（如果存在）
        if (starData.starPosition || starData.constellationPosition) {
            const positionData = starData.starPosition || starData.constellationPosition
            const positionValidation = this.validateStarPosition(positionData)
            
            if (!positionValidation.valid) {
                errors.push(...positionValidation.errors.map(err => `位置数据: ${err}`))
            }
            warnings.push(...positionValidation.warnings.map(warn => `位置数据: ${warn}`))
        }

        return {
            valid: errors.length === 0,
            errors,
            warnings
        }
    }

    /**
     * 验证星座数据
     */
    validateConstellationData(constellationData) {
        const errors = []
        const warnings = []

        if (!constellationData || typeof constellationData !== 'object') {
            errors.push('星座数据必须是对象类型')
            return { valid: false, errors, warnings }
        }

        // 验证基本字段
        const rules = this.validationRules.constellationData
        for (const [field, rule] of Object.entries(rules)) {
            const value = constellationData[field]

            if (rule.required && !value) {
                errors.push(`字段 ${field} 是必需的`)
                continue
            }

            if (value !== undefined) {
                if (rule.type === 'array' && !Array.isArray(value)) {
                    errors.push(`字段 ${field} 必须是数组类型`)
                }

                if (rule.type === 'object' && typeof value !== 'object') {
                    errors.push(`字段 ${field} 必须是对象类型`)
                }

                if (rule.type === 'string' && typeof value !== 'string') {
                    errors.push(`字段 ${field} 必须是字符串类型`)
                }
            }
        }

        // 验证星星数组
        if (constellationData.stars && Array.isArray(constellationData.stars)) {
            constellationData.stars.forEach((star, index) => {
                const starValidation = this.validateStarData(star)
                if (!starValidation.valid) {
                    errors.push(...starValidation.errors.map(err => `星星[${index}]: ${err}`))
                }
                warnings.push(...starValidation.warnings.map(warn => `星星[${index}]: ${warn}`))
            })

            // 检查星星ID的唯一性
            const starIds = constellationData.stars.map(star => star.id).filter(id => id)
            const duplicateIds = starIds.filter((id, index) => starIds.indexOf(id) !== index)
            if (duplicateIds.length > 0) {
                errors.push(`发现重复的星星ID: ${duplicateIds.join(', ')}`)
            }
        }

        return {
            valid: errors.length === 0,
            errors,
            warnings
        }
    }

    /**
     * 清理和标准化星座位置数据
     */
    sanitizeStarPosition(starPosition) {
        if (!starPosition || typeof starPosition !== 'object') {
            return null
        }

        const sanitized = {}

        // 清理坐标
        if (typeof starPosition.x === 'number' && isFinite(starPosition.x)) {
            sanitized.x = Math.max(0, Math.min(1, starPosition.x))
        }

        if (typeof starPosition.y === 'number' && isFinite(starPosition.y)) {
            sanitized.y = Math.max(0, Math.min(1, starPosition.y))
        }

        // 清理可选字段
        if (starPosition.anchorStarId && typeof starPosition.anchorStarId === 'string') {
            sanitized.anchorStarId = starPosition.anchorStarId.trim()
        }

        if (typeof starPosition.orbitRadius === 'number' && starPosition.orbitRadius >= 0) {
            sanitized.orbitRadius = starPosition.orbitRadius
        }

        if (typeof starPosition.orbitAngle === 'number') {
            sanitized.orbitAngle = starPosition.orbitAngle
        }

        // 保留元数据
        if (starPosition.generatedAt) {
            sanitized.generatedAt = starPosition.generatedAt
        }

        if (starPosition.generationType) {
            sanitized.generationType = starPosition.generationType
        }

        if (typeof starPosition.starIndex === 'number') {
            sanitized.starIndex = starPosition.starIndex
        }

        return Object.keys(sanitized).length > 0 ? sanitized : null
    }

    /**
     * 清理和标准化星星数据
     */
    sanitizeStarData(starData) {
        if (!starData || typeof starData !== 'object') {
            return null
        }

        const sanitized = {
            id: starData.id || starData._id || `star_${Date.now()}`,
            theme: (starData.theme || '').trim() || '未知主题',
            summary: (starData.summary || '').trim(),
            emotionKeyword: (starData.emotionKeyword || starData.starKeyword || '').trim(),
            dialogueContent: Array.isArray(starData.dialogueContent) ? starData.dialogueContent : [],
            createTime: starData.createTime || starData.completedTime || new Date().toISOString()
        }

        // 清理位置数据
        const positionData = starData.starPosition || starData.constellationPosition
        if (positionData) {
            const sanitizedPosition = this.sanitizeStarPosition(positionData)
            if (sanitizedPosition) {
                sanitized.starPosition = sanitizedPosition
            }
        }

        // 保留其他有用字段
        const optionalFields = [
            'tomorrowMessage', 'questions', 'answers', 'dailyFeeling',
            'wantDiary', 'diaryContent', 'migrationInfo'
        ]

        optionalFields.forEach(field => {
            if (starData[field] !== undefined) {
                sanitized[field] = starData[field]
            }
        })

        return sanitized
    }

    /**
     * 批量验证数据
     */
    batchValidate(dataArray, validationType = 'starData') {
        const results = {
            totalCount: dataArray.length,
            validCount: 0,
            invalidCount: 0,
            warningCount: 0,
            results: []
        }

        dataArray.forEach((data, index) => {
            let validation = null

            switch (validationType) {
                case 'starData':
                    validation = this.validateStarData(data)
                    break
                case 'starPosition':
                    validation = this.validateStarPosition(data)
                    break
                case 'constellationData':
                    validation = this.validateConstellationData(data)
                    break
                default:
                    validation = { valid: false, errors: ['未知的验证类型'], warnings: [] }
            }

            results.results.push({
                index,
                data,
                validation
            })

            if (validation.valid) {
                results.validCount++
            } else {
                results.invalidCount++
            }

            if (validation.warnings && validation.warnings.length > 0) {
                results.warningCount++
            }
        })

        return results
    }

    /**
     * 生成验证报告
     */
    generateValidationReport(validationResults) {
        const report = {
            summary: {
                total: validationResults.totalCount,
                valid: validationResults.validCount,
                invalid: validationResults.invalidCount,
                warnings: validationResults.warningCount,
                validRate: validationResults.totalCount > 0 ? 
                    (validationResults.validCount / validationResults.totalCount * 100).toFixed(2) + '%' : '0%'
            },
            issues: {
                errors: [],
                warnings: []
            }
        }

        validationResults.results.forEach((result, index) => {
            if (result.validation.errors && result.validation.errors.length > 0) {
                report.issues.errors.push({
                    index,
                    errors: result.validation.errors
                })
            }

            if (result.validation.warnings && result.validation.warnings.length > 0) {
                report.issues.warnings.push({
                    index,
                    warnings: result.validation.warnings
                })
            }
        })

        return report
    }
}

// 创建全局实例
const dataValidator = new DataValidator()

export default dataValidator
