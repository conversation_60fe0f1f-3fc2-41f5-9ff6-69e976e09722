const cloud = require('wx-server-sdk')
cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV })
const db = cloud.database()

exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const openid = wxContext.OPENID

  const userRef = db.collection('users')

  // 查询是否存在该用户
  const userRes = await userRef.where({ _openid: openid }).get()

  // 如果用户不存在，创建初始记录
  if (userRes.data.length === 0) {
    await userRef.add({
      data: {
        _openid: openid,
        lightPoints: 12,
        isVIP: false,
        vipExpireTime: null
      }
    })

    return {
      lightPoints: 12,
      isVIP: false,
      vipExpireTime: null
    }
  }

  // 如果已存在，返回用户信息
  const userData = userRes.data[0]
  return {
    lightPoints: userData.lightPoints || 0,
    isVIP: userData.isVIP || false,
    vipExpireTime: userData.vipExpireTime || null
  }
}
