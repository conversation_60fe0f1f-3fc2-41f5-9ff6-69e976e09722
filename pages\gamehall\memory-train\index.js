const storyData = {
    // 起始节点
    'start': {
        text: ['规律的、催眠般的“哐当”声，将你的意识，从一片粘稠的黑暗中，缓缓拖拽了出来。', '你睁开眼睛。', '你发现自己，正坐在一张触感极其柔软的、暗红色的天鹅绒座椅上。这是一节充满了复古气息的列车车厢，黄铜色的行李架，擦得锃亮的木质墙壁，以及窗外那片，被夜色完全吞噬的黑暗。', '你的大脑一片空白。你不记得你是谁，也不记得你为何会在这里。', '就在你感到一阵因巨大的“失重感”而带来的、毛骨悚然的恐慌时，你注意到了你的对面。', '你的对面，坐着一个人。', '他穿着一身与你身上一模一样的、剪裁得体的深色风衣。他的发型、他的脸型、他的眉眼，甚至他嘴角那丝若有若无的、带着一丝嘲讽的微笑……都与你，一模一样。', '他就像是你在镜子里的“倒影”，一个活了过来的、拥有了自己独立意识的“倒影”。', '就在你因为眼前这诡异到极点的一幕，而感到浑身血液都快要凝固的瞬间，对面的那个“你”，优雅地整理了一下自己的领带，用一种你无比熟悉的、属于你自己的声音，平静地、微笑着，开口了：', '“你好。看来，你也醒了。”', '“那么，在我们之间，必须有一个人，先想起来点什么，对吗？”', '“不如，你先来？”', '“请问……你是谁？”'],
        options: [
            { text: '“我不知道我是谁。你，又是谁？”', subtext: '将问题抛回给对方，一种谨慎的防御与试探。', next: '2-A' },
            { text: '“我是一个医生。”', subtext: '为自己快速构建一个身份，试图在混乱中，重新掌握主动权。', next: '2-B' },
            { text: '“我是一个，刚刚杀过人的，逃犯。”', subtext: '用一个极具攻击性的身份，来冲击对方，观察他在压力下的真实反应。', next: '2-C' },
            { text: '（沉默）', subtext: '放弃语言的交锋，用纯粹的观察与审视，来积蓄力量，后发制人。', next: '2-D' }
        ]
    },
    // 第二轮分支
    '2-A': {
        text: ['对面的“你”，在听到你这个滴水不漏的反问后，脸上的笑容，更深了。', '“哦？一个反问？有意思。” 他轻轻地鼓了鼓掌，那声音，在这空旷的车厢里，显得异常清脆，“看来，你很谨慎。那么，我们就换个游戏。”', '他打了个响指。', '瞬间，你们之间那张小小的茶几上，凭空，出现了一副，摊开的扑克牌。', '“既然我们都不记得自己是谁，” 他微笑着，说，“不如，就让‘运气’，来替我们，做第一个决定吧。”', '“规则很简单。我们，各抽一张牌。谁的点数小，谁，就必须先回答对方一个，无论多么尖锐，都不能拒绝的问题。”', '“怎么样？这个提议，足够公平吧？”'],
        options: [
            { text: '接受游戏。你伸出手，从那副牌中，随意地，抽出一张。', subtext: '你选择，进入对方制定的规则，看看他到底想玩什么花样。', next: '3' },
            { text: '拒绝游戏。你说：“我从不相信运气，我只相信我自己的判断。”', subtext: '你选择，继续保持防御，拒绝被对方牵着鼻子走。', next: '3' },
            { text: '改变规则。你说：“可以。但，如果是你输了，你要回答我两个问题。”', subtext: '你选择，在对方的规则上，加入对自己有利的“条款”，试图反向掌控局面。', next: '3' },
            { text: '物理破局。你猛地一挥手，将整副扑克牌，全部扫落在地。', subtext: '你选择，用最直接的行动，来表达你对这种“无聊游戏”的，彻底不屑。', next: '3' }
        ],
        styleResponse: '“看来，‘运气’，已经，为我们，指明了，下一个‘话题’。” 对面的“你”，收起了所有的扑克牌，脸上，露出一丝，仿佛一切尽在掌握的微笑。'
    },
    '2-B': {
        text: ['对面的“你”，在听到“医生”这个身份后，先是微微一愣，随即，笑了。那是一种，充满了“玩味”与“怜悯”的笑。', '“哦……医生？一个崇高的职业啊。” 他刻意加重了“崇高”的语气，“那么，医生先生，您一定，对‘手’，非常了解，对吗？”', '他，用眼神，示意你看向自己的双手。', '你低头，这才发现，你的手，指甲缝里残留着黑色的印记，指节处还有着薄薄的老茧。并且，在微微地，无法自控地颤抖。', '“看，医生先生。你的手，在‘抗议’哦。” 他脸上的笑意更浓了，“不过，没关系。我们，还有时间，来慢慢地，玩这场‘角色扮演’的游戏。”', '他看着你，用一种充满了“恶意”的、仿佛在看一场好戏的眼神，一字一句地，问道：', '“那么，这位连自己的手，都控制不住的‘医生’先生……你，是更擅长，拿起那把能切开皮肉的‘手术刀’，还是，更擅长，去弹奏那把，能撕裂灵魂的‘大提琴’呢？”'],
        options: [
            { text: '坚持谎言。你强行抑制住颤抖，说：“我当然，选择手术刀。一个好的外科医生，手，偶尔，也是会抖的。”', subtext: '你选择，用更强的意志，去维护你刚刚建立的“秩序”，拒绝示弱。', next: '3' },
            { text: '转换身份。你坦然地，切换到另一个身份，说：“看来，被你看穿了。好吧，我承认，我其实，是一个……修了三十年车的，老汽修工。”', subtext: '你选择，用一个新的“谎言”，来覆盖旧的谎言，展现你灵活的应变能力。', next: '3' },
            { text: '反守为攻。你反问道：“我的手不像是医生，你的手，就像了吗？把你的手，拿出来我看看。”', subtext: '你选择，用对方的逻辑，去攻击对方，试图，将战火，烧到他的身上。', next: '3' },
            { text: '承认失败。你叹了口气，说：“好吧。我不是医生。我只是……太想知道我是谁了。”', subtext: '你选择，主动放弃伪装，用一种“示弱”的姿态，来观察对方的下一步行动。', next: '3' }
        ],
        styleResponse: '“……好吧。看来，‘身份’这个游戏，已经，不好玩了。” 对面的“你”，在短暂的交锋后，露出一丝，仿佛“已经玩腻了”的疲惫笑容，“我们，来聊点，更真实的东西吧。”'
    },
    '2-C': {
        text: ['对面的“你”，在听到你这个，充满了“攻击性”与“挑衅”的回答后，非但没有被激怒，反而，发出了，一声，充满了“赞许”的、低沉的笑声。', '“哈哈哈哈……逃犯？有意思。真的，太有意思了。” 他看着你，眼神里，第一次，闪烁起一种，近乎于“狂热”的光芒。', '“我喜欢这个身份。它，比‘医生’，要诚实得多。”', '他，突然，站起了身。', '然后，他，走到你的面前，居高临下地，俯视着你。', '“那么，‘逃犯’先生，” 他说，声音，压得很低，像恶魔的耳语，“既然，我们是‘同类’。不如，我们，来做一件，只有‘我们’这种人，才敢做的事情，怎么样？”', '他，指向了车厢的尽头，那扇，紧闭着的、通往“下一节车厢”的门。', '“那扇门的后面，” 他说，声音里，充满了诱惑，“坐着这趟列车，唯一的‘列车长’。”', '“他，知道，我们所有人的秘密。包括，我们，到底，‘犯了什么罪’。”', '“现在，我给你一个选择。你去，用你的方式，‘说服’他，让他，把我们的‘档案’，交出来。而我，会在这里，为你，‘望风’。”', '“怎么样？这个分工，很合理吧？毕竟，你，才是那个，更专业的‘罪犯’，不是吗？”'],
        options: [
            { text: '接受提议。你站起身，说：“可以。但，我拿到档案后，必须，我先看。”', subtext: '你选择，将计就计，深入这个危险的游戏，并试图，为自己，争取到主动权。', next: '3' },
            { text: '提出质疑。你说：“望风？我怎么知道，你不会在我进去之后，就把门，从外面锁上？”', subtext: '你选择，保持警惕，指出这个提议中，最致命的逻辑漏洞。', next: '3' },
            { text: '拒绝合作。你说：“我从不和陌生人合作。尤其是，一个长得和我一模一样的陌生人。”', subtext: '你选择，拒绝这个高风险的提议，重新，将局面，拉回到“对峙”的状态。', next: '3' },
            { text: '直接动手。你，猛地，出手，试图，将这个，靠得太近的“你”，直接，制服在地。', subtext: '你选择，用最原始的暴力，来打破这个，由他主导的、危险的局面。', next: '3' }
        ],
        styleResponse: '“……没意思。” 对面的“你”，在你做出选择后，突然，像一个泄了气的皮球一样，退回到自己的座位上，脸上，露出一种，极度失望的表情，“看来，那个所谓的‘列车长’，那个所谓的‘罪’，对你来说，都，不够刺激。那么，好吧。我们，来玩一个，更私人的，也更残酷的游戏。”'
    },
    '2-D': {
        text: ['你的沉默，像一块，投入深海的巨石。没有激起任何波澜，却让整个空间的“压强”，都，瞬间，增加了数倍。', '对面的“你”，脸上的那丝嘲讽的微笑，在你那冰冷的、充满了“审视”的目光中，一点一点地，收敛了起来。', '他，第一次，坐直了身体。', '“……好吧。” 他，缓缓地，吐出了一口气，仿佛，是在承认，他第一轮的“试探”，已经，彻底失败。', '“看来，你，比我想象的，要难对付得多。”', '“你，不喜欢，说废话。你，只相信，你自己眼睛看到的，‘证据’。对吗？”', '他，一边说着，一边，从怀里，拿出了一样东西，放在了桌上。', '那是一只，老旧的、黑色的录音笔。', '“我，确实，不记得，我是谁。” 他说，声音，第一次，变得，有些，真诚，“但是，在我醒来的时候，我的手里，就攥着这支录音笔。里面，好像，录下了一段，对我们来说，至关重要的‘对话’。”', '“但是，” 他顿了顿，眼神，再一次，变得，复杂起来，“它的电量，只够，播放一次。而且，只有30秒。”', '“现在，我把‘选择权’，交给你。”', '“这支录音笔里，有A、B、C，三段，不同的录音。每一段，都只有30秒。你，只能选择，播放其中一段。一旦播放，录音笔，就会彻底报废。”', '“那么，你，想听哪一段？”'],
        options: [
            { text: '播放录音A。录音笔上标注的标签是：【一个男人的，愤怒的质问】。', subtext: '你选择，去探寻，冲突的“起因”。', next: '3' },
            { text: '播放录音B。录音笔上标注的标签是：【一个女人的，疲惫的叹息】。', subtext: '你选择，去感受，故事的“情感内核”。', next: '3' },
            { text: '播放录音C。录音笔上标注的标签是：【一段，列车到站的，环境音】。', subtext: '你选择，去寻找，关于“我是谁”之外的，关于“我们在哪”的线索。', next: '3' },
            { text: '毁掉它。你，拿起那支录音笔，在对方，震惊的目光中，将它，狠狠地，摔在地上，踩得粉碎。', subtext: '你选择，拒绝，这种被施舍的、有限的“线索”。你，不相信，他给出的任何东西。', next: '3' }
        ],
        styleResponse: '“看来，你，要么，选错了方向，要么，就不相信我。” 对面的“你”，在你做出选择后，无奈地，叹了口气，“好吧，那我，再给你，最后一个，也是最真实的‘证据’。”'
    },
    // 【V1.4 修正】删除了我之前擅自加入的“无论你...”的错误文本
    '3': {
        text: ['瞬间，一张小小的茶几，在你们之间，凭空出现。', '茶几上，两样东西，缓缓地，由虚影，变成了实体。', '左手边，是一本，厚重的、用古老的皮质封面包裹的、上了锁的《历史书》。', '右手边，是一张，轻盈的、洁白无瑕的、等待被谱写的《五线谱》。', '对面的“你”，用一种，仿佛是在拷问整个宇宙的语气，缓缓地，问道：', '“告诉我……面对‘人生’这趟，单程的列车。我们，到底，应该，成为一个，背负着所有‘历史’的、清醒的‘记录者’？还是，应该成为一个，可以随时，谱写全新‘乐章’的、无知的‘创造者’？”'],
        options: [
            { text: '你选择【历史】。你伸出手，轻轻地，合上了那本，厚重的《历史书》。', subtext: '你选择，先承认和看清，所有已经发生的“事实”，再谈其他。', next: '4' },
            { text: '你选择【未来】。你伸出手，在那张空白的《五线谱》上，用指尖，轻轻地，点下了，第一个音符。', subtext: '你选择，无视过去，因为，创造全新的乐章，才是更重要的事。', next: '4' },
            { text: '你选择【并存】。你，将那本《历史书》，轻轻地，压在了《五线谱》的一角。', subtext: '你选择，寻求一种，背负着过去，但依然，可以创造未来的可能。', next: '4' },
            { text: '你选择【超越】。你，两样东西，都没有碰。你只是，微笑着，对那个“自己”，伸出了手。', subtext: '你选择，邀请对方，一起，离开这个，由“二元对立”所构成的游戏。', next: '4' }
        ]
    },
    // 【V1.4 修正】删除了我之前擅自加入的“无论你...”的错误文本
    '4': {
        text: ['对面的“你”，缓缓地，站起了身。', '“好吧。” 他说，声音里，带着一丝，仿佛是“最终摊牌”的决绝，“看来，这些，充满了‘隐喻’的把戏，已经，无法，再困住你了。”', '“那么，就让你，看一看，那个，最‘真实’的，也是，我们，共同，想要‘遗忘’的——真相吧。”', '他，再一次，打了个响指。', '瞬间，整个世界，在你面前，碎裂了。', '你眼前的列车车厢，那个“你”，那本《历史书》与《五线谱》，都像一块被巨石击中的玻璃一样，轰然，坍塌，分解成亿万片，闪着微光的碎片，被卷入一片，纯白色的、无边无际的“虚空”之中。', '你，就悬浮在这片，绝对的“无”之中。', '紧接着，一段，完整的、清晰的、不再有任何“割裂感”的“真实记忆”，像一条，被解冻的、奔腾不息的大河，瞬间，就填满了你之前，那片空白的、干涸的意识河床。', '你，全都，想起来了。', '你想起了，那个，曾经，在聚光灯下，拉着大提琴，意气风发的少年。', '你也想起了，那个，为了家人的期待，和一份更“安稳”的生活，最终，收起了琴弓，穿上了西装的，青年。', '你想起了，那个“签约日”。', '你，并没有，“抛弃”那个拉琴的自己。你只是，与他，做了一个“交易”。你对他说：“再给我，十年。十年后，我一定，会回来，让你，重新，拿起这把琴。”', '而今天，就是那个，十年之期，已到的日子。', '这辆列车，这个“对面的你”，都只是，那个十年前的“你”，为了防止十年后的“你”，会因为沉迷于“安稳”而“背信弃义”，而为自己，设下的一个，最深刻的“闹钟”。', '当你，终于，看清了这个，让你痛苦不堪的、最核心的“真相”时。你发现，你，已经不再，身处那片“虚空”。', '你，再一次，回到了，那个，你最初醒来时的、充满了灰尘与松节油味道的——旧画室。', '你，依然，坐在那张，孤零零的木椅子上。', '而你的面前，摆着两样东西。', '左手边，是一份，散发着“安全”与“成功”气息的、已经签好了字的——“首席市场分析师”的《工作合同》。', '右手边，是一把，你此刻，正死死攥在手中的、代表着“梦想”与“激情”的——断了弦的《大提琴弓》。', '现在，你，已经，拥有了全部的“记忆”。', '那么，你，会，先，拿起哪一个？'],
        options: [
            { text: '你，先，拿起了那份，沉甸甸的《工作合同》。', subtext: '你选择，先确认你的“现实”与“责任”。', next: '5' },
            { text: '你，先，拿起了那把，虽然断了弦，却无比滚烫的《大提琴弓》。', subtext: '你选择，先回应你内心，那个被压抑了十年的“渴望”。', next: '5' },
            { text: '你，两样东西，都没有碰。你，站起身，走到了画室那扇，虚掩着的窗户前。', subtext: '你选择，在做出任何决定之前，先看一看，外面的世界。', next: '5' },
            { text: '你，两样东西，都没有碰。你，只是，疲惫地，将头，深深地，埋进了自己的双臂里。', subtext: '你选择，在做出任何决定之前，先给自己，一段，消化这巨大真相的“缓冲时间”。', next: '5' }
        ],
        styleResponseMap: {
            '3_A': '“……一个，明智，但沉重的选择。” 对面的“你”，看着你合上《历史书》的动作，眼神复杂地，点了点头，“看来，你，选择，先背负起，我们共同的‘重量’。”',
            '3-B': '“……一个，勇敢，但漂泊的选择。” 对面的“你”，看着你在《五线谱》上点下的那个音符，轻轻地，叹了口气，“看来，你，宁愿，成为一个没有‘过去’的幽灵，也要，拥抱那个，不确定的‘未来’。”',
            '3-C': '“……一个，贪婪，但完整的选择。” 对面的“你”，看着你将书压在乐谱上的动作，嘴角，勾起一丝，难以捉摸的微笑，“看来，你，既想要，大地的沉稳，也想要，天空的自由。”',
            '3-D': '“……一个，出乎意料，却又在情理之中的选择。” 对面的“你”，看着你伸出的手，第一次，露出了，发自内心的，赞许的笑容，“看来，你，已经，厌倦了，我为你设下的，所有‘游戏’。”'
        }
    },
    // 【V1.4 修正】删除了我之前擅自加入的“无论你...”的错误文本
    '5': {
        text: ['此刻，你，重新，站回了那个，充满了灰尘与松节油味道的旧画室的中央。', '你，已经，拥有了全部的“记忆”。 你，也拥有了，全部的“自由”。', '画室那扇，紧闭的、厚重的大门，突然，缓缓地，向你，敞开了。', '门外，就是那个，你所熟悉的、充满了汽车鸣笛声、人群喧哗声、电话铃声的、无比真实的、你已经，在其中，战斗了十年的——“现实世界”。', '你知道，只要你，站起身，走出这扇门，你，就会变回那个，成功的、干练的、受人尊敬的“市场总监”。你可以，继续，去赢得，一场又一场，“正确，但无趣的，胜利”。', '没有人，会责怪你。 甚至，连那个，已经与你，合二为一的、“过去的自己”，也都会，理解你。', '那么，现在。', '在你，即将，走出这间，充满了回忆的画室，回归“现实”的前一刻。', '你，会选择，带上什么，作为你，开启“新人生”的、唯一的“行李”？'],
        options: [
            { text: '你，只，带上了那份，沉甸甸的《工作合同》。', subtext: '你选择，彻底告别过去，更务实地，拥抱你的“责任”与“现实”。', next: '6' },
            { text: '你，只，带上了那把，断了弦的《大提琴弓》。', subtext: '你选择，放下世俗的成就，更纯粹地，回应你内心的“梦想”与“渴望”。', next: '6' },
            { text: '你，将《工作合同》与《大提琴弓》，都，抱在了怀里。', subtext: '你选择，同时，背负起你的“责任”与“梦想”，试图，在现实中，找到一条，让两者共存的道路。', next: '6' },
            { text: '你，什么都没有带。你，两手空空地，站起身，平静地，走向那扇门。', subtext: '你选择，将“过去”的一切，都留在过去。你相信，真正的“未来”，不需要任何行李。', next: '6' }
        ],
        styleResponseMap: {
            '4_A': '你，先，确认了你的“现实”。那份沉重的合同，是你过去十年，用无数个不眠之夜，为自己，换来的“盔甲”。它冰冷，坚硬，但它，让你感到安全。',
            '4-B': '你，先，回应了你的“渴望”。那把断了弦的琴弓，在你手中，依然滚烫。它，是你灵魂深处，那从未熄灭的“火焰”。它脆弱，危险，但它，让你感到活着。',
            '4-C': '你，没有立刻，做出选择。你走向窗户，试图，从外界寻找答案。但你发现，窗外的风景，会因为你内心的变化而变化。你最终明白，答案，永远，不在外面。',
            '4-D': '你，给了自己，一段宝贵的“缓冲”。当巨大的真相冲刷而来，最智慧的，不是行动，而是允许自己，去充分地，感受这份“震荡”。你，保护了自己。'
        }
    },
    // 【V1.4 修正】删除了我之前擅自加入的“无论你...”的错误文本
    '6': {
        text: ['此刻，你，正独自一人，站在这条，你所熟悉的、通往你那间高级办公室的街道上。', '你，抬起头。', '你，看着眼前，这个，由冰冷的玻璃幕墙、行色匆匆的人群、以及，空气里那股混合着咖啡与压力的味道所组成的“现实世界”。', '这一切，曾经，让你感到“麻木”。', '但现在，当你，带着一颗，刚刚，经历了一场“内心战争”洗礼的、全新的“心脏”，去重新，审视它时。', '你，从这片，看似“无趣”的现实中，看到了一些，不一样的东西。', '你看到了，那个，每天，为你冲咖啡的、年轻的实习生，他的耳机里，可能，正放着一首，关于“摇滚梦想”的、激烈的歌曲。', '你看到了，那个，总是，对你，点头哈腰的、中年的客户，他的公文包里，可能，藏着一张，他偷偷画下的、关于“远方田野”的、小小的素描。', '你，突然，意识到。', '这辆名为“人生”的列车，它的每一节车厢里，都，坐满了，像你一样，内心，充满了“挣扎”与“渴望”的——“陌生人”。', '你，并不，孤单。', '当你，完成了这次，与整个世界的“列车旅途”时。', '你知道，是时候，为你自己，这场，惊心动魄的“内心旅程”，做一个，最终的“总结”了。', '在经历了这一切之后，你认为，你，为自己，赢得的，最宝贵的“战利品”，究竟，是什么？'],
        options: [
            { text: '我，赢回了，那个，被我遗忘了十年的“梦想”。', subtext: '你认为，最重要的，是重新记起了那个，让你感到“活着”的激情。', next: 'report-A' },
            { text: '我，赢得了，一个，能同时拥抱“现实”与“梦想”的、更“完整”的自己。', subtext: '你认为，最重要的，是获得了，一种，能整合内心矛盾的、全新的“智慧”。', next: 'report-B' },
            { text: '我，赢得了，从那个“必须成功”的、沉重的“过去”中，彻底“解脱”的自由。', subtext: '你认为，最重要的，不是“得到”了什么，而是“放下”了什么。', next: 'report-C' },
            { text: '我，什么都没有赢得。我只是，终于，学会了，如何，与“我自己”，和平地，待在一起。', subtext: '你认为，最重要的，是一种，超越了所有概念的、纯粹的“自我接纳”与“内心安宁”。', next: 'report-D' }
        ],
        styleResponseMap: {
            '5_A': '你，走出了那扇门。怀里，只有那份，代表着“现实”的冰冷合同。你感觉，自己，像一个，卸下了所有不必要包袱的、清醒的战士。你选择，更清醒地，活在此刻。',
            '5_B': '你，走出了那扇门。怀里，只有那把，代表着“梦想”的、滚烫的琴弓。你感觉，自己，像一个，即将，去赴一场，与整个世界为敌的、浪漫约会的骑士。你选择，更热烈地，活在此刻。',
            '5-C': '你，走出了那扇门。怀里，同时，抱着你的“盔甲”与“火焰”。你感觉，自己，像一个，准备，在最复杂的钢丝上，跳出最美舞蹈的杂技演员。你选择，更完整地，活在此刻。',
            '5_D': '你，走出了那扇门。两手空空，一无所有，也因此，拥有一切。你感觉，自己，像一阵，可以，吹向任何方向的，自由的风。你选择，更轻盈地，活在此刻。'
        }
    },
    // 【V1.4 修正】更新了所有报告的文本内容
    'report-A': {
        isEnd: true,
        text: ['《人格特质报告：致一位内心驱动的理想主义者》', '朋友，你好。', '恭喜你，顺利完成了这次完整的内心探索。在故事的结尾，面对关乎个人价值的最终选择，你清晰地表达了自己的倾向——你为自己赢回了那个，被遗忘了十年的“梦想”。', '这是一个非常明确的信号，它揭示了你人格中一个非常核心的特质：你是一位由内在价值与情感体验所驱动的“理想主义者”。', '【核心特质分析】', '在最终的选择中，你更倾向于那把象征着“热爱”与“激情”的琴弓，而非那份代表着“安稳”与“成就”的合同。这并不意味着你不了解现实，而是说明在你的决策体系中，“内在的真实感受”占据了极高的权重。', '这种人格特质，通常会表现为以下几个方面：', '高度关注事物的意义和价值： 对你而言，做一件事的理由，往往更多地源于“它是否让我感到有意义、有热情”，而不是“它是否能带来最大的现实利益”。你倾向于投身那些能与你个人价值观产生共鸣的领域。', '情感体验深刻、真诚： 你能够体验到比一般人更强烈、更深刻的情感。无论是喜悦还是悲伤，你的感受都非常真切。同时，你也非常珍视“真诚”，并以此作为衡量人际关系质量的重要标准。', '富有创造力和想象力： 理想主义者往往对“可能性”更感兴趣。你可能经常会思考“事情可以变成什么样”，而不是仅仅接受“事情现在是什么样”。这种思维模式，是你创造力的重要来源。', '坚持个人信条： 你拥有一套相对稳固的内在标准和道德准则。在面临外界压力时，你倾向于坚守自己的原则，而不是轻易妥协。', '你在故事结尾的选择，正是这种内在驱动力的体现。你选择了那份能直接触动你内心、点燃你生命热情的选项，这展现了你忠于自我、追求精神满足的鲜明人格底色。', '【给探索者的最后赠言】', '我的朋友，', '我们看见了你的选择，并由衷地欣赏这份忠于内心的坚持。在一个高度强调“结果”和“效率”的环境中，能够始终聆听自己内心的声音，并勇敢地以其作为人生的指引，是一项非常宝贵且难得的能力。', '这份对意义和热情的追求，是你人格中最闪光的部分。它让你的生命体验充满了深度和色彩，也让你拥有了感染和启发他人的独特魅力。', '请继续相信你内心的这份指引。坚持你所坚持的，热爱你所热爱的。你的选择，本身就充满了力量与价值。']
    },
    'report-B': {
        isEnd: true,
        text: ['《人格特质报告：致一位寻求平衡的整合者》', '朋友，你好。', '恭喜你，顺利完成了这次完整的内心探索。在故事的结尾，面对一个看似两难的复杂情境，你没有做出非此即彼的取舍，而是选择赢得一个“能同时拥抱‘现实’与‘梦想’的、更完整的自己”。', '这是一个非常成熟的选择，它揭示了你人格中一个非常核心的特质：你是一位在复杂局面中，倾向于寻求“平衡”与“整合”的思考者。', '【核心特质分析】', '在最终的选择中，你没有将象征“现实”的合同与象征“梦想”的琴弓视为绝对的对立。这并不代表你犹豫不决，而是说明在你的决策模型中，你拥有“全面看待问题并容纳多重价值”的卓越能力。', '这种人格特质，通常会表现为以下几个方面：', '思维的全面性与系统性： 你在看待问题时，习惯于看到事物的多个侧面。你既能理解“现实”层面的重要性与必要性，也能体会到“理想”层面的价值与意义。这让你在做决策时，考虑得比一般人更周全。', '强大的情境适应能力： 因为你能够理解并接纳矛盾的存在，所以你通常能很好地适应各种复杂的环境。你不会轻易地陷入“非黑即白”的思维困境，而是能灵活地找到不同方案中的可行之处。', '注重长期和谐与可持续性： 你的决策往往不只看重眼前的得失，而是会考虑如何让不同的要素能够长期地、和谐地共存。你追求的不是短暂的最优解，而是一个更具稳定性和发展性的平衡状态。', '富有责任感和担当： 选择“整合”往往意味着要承担更多的复杂性。你做出这样的选择，本身就说明你是一个不畏惧困难、愿意为更完整的目标而付出努力的人。', '你在故事结尾的选择，正是这种整合性思维的集中体现。你没有放弃任何一方的价值，而是尝试去构建一个能让两者共存的、更宏大的框架，这展现了你成熟、稳健的人格底色。', '【给探索者的最后赠言】', '我的朋友，', '我们看见了你的选择，并由衷地欣赏这份拥抱复杂性的智慧与勇气。在一个人人都渴望简单答案的时代，能够直面矛盾，并主动寻求整合与平衡，是一项非常宝贵且强大的能力。', '这份全面、周到、富有建设性的思考方式，是你人格中最坚实的部分。它让你在处理各种复杂问题时，总能找到更优的路径，也让你成为身边人可以信赖和依靠的对象。', '请继续相信你的这份判断力。坚持你所坚持的，守护你所守护的。你的选择，本身就充满了智慧与力量。']
    },
    'report-C': {
        isEnd: true,
        text: ['《人格特质报告：致一位追求内在自由的超越者》', '朋友，你好。', '恭喜你，顺利完成了这次完整的内心探索。在故事的结尾，面对“收获了什么”的最终提问，你给出了一个极具洞察力的答案——你赢得的，是从那个沉重的过去中，彻底“解脱”的自由。', '这是一个非常通透的选择，它揭示了你人格中一个非常核心的特质：你是一位不被外在框架所束缚、高度追求“内在自由”的独立思考者。', '【核心特质分析】', '在最终的选择中，你意识到，真正构成束缚的，可能并非“现实”或“梦想”本身，而是附加于其上的各种“必须”和“应该”。你选择从这种精神枷锁中“解脱”，这说明你拥有“审视并超越既定规则”的深刻洞察力。', '这种人格特质，通常会表现为以下几个方面：', '强大的独立思考能力： 你不轻易接受社会或他人给你的既定标签和评价体系。你习惯于用自己的头脑去审视和判断事物的本质，而不是盲从于权威或大众。', '高度的自我觉察： 你能够清晰地意识到自己的念头和情绪，但又不会轻易地被它们所控制。你仿佛是自己内心的“观察者”，能够与自己的思想保持一个健康的距离。', '追求精神层面的自由： 对你而言，内心的平静和不被束缚的状态，可能比获得外在的成功或物质更为重要。你渴望的是一种可以自由选择、不被任何观念所绑架的生命状态。', '行为洒脱，不拘一格： 因为你的价值体系源于内在，所以你的行为举止往往显得比一般人更加洒脱、不拘小节。你活得更真实、更忠于自己。', '你在故事结尾的选择，正是这种超越性思维的集中体现。你没有在给定的选项里做文章，而是直接跳出了整个问题的框架，这展现了你通透、自由的人格底色。', '【给探索者的最后赠言】', '我的朋友，', '我们看见了你的选择，并由衷地欣赏这份超越框架的智慧与通透。在一个充满了各种“标准答案”的世界里，能够始终保持清醒的觉察，并勇敢地选择属于自己的、不被定义的道路，是一项非常宝贵且罕见的能力。', '这份对内在自由的追求，是你人格中最鲜明的部分。它让你拥有了不被外界风雨轻易侵扰的内在堡垒，也让你的人生充满了无限的可能性。', '请继续相信你的这份洞察力。坚持你所坚持的，放下你所放下的。你的选择，本身就充满了智慧与价值。']
    },
    'report-D': {
        isEnd: true,
        text: ['《人格特质报告：致一位内心平和的自我接纳者》', '朋友，你好。', '恭喜你，顺利完成了这次完整的内心探索。在故事的结尾，面对“收获了什么”的最终提问，你给出了一个最朴素，却也最深刻的答案——你只是，终于学会了，如何与“自己”，和平地，待在一起。', '这是一个充满了温暖与智慧的选择，它揭示了你人格中一个非常核心的特质：你是一位深刻理解并践行“自我接纳”的平和主义者。', '【核心特质分析】', '在最终的选择中，你没有将焦点放在“赢得”或“成为”什么上，而是转向了内在，关注与自我关系的改善。这说明，你已经认识到，所有外在的成就与纷争，最终都源于我们如何与自己相处。你拥有“善待自我并与之和解”的宝贵能力。', '这种人格特质，通常会表现为以下几个方面：', '较高的情绪稳定性： 你可能依然会体验到各种情绪，但你拥有很强的自我调节能力。你不会长时间地陷入负面情绪或自我批评中，而是能更快地恢复内心的平静。', '对他人的包容和理解： 因为你能够接纳自己的不完美，所以你也更能理解和包容他人的不完美。这让你在人际关系中，往往是一个温和的、不带评判的、让人感到安心的存在。', '关注过程，而非仅仅是结果： 你明白人生的价值在于体验本身。你不会为了一个遥远的目标而过度牺牲当下的感受，而是懂得在过程中寻找意义和满足。', '内在的满足感和驱动力： 你的幸福感和价值感，更多地来源于你的内心，而不是外界的评价或物质的获得。你拥有一个稳固的“内在核心”，不轻易被外界环境所动摇。', '你在故事结尾的选择，正是这种自我接纳能力的集中体现。你将最终的胜利，定义为一场与自己的“握手言和”，这展现了你平和、通达、富有智慧的人格底色。', '【给探索者的最后赠言】', '我的朋友，', '我们看见了你的选择，并由衷地欣赏这份返璞归真的智慧与温暖。在这个鼓励我们不断向外追逐的时代，能够转向内心，学习如何与自己温柔相处，是一项最重要、也最强大的能力。', '这份平和地接纳自我的能力，是你人格中最有力量的部分。它像一个坚固而温暖的港湾，能让你在人生的风浪中，始终有所依靠，获得安宁。', '请继续相信你的这份感受。坚持你所坚持的，接纳你所接纳的。你的选择，本身就充满了深刻的智慧与治愈的力量。']
    }
};


Page({
    data: {
        storyData: storyData,
        currentNodeKey: 'start',
        previousNodeKey: null,
        displayedText: [],
        options: [],
        showOptions: false,
        isChoosing: false,
        textFadeOut: false,
        optionsFadeOut: false,
        textTimer: null,
        // 【V5.0 新增】跳过功能是否可用
        skipAvailable: true,
    },

    // 【V5.0 新增】BGM播放器实例
    bgm: null,

    onLoad: function (options) {
        // 【V5.0 新增】初始化并播放BGM
        // 假设小程序的全局BGM播放器实例挂载在 app.globalData.bgm 上
        // if (getApp().globalData.bgm && getApp().globalData.bgm.pause) {
        //   getApp().globalData.bgm.pause(); // 暂停全局BGM
        // }
        this.bgm = wx.createInnerAudioContext();
        // 【V5.1 路径修改】路径已更新为你指定的 assets 文件夹
        this.bgm.src = 'cloud://cloudbase-8gji862jcfb501e7.636c-cloudbase-8gji862jcfb501e7-1365531166/assets/train_suspense.mp3';
        this.bgm.loop = true;
        this.bgm.play();

        this.startGame();
    },

    onUnload: function () {
        // 页面卸载时，清除定时器并停止BGM，防止内存泄漏
        if (this.data.textTimer) {
            clearTimeout(this.data.textTimer);
        }
        if (this.bgm) {
            this.bgm.stop();
            this.bgm.destroy();
        }
        // 可以在这里恢复全局BGM
        // if (getApp().globalData.bgm && getApp().globalData.bgm.play) {
        //   getApp().globalData.bgm.play();
        // }
    },

    startGame() {
        if (this.data.textTimer) {
            clearTimeout(this.data.textTimer);
        }
        this.setData({
            currentNodeKey: 'start',
            previousNodeKey: null,
            displayedText: [],
            options: [],
            showOptions: false,
            isChoosing: false,
            skipAvailable: true, // 每次开始新节点时，都允许跳过
        });
        this.startStoryNode('start');
    },

    // 故事节点处理引擎
    startStoryNode(nodeKey) {
        const node = this.data.storyData[nodeKey];
        if (!node) return;

        let textQueue = [];

        // 处理风格化回应
        if (node.styleResponseMap && this.data.previousNodeKey) {
            const response = node.styleResponseMap[this.data.previousNodeKey];
            if (response) {
                textQueue.push(response);
            }
        } else if (node.styleResponse) {
            textQueue.push(node.styleResponse);
        }

        textQueue = textQueue.concat(node.text);

        this.playTextQueue(textQueue, () => {
            // 所有文本播放完毕后的回调
            if (node.options) {
                // 最终停顿4秒后，显示选项
                setTimeout(() => {
                    this.setData({
                        options: node.options,
                        showOptions: true
                    });
                }, 4000);
            }
        });
    },

    // 文本播放队列处理器
    playTextQueue(queue, onComplete) {
        let index = 0;

        const next = () => {
            if (index < queue.length) {
                this.setData({
                    displayedText: [...this.data.displayedText, { content: queue[index], id: Date.now() + Math.random() }]
                });
                index++;
                // 【V5.0 节奏修改】每行停顿4秒
                const timer = setTimeout(next, 4000);
                this.setData({ textTimer: timer });
            } else {
                if (onComplete) {
                    onComplete();
                }
            }
        };
        next();
    },

    // 【V5.0 新增】跳过功能处理器
    handleSkip() {
        // 如果正在选择或已经跳过，则不执行
        if (this.data.isChoosing || !this.data.skipAvailable) return;

        // 1. 立即停止正在进行的文本播放队列
        if (this.data.textTimer) {
            clearTimeout(this.data.textTimer);
        }

        // 2. 获取当前节点的所有文本
        const node = this.data.storyData[this.data.currentNodeKey];
        if (!node) return;
        let fullTextQueue = [];
        if (node.styleResponseMap && this.data.previousNodeKey) {
            const response = node.styleResponseMap[this.data.previousNodeKey];
            if (response) fullTextQueue.push(response);
        } else if (node.styleResponse) {
            fullTextQueue.push(node.styleResponse);
        }
        fullTextQueue = fullTextQueue.concat(node.text);

        // 3. 将所有文本一次性渲染到屏幕上
        const fullDisplayedText = fullTextQueue.map(line => ({ content: line, id: Math.random() }));
        this.setData({
            displayedText: fullDisplayedText,
            skipAvailable: false, // 当前节点不允许再次跳过
        });

        // 4. 立即显示选项
        if (node.options) {
            this.setData({
                options: node.options,
                showOptions: true
            });
        }
    },

    // 用户选择处理器
    handleSelectOption(e) {
        if (this.data.isChoosing) return;
        if (this.data.textTimer) {
            clearTimeout(this.data.textTimer);
        }

        this.setData({ isChoosing: true, optionsFadeOut: true });

        const nextNodeKey = e.currentTarget.dataset.next;
        // 【V1.4 修正】使用 index 来创建唯一的 choiceKey，因为选项文本可能很长
        const choiceKey = this.data.currentNodeKey + '_' + e.currentTarget.dataset.index;

        setTimeout(() => {
            this.setData({ textFadeOut: true });
            setTimeout(() => {
                this.setData({
                    previousNodeKey: choiceKey,
                    currentNodeKey: nextNodeKey,
                    displayedText: [],
                    options: [],
                    showOptions: false,
                    isChoosing: false,
                    textFadeOut: false,
                    optionsFadeOut: false,
                    skipAvailable: true, // 进入新节点，允许跳过
                });
                this.startStoryNode(nextNodeKey);
            }, 500);
        }, 500);
    }
});
