const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

exports.main = async (event, context) => {
  const { 
    type,
    openid, 
    limit = 10,  
    offset = 0,
    diaryId
  } = event

  try {
    switch(type) {
      // 获取日记列表
      case 'list':
        const result = await db.collection('star_track_diary')
          .where({
            openid: openid
          })
          .orderBy('createTime', 'desc')
          .skip(offset)
          .limit(limit)
          .get()

        // 查询今日是否有日记（用于次日登录提醒）
        const todayStart = new Date(new Date().toLocaleDateString())
        const todayDiary = await db.collection('star_track_diary')
          .where({
            openid: openid,
            createTime: _.gte(todayStart)
          })
          .count()

        return {
          success: true,
          diaries: result.data,
          hasTodayDiary: todayDiary.total > 0
        }

      // 获取日记详情
      case 'getDiaryDetail':
        const diaryDetail = await db.collection('star_track_diary')
          .doc(diaryId)
          .get()

        // 检查权限：仅允许查看自己的日记
        if (diaryDetail.data.openid !== openid) {
          return {
            success: false,
            error: '无权访问该日记'
          }
        }

        return {
          success: true,
          diary: diaryDetail.data
        }

      default:
        return { 
          success: false, 
          error: '未知的操作类型' 
        }
    }
  } catch (error) {
    console.error('获取星轨日记错误', error)
    return {
      success: false,
      error: error.message
    }
  }
} 