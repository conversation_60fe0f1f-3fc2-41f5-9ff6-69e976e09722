# 微光小程序项目

## 项目简介

"微光"是一个基于微信小程序平台开发的AI陪伴应用。它的核心理念是"向内求索"，旨在帮助用户通过AI辅助进行自我探索和成长。项目采用微信小程序 + 云开发的技术架构，集成了微信支付、AI对话等功能。

## 核心功能模块

### 1. 点灯仪式（入口）
- 新用户交互式引导
- 个性化微光命名
- 首次对话体验

### 2. 星光对话（主要功能）
- 主题式深度对话
- "三问"仪式：
  - AI生成总结
  - 用户情感标记
  - 明日寄语

### 3. 微光游戏厅
- 心理探索游戏
- 互动式剧本体验
- 个性化反馈

### 4. 星轨日记（功能中心）
- 个人成长轨迹记录
- 故事馆
- 私密写信功能
- 光点商城

## 商业模式
- 虚拟货币：光点
- 支付方式：
  - 直接购买光点（1元 = 1光点）
  - VIP会员（68元/月）
    - 每日4个免费光点
    - AI日记生成
    - 专属游戏内容

## 开发任务清单

### 一、用户与支付系统
1. [x] 用户账户系统搭建
   - 实现微信授权登录
   - 创建用户数据库记录（openid、昵称、头像等）
   - 初始化用户光点字段

2. [ ] 微信支付功能
   - 完善支付回调功能
   - 实现支付成功通知处理
   - 开发光点自动充值逻辑

### 二、AI对话系统
3. [ ] API代理服务
   - 搭建Google Gemini Pro API代理
   - 确保稳定的跨境访问
   - 实现错误处理和重试机制

4. [ ] 对话接口开发
   - 接收用户问题
   - 处理AI角色指令
   - 转发请求到Gemini API
   - 返回处理结果

### 三、用户分析系统
5. [ ] "前置七问"功能
   - 开发答案收集接口
   - 实现计分算法
   - 设计层级判定逻辑
   - 数据库存储机制

### 四、日记系统
6. [ ] 数据库设计
   - 创建星轨日记表
   - 设计数据结构
   - 建立索引优化

7. [ ] 写入接口
   - 心情词语存储
   - 明日寄语保存
   - AI总结存档
   - 日记内容记录

8. [ ] 读取接口
   - 星轨日记页面展示
   - 次日登录提醒
   - 历史记录查询

### 五、会员系统
9. [ ] VIP功能
   - 会员状态判定
   - 权限控制
   - 专属功能解锁

## 技术栈

- 前端：微信小程序
- 后端：云开发
- AI：Google Gemini Pro
- 数据库：云数据库
- 支付：微信支付

## 开发环境配置

1. 微信开发者工具配置
   - 最低基础库版本：2.2.3
   - AppID：wx7bc4cd4ea398129d
   - 云环境ID：cloud1-9gtm8p4792868ee9

2. 云开发环境
   - 开启云函数
   - 配置数据库
   - 设置安全规则

## 注意事项

1. 代码提交规范
   - 遵循功能模块划分
   - 添加清晰的注释
   - 做好版本控制

2. 安全考虑
   - 敏感信息加密
   - 用户数据保护
   - 支付安全

3. 性能优化
   - 控制云函数调用频率
   - 优化数据库查询
   - 注意内存使用

## 项目进度追踪

可以通过在任务清单中标记 [x] 来追踪已完成的任务：
- [ ] 表示待完成
- [x] 表示已完成

## 联系方式

如有问题或建议，请通过以下方式联系：
[在此添加联系方式] 