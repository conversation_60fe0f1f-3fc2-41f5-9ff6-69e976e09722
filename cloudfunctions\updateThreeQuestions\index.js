const cloud = require('wx-server-sdk')

cloud.init({
    env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 数据验证函数
function validateUpdateData(updateData) {
    const errors = []
    const allowedFields = [
        'dailyFeeling', 'tomorrowMessage', 'wantDiary', 'diaryContent',
        'starKeyword', 'emotionKeyword', 'completed', 'diaryGenerated',
        'diaryWordCount', 'diaryQualityScore', 'diaryFallback'
    ]

    // 检查是否有不允许的字段
    Object.keys(updateData).forEach(field => {
        if (!allowedFields.includes(field)) {
            errors.push(`不允许更新字段: ${field}`)
        }
    })

    // 验证具体字段
    if (updateData.dailyFeeling !== undefined) {
        if (typeof updateData.dailyFeeling !== 'string') {
            errors.push('dailyFeeling 必须是字符串类型')
        } else if (updateData.dailyFeeling.trim().length > 50) {
            errors.push('dailyFeeling 长度不能超过50个字符')
        }
    }

    if (updateData.tomorrowMessage !== undefined) {
        if (typeof updateData.tomorrowMessage !== 'string') {
            errors.push('tomorrowMessage 必须是字符串类型')
        } else if (updateData.tomorrowMessage.trim().length > 100) {
            errors.push('tomorrowMessage 长度不能超过100个字符')
        }
    }

    if (updateData.wantDiary !== undefined && typeof updateData.wantDiary !== 'boolean') {
        errors.push('wantDiary 必须是布尔类型')
    }

    if (updateData.completed !== undefined && typeof updateData.completed !== 'boolean') {
        errors.push('completed 必须是布尔类型')
    }

    if (updateData.diaryGenerated !== undefined && typeof updateData.diaryGenerated !== 'boolean') {
        errors.push('diaryGenerated 必须是布尔类型')
    }

    if (updateData.diaryContent !== undefined && typeof updateData.diaryContent !== 'string') {
        errors.push('diaryContent 必须是字符串类型')
    }

    if (updateData.diaryWordCount !== undefined && (!Number.isInteger(updateData.diaryWordCount) || updateData.diaryWordCount < 0)) {
        errors.push('diaryWordCount 必须是非负整数')
    }

    if (updateData.diaryQualityScore !== undefined && (typeof updateData.diaryQualityScore !== 'number' || updateData.diaryQualityScore < 0 || updateData.diaryQualityScore > 1)) {
        errors.push('diaryQualityScore 必须是0-1之间的数字')
    }

    return { valid: errors.length === 0, errors }
}

// 清理和标准化更新数据
function sanitizeUpdateData(updateData) {
    const sanitized = {}

    if (updateData.dailyFeeling !== undefined) {
        sanitized.dailyFeeling = updateData.dailyFeeling.trim()
    }

    if (updateData.tomorrowMessage !== undefined) {
        sanitized.tomorrowMessage = updateData.tomorrowMessage.trim()
    }

    if (updateData.wantDiary !== undefined) {
        sanitized.wantDiary = Boolean(updateData.wantDiary)
    }

    if (updateData.diaryContent !== undefined) {
        sanitized.diaryContent = updateData.diaryContent.trim()
        // 自动计算字数
        sanitized.diaryWordCount = sanitized.diaryContent.length
    }

    if (updateData.starKeyword !== undefined) {
        sanitized.starKeyword = updateData.starKeyword.trim()
    }

    if (updateData.emotionKeyword !== undefined) {
        sanitized.emotionKeyword = updateData.emotionKeyword.trim()
    }

    if (updateData.completed !== undefined) {
        sanitized.completed = Boolean(updateData.completed)
        if (sanitized.completed) {
            sanitized.completedTime = db.serverDate()
        }
    }

    if (updateData.diaryGenerated !== undefined) {
        sanitized.diaryGenerated = Boolean(updateData.diaryGenerated)
        if (sanitized.diaryGenerated) {
            sanitized.diaryGenerationTime = db.serverDate()
        }
    }

    if (updateData.diaryQualityScore !== undefined) {
        sanitized.diaryQualityScore = updateData.diaryQualityScore
    }

    if (updateData.diaryFallback !== undefined) {
        sanitized.diaryFallback = Boolean(updateData.diaryFallback)
    }

    // 添加更新时间
    sanitized.updateTime = db.serverDate()

    return sanitized
}

exports.main = async (event, context) => {
    console.log('更新三问记录请求:', JSON.stringify(event))

    const {
        recordId,           // 记录ID
        updateData,         // 要更新的数据
        upsert = false      // 是否在记录不存在时创建
    } = event

    const wxContext = cloud.getWXContext()
    const openid = wxContext.OPENID

    if (!openid) {
        return {
            success: false,
            error: '用户身份验证失败',
            errorType: 'AUTH_ERROR'
        }
    }

    if (!recordId) {
        return {
            success: false,
            error: '必须提供 recordId',
            errorType: 'MISSING_PARAM'
        }
    }

    if (!updateData || typeof updateData !== 'object') {
        return {
            success: false,
            error: '必须提供有效的 updateData 对象',
            errorType: 'INVALID_PARAM'
        }
    }

    try {
        // 验证更新数据
        const validation = validateUpdateData(updateData)
        if (!validation.valid) {
            return {
                success: false,
                error: '更新数据验证失败',
                validationErrors: validation.errors,
                errorType: 'VALIDATION_ERROR'
            }
        }

        // 首先检查记录是否存在且属于当前用户
        const existingRecord = await db.collection('three_questions_records').doc(recordId).get()

        if (!existingRecord.data) {
            if (upsert) {
                return {
                    success: false,
                    error: 'upsert 模式暂不支持，请使用 saveThreeQuestions 创建新记录',
                    errorType: 'UNSUPPORTED_OPERATION'
                }
            } else {
                return {
                    success: false,
                    error: '记录不存在',
                    errorType: 'NOT_FOUND'
                }
            }
        }

        if (existingRecord.data.openid !== openid) {
            return {
                success: false,
                error: '无权访问此记录',
                errorType: 'ACCESS_DENIED'
            }
        }

        // 清理和标准化更新数据
        const sanitizedData = sanitizeUpdateData(updateData)

        // 执行更新
        const updateResult = await db.collection('three_questions_records').doc(recordId).update({
            data: sanitizedData
        })

        // 获取更新后的记录
        const updatedRecord = await db.collection('three_questions_records').doc(recordId).get()

        console.log('三问记录更新成功:', {
            recordId,
            updatedFields: Object.keys(sanitizedData),
            stats: updateResult.stats
        })

        return {
            success: true,
            recordId,
            updatedFields: Object.keys(sanitizedData),
            updateStats: updateResult.stats,
            updatedRecord: updatedRecord.data
        }

    } catch (error) {
        console.error('更新三问记录失败:', error)

        return {
            success: false,
            error: error.message,
            errorCode: error.errCode,
            errorType: 'DATABASE_ERROR',
            context: {
                openid,
                recordId,
                updateFields: Object.keys(updateData || {})
            }
        }
    }
}