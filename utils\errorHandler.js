/**
 * 增强版错误处理工具函数 - 支持三问仪式的容错机制
 */

// 错误类型定义
const ERROR_TYPES = {
    NETWORK: 'network',
    TIMEOUT: 'timeout',
    VALIDATION: 'validation',
    AI_GENERATION: 'ai_generation',
    DATA_SAVE: 'data_save',
    USER_INPUT: 'user_input',
    SYSTEM: 'system',
    VIP_CHECK: 'vip_check',
    MESSAGE_SCHEDULE: 'message_schedule'
};

// 错误严重程度
const ERROR_SEVERITY = {
    LOW: 'low',       // 不影响核心功能
    MEDIUM: 'medium', // 影响部分功能
    HIGH: 'high',     // 影响核心功能
    CRITICAL: 'critical' // 系统无法正常工作
};

// 错误恢复策略
const RECOVERY_STRATEGIES = {
    RETRY: 'retry',
    FALLBACK: 'fallback',
    USER_ACTION: 'user_action',
    SKIP: 'skip',
    ABORT: 'abort'
};

// 显示错误提示
function showError(error, defaultMessage = '操作失败') {
    let message = defaultMessage;

    if (typeof error === 'string') {
        message = error;
    } else if (error && error.message) {
        message = error.message;
    } else if (error && error.errMsg) {
        message = error.errMsg;
    }

    wx.showToast({
        title: message,
        icon: 'none',
        duration: 2000
    });
}

// 显示网络错误
function showNetworkError() {
    wx.showToast({
        title: '网络连接失败，请检查网络后重试',
        icon: 'none',
        duration: 3000
    });
}

// 显示加载错误
function showLoadingError(message = '加载失败') {
    wx.showToast({
        title: message,
        icon: 'error',
        duration: 2000
    });
}

// 显示成功提示
function showSuccess(message = '操作成功') {
    wx.showToast({
        title: message,
        icon: 'success',
        duration: 1500
    });
}

// 显示确认对话框
function showConfirm(options = {}) {
    const defaultOptions = {
        title: '确认',
        content: '确定要执行此操作吗？',
        confirmText: '确定',
        cancelText: '取消',
        success: () => { },
        fail: () => { }
    };

    const finalOptions = { ...defaultOptions, ...options };

    wx.showModal({
        title: finalOptions.title,
        content: finalOptions.content,
        confirmText: finalOptions.confirmText,
        cancelText: finalOptions.cancelText,
        success: (res) => {
            if (res.confirm) {
                finalOptions.success();
            } else {
                finalOptions.fail();
            }
        }
    });
}

// 处理云函数调用错误
function handleCloudError(error, context = '') {
    console.error(`${context} 云函数调用失败:`, error);

    if (error.errCode === -1) {
        showNetworkError();
    } else if (error.errMsg && error.errMsg.includes('timeout')) {
        showError('请求超时，请重试');
    } else {
        showError(error.errMsg || '服务异常，请稍后重试');
    }
}

// 基础重试机制
function retryOperation(operation, maxRetries = 3, delay = 1000) {
    return new Promise((resolve, reject) => {
        let retries = 0;

        function attempt() {
            operation()
                .then(resolve)
                .catch(error => {
                    retries++;
                    if (retries < maxRetries) {
                        console.log(`操作失败，${delay}ms后进行第${retries + 1}次重试`);
                        setTimeout(attempt, delay);
                    } else {
                        reject(error);
                    }
                });
        }

        attempt();
    });
}

// 增强版网络重试机制 - 支持指数退避
function retryWithBackoff(operation, options = {}) {
    const {
        maxRetries = 3,
        baseDelay = 1000,
        maxDelay = 10000,
        backoffFactor = 2,
        jitter = true,
        retryCondition = null
    } = options;

    return new Promise((resolve, reject) => {
        let retries = 0;

        function attempt() {
            operation()
                .then(resolve)
                .catch(error => {
                    // 检查是否应该重试
                    if (retryCondition && !retryCondition(error)) {
                        reject(error);
                        return;
                    }

                    retries++;
                    if (retries < maxRetries) {
                        // 计算延迟时间（指数退避）
                        let delay = Math.min(baseDelay * Math.pow(backoffFactor, retries - 1), maxDelay);

                        // 添加随机抖动避免雷群效应
                        if (jitter) {
                            delay = delay * (0.5 + Math.random() * 0.5);
                        }

                        console.log(`操作失败，${Math.round(delay)}ms后进行第${retries + 1}次重试`, error.message);
                        setTimeout(attempt, delay);
                    } else {
                        console.error(`操作失败，已达到最大重试次数 ${maxRetries}`, error);
                        reject(error);
                    }
                });
        }

        attempt();
    });
}

// 网络状态检查
function checkNetworkStatus() {
    return new Promise((resolve) => {
        wx.getNetworkType({
            success: (res) => {
                const networkType = res.networkType;
                const isConnected = networkType !== 'none';
                resolve({
                    isConnected,
                    networkType,
                    isWifi: networkType === 'wifi',
                    isMobile: ['2g', '3g', '4g', '5g'].includes(networkType)
                });
            },
            fail: () => {
                resolve({
                    isConnected: false,
                    networkType: 'unknown'
                });
            }
        });
    });
}

// 智能错误分类和处理
function classifyError(error) {
    let errorType = ERROR_TYPES.SYSTEM;
    let severity = ERROR_SEVERITY.MEDIUM;
    let recoveryStrategy = RECOVERY_STRATEGIES.RETRY;

    if (!error) {
        return { errorType, severity, recoveryStrategy };
    }

    const errorMessage = error.message || error.errMsg || '';
    const errorCode = error.errCode || error.code;

    // 网络相关错误
    if (errorCode === -1 || errorMessage.includes('network') || errorMessage.includes('连接')) {
        errorType = ERROR_TYPES.NETWORK;
        severity = ERROR_SEVERITY.HIGH;
        recoveryStrategy = RECOVERY_STRATEGIES.RETRY;
    }
    // 超时错误
    else if (errorMessage.includes('timeout') || errorMessage.includes('超时')) {
        errorType = ERROR_TYPES.TIMEOUT;
        severity = ERROR_SEVERITY.MEDIUM;
        recoveryStrategy = RECOVERY_STRATEGIES.RETRY;
    }
    // 验证错误
    else if (errorMessage.includes('验证') || errorMessage.includes('validation')) {
        errorType = ERROR_TYPES.VALIDATION;
        severity = ERROR_SEVERITY.LOW;
        recoveryStrategy = RECOVERY_STRATEGIES.USER_ACTION;
    }
    // AI生成错误
    else if (errorMessage.includes('AI') || errorMessage.includes('生成')) {
        errorType = ERROR_TYPES.AI_GENERATION;
        severity = ERROR_SEVERITY.MEDIUM;
        recoveryStrategy = RECOVERY_STRATEGIES.FALLBACK;
    }
    // 数据保存错误
    else if (errorMessage.includes('保存') || errorMessage.includes('数据库')) {
        errorType = ERROR_TYPES.DATA_SAVE;
        severity = ERROR_SEVERITY.HIGH;
        recoveryStrategy = RECOVERY_STRATEGIES.RETRY;
    }

    return { errorType, severity, recoveryStrategy };
}

// 错误恢复处理器
async function handleErrorWithRecovery(error, context = '', options = {}) {
    const { errorType, severity, recoveryStrategy } = classifyError(error);

    console.error(`[${context}] 错误分类:`, {
        type: errorType,
        severity,
        strategy: recoveryStrategy,
        error: error.message || error
    });

    // 记录错误到分析系统
    await recordErrorMetrics(error, context, errorType, severity);

    switch (recoveryStrategy) {
        case RECOVERY_STRATEGIES.RETRY:
            return handleRetryableError(error, context, options);

        case RECOVERY_STRATEGIES.FALLBACK:
            return handleFallbackError(error, context, options);

        case RECOVERY_STRATEGIES.USER_ACTION:
            return handleUserActionError(error, context, options);

        case RECOVERY_STRATEGIES.SKIP:
            return handleSkippableError(error, context, options);

        default:
            return handleGenericError(error, context, options);
    }
}

// 处理可重试错误
async function handleRetryableError(error, context, options = {}) {
    const { maxRetries = 3, showUserFeedback = true } = options;

    if (showUserFeedback) {
        showError('网络异常，正在重试...');
    }

    try {
        // 检查网络状态
        const networkStatus = await checkNetworkStatus();
        if (!networkStatus.isConnected) {
            showNetworkError();
            return { success: false, error: '网络未连接', canRetry: false };
        }

        return { success: false, error: error.message, canRetry: true, maxRetries };
    } catch (checkError) {
        console.error('网络状态检查失败:', checkError);
        return { success: false, error: error.message, canRetry: true, maxRetries };
    }
}

// 处理需要备用方案的错误
async function handleFallbackError(error, context, options = {}) {
    const { fallbackData = null, showUserFeedback = true } = options;

    if (showUserFeedback) {
        showError('服务暂时不可用，使用备用方案');
    }

    console.log(`[${context}] 使用备用方案处理错误:`, error.message);

    return {
        success: true,
        useFallback: true,
        fallbackData,
        originalError: error.message
    };
}

// 处理需要用户操作的错误
async function handleUserActionError(error, context, options = {}) {
    const { userMessage = '请检查输入内容', showUserFeedback = true } = options;

    if (showUserFeedback) {
        showError(userMessage);
    }

    return {
        success: false,
        requiresUserAction: true,
        userMessage,
        error: error.message
    };
}

// 处理可跳过的错误
async function handleSkippableError(error, context, options = {}) {
    const { skipMessage = '该步骤已跳过', showUserFeedback = true } = options;

    if (showUserFeedback) {
        wx.showToast({
            title: skipMessage,
            icon: 'none',
            duration: 1500
        });
    }

    console.log(`[${context}] 跳过错误:`, error.message);

    return {
        success: true,
        skipped: true,
        skipReason: error.message
    };
}

// 处理通用错误
async function handleGenericError(error, context, options = {}) {
    const { showUserFeedback = true } = options;

    if (showUserFeedback) {
        showError('操作失败，请稍后重试');
    }

    return {
        success: false,
        error: error.message,
        context
    };
}

// 记录错误指标到分析系统
async function recordErrorMetrics(error, context, errorType, severity) {
    try {
        // 获取用户信息
        const userInfo = wx.getStorageSync('userInfo') || {};

        await wx.cloud.callFunction({
            name: 'tieringAnalytics',
            data: {
                type: 'recordErrorMetrics',
                data: {
                    errorType,
                    severity,
                    context,
                    errorMessage: error.message || error.toString(),
                    errorCode: error.errCode || error.code,
                    timestamp: new Date(),
                    userLevel: userInfo.userLevel || 0,
                    stackTrace: error.stack ? error.stack.substring(0, 500) : null
                }
            }
        });
    } catch (recordError) {
        console.error('记录错误指标失败:', recordError);
        // 不影响主流程
    }
}

// 数据恢复机制
function createDataRecoveryManager() {
    const RECOVERY_KEY_PREFIX = 'recovery_';

    return {
        // 保存恢复数据
        saveRecoveryData(key, data) {
            try {
                const recoveryKey = RECOVERY_KEY_PREFIX + key;
                const recoveryData = {
                    data,
                    timestamp: Date.now(),
                    version: '1.0'
                };
                wx.setStorageSync(recoveryKey, recoveryData);
                console.log(`恢复数据已保存: ${key}`);
            } catch (error) {
                console.error('保存恢复数据失败:', error);
            }
        },

        // 获取恢复数据
        getRecoveryData(key, maxAge = 3600000) { // 默认1小时过期
            try {
                const recoveryKey = RECOVERY_KEY_PREFIX + key;
                const recoveryData = wx.getStorageSync(recoveryKey);

                if (!recoveryData) {
                    return null;
                }

                const age = Date.now() - recoveryData.timestamp;
                if (age > maxAge) {
                    this.clearRecoveryData(key);
                    return null;
                }

                return recoveryData.data;
            } catch (error) {
                console.error('获取恢复数据失败:', error);
                return null;
            }
        },

        // 清除恢复数据
        clearRecoveryData(key) {
            try {
                const recoveryKey = RECOVERY_KEY_PREFIX + key;
                wx.removeStorageSync(recoveryKey);
                console.log(`恢复数据已清除: ${key}`);
            } catch (error) {
                console.error('清除恢复数据失败:', error);
            }
        },

        // 清除所有过期的恢复数据
        cleanupExpiredRecoveryData(maxAge = 3600000) {
            try {
                const storageInfo = wx.getStorageInfoSync();
                const keysToRemove = [];

                storageInfo.keys.forEach(key => {
                    if (key.startsWith(RECOVERY_KEY_PREFIX)) {
                        try {
                            const data = wx.getStorageSync(key);
                            if (data && data.timestamp) {
                                const age = Date.now() - data.timestamp;
                                if (age > maxAge) {
                                    keysToRemove.push(key);
                                }
                            }
                        } catch (error) {
                            keysToRemove.push(key); // 损坏的数据也删除
                        }
                    }
                });

                keysToRemove.forEach(key => {
                    wx.removeStorageSync(key);
                });

                console.log(`清理了 ${keysToRemove.length} 个过期恢复数据`);
            } catch (error) {
                console.error('清理过期恢复数据失败:', error);
            }
        }
    };
}

module.exports = {
    // 基础错误处理函数
    showError,
    showNetworkError,
    showLoadingError,
    showSuccess,
    showConfirm,
    handleCloudError,
    retryOperation,

    // 增强版错误处理函数
    retryWithBackoff,
    checkNetworkStatus,
    classifyError,
    handleErrorWithRecovery,
    handleRetryableError,
    handleFallbackError,
    handleUserActionError,
    handleSkippableError,
    handleGenericError,
    recordErrorMetrics,
    createDataRecoveryManager,

    // 常量导出
    ERROR_TYPES,
    ERROR_SEVERITY,
    RECOVERY_STRATEGIES
};