const cloud = require('wx-server-sdk')

cloud.init({
    env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 错误类型定义
const ERROR_TYPES = {
    VALIDATION_ERROR: 'validation_error',
    DATABASE_ERROR: 'database_error',
    NETWORK_ERROR: 'network_error',
    SYSTEM_ERROR: 'system_error',
    TIMEOUT_ERROR: 'timeout_error',
    PERMISSION_ERROR: 'permission_error'
}

// 验证星座位置数据
function validateStarPosition(starPosition) {
    const errors = []

    if (!starPosition || typeof starPosition !== 'object') {
        errors.push('starPosition 必须是对象类型')
        return { valid: false, errors }
    }

    // 验证位置坐标
    if (starPosition.x === undefined || starPosition.y === undefined) {
        errors.push('starPosition 必须包含 x 和 y 坐标')
    } else {
        if (typeof starPosition.x !== 'number' || starPosition.x < 0 || starPosition.x > 1) {
            errors.push('starPosition.x 必须是0-1之间的数字')
        }
        if (typeof starPosition.y !== 'number' || starPosition.y < 0 || starPosition.y > 1) {
            errors.push('starPosition.y 必须是0-1之间的数字')
        }
    }

    // 验证轨道信息（可选）
    if (starPosition.anchorStarId !== undefined && typeof starPosition.anchorStarId !== 'string') {
        errors.push('starPosition.anchorStarId 必须是字符串类型')
    }

    if (starPosition.orbitRadius !== undefined) {
        if (typeof starPosition.orbitRadius !== 'number' || starPosition.orbitRadius < 0) {
            errors.push('starPosition.orbitRadius 必须是非负数')
        }
    }

    if (starPosition.orbitAngle !== undefined) {
        if (typeof starPosition.orbitAngle !== 'number') {
            errors.push('starPosition.orbitAngle 必须是数字类型')
        }
    }

    return { valid: errors.length === 0, errors }
}

// 验证星座数据
function validateConstellationData(constellationData) {
    const errors = []

    if (!constellationData || typeof constellationData !== 'object') {
        errors.push('constellationData 必须是对象类型')
        return { valid: false, errors }
    }

    // 验证用户ID
    if (!constellationData.userId || typeof constellationData.userId !== 'string') {
        errors.push('constellationData.userId 必须是非空字符串')
    }

    // 验证星星数组
    if (constellationData.stars !== undefined) {
        if (!Array.isArray(constellationData.stars)) {
            errors.push('constellationData.stars 必须是数组类型')
        } else {
            // 验证每个星星的数据
            constellationData.stars.forEach((star, index) => {
                if (!star.id || typeof star.id !== 'string') {
                    errors.push(`constellationData.stars[${index}].id 必须是非空字符串`)
                }

                const positionValidation = validateStarPosition(star.position)
                if (!positionValidation.valid) {
                    errors.push(...positionValidation.errors.map(err => `constellationData.stars[${index}].position: ${err}`))
                }
            })
        }
    }

    // 验证元数据
    if (constellationData.metadata !== undefined) {
        if (typeof constellationData.metadata !== 'object') {
            errors.push('constellationData.metadata 必须是对象类型')
        }
    }

    return { valid: errors.length === 0, errors }
}

// 创建错误响应
function createErrorResponse(errorType, message, details = {}) {
    return {
        success: false,
        error: message,
        errorType,
        timestamp: new Date().toISOString(),
        details
    }
}

// 记录错误到日志系统
async function logError(error, context, additionalData = {}) {
    try {
        const errorLog = {
            timestamp: new Date().toISOString(),
            context,
            error: {
                message: error.message,
                stack: error.stack,
                code: error.errCode || error.code
            },
            additionalData
        }

        console.error(`[错误日志] ${context}:`, errorLog)
    } catch (logError) {
        console.error('记录错误日志失败:', logError)
    }
}

exports.main = async (event, context) => {
    const startTime = Date.now()
    let operationContext = 'updateStarPosition'

    try {
        console.log('收到的事件参数:', JSON.stringify(event))

        const {
            recordId,           // 三问记录ID
            starPosition,       // 星星位置数据
            constellationData,  // 完整星座数据（可选）
            operation = 'update' // 操作类型：update, create, delete
        } = event

        // 获取用户身份
        const wxContext = cloud.getWXContext()
        const openid = wxContext.OPENID

        if (!openid) {
            return createErrorResponse(ERROR_TYPES.PERMISSION_ERROR, '用户身份验证失败')
        }

        // 验证必要参数
        if (!recordId || typeof recordId !== 'string') {
            return createErrorResponse(ERROR_TYPES.VALIDATION_ERROR, 'recordId 必须是非空字符串')
        }

        // 验证星座位置数据
        if (operation !== 'delete') {
            const positionValidation = validateStarPosition(starPosition)
            if (!positionValidation.valid) {
                return createErrorResponse(ERROR_TYPES.VALIDATION_ERROR, '星座位置数据验证失败', {
                    validationErrors: positionValidation.errors
                })
            }
        }

        // 验证星座数据（如果提供）
        if (constellationData) {
            const constellationValidation = validateConstellationData(constellationData)
            if (!constellationValidation.valid) {
                return createErrorResponse(ERROR_TYPES.VALIDATION_ERROR, '星座数据验证失败', {
                    validationErrors: constellationValidation.errors
                })
            }
        }

        try {
            // 首先验证记录是否存在且属于当前用户
            const recordQuery = await db.collection('three_questions_records')
                .where({
                    _id: recordId,
                    openid: openid
                })
                .get()

            if (recordQuery.data.length === 0) {
                return createErrorResponse(ERROR_TYPES.PERMISSION_ERROR, '记录不存在或无权限访问')
            }

            const existingRecord = recordQuery.data[0]
            console.log('找到现有记录:', existingRecord._id)

            let updateData = {}
            let operationResult = null

            switch (operation) {
                case 'update':
                case 'create':
                    // 更新或创建星星位置
                    updateData = {
                        starPosition: {
                            ...starPosition,
                            updatedAt: new Date().toISOString(),
                            updatedBy: openid
                        },
                        lastPositionUpdate: db.serverDate()
                    }

                    // 如果提供了完整的星座数据，也一并更新
                    if (constellationData) {
                        updateData.constellationData = {
                            ...constellationData,
                            userId: openid,
                            updatedAt: new Date().toISOString(),
                            version: (existingRecord.constellationData?.version || 0) + 1
                        }
                    }

                    operationResult = await db.collection('three_questions_records')
                        .doc(recordId)
                        .update({
                            data: updateData
                        })

                    console.log('星星位置更新成功:', operationResult)
                    break

                case 'delete':
                    // 删除星星位置数据
                    updateData = {
                        starPosition: _.remove(),
                        constellationData: constellationData ? _.remove() : undefined,
                        lastPositionUpdate: db.serverDate()
                    }

                    // 移除 undefined 值
                    Object.keys(updateData).forEach(key => {
                        if (updateData[key] === undefined) {
                            delete updateData[key]
                        }
                    })

                    operationResult = await db.collection('three_questions_records')
                        .doc(recordId)
                        .update({
                            data: updateData
                        })

                    console.log('星星位置删除成功:', operationResult)
                    break

                default:
                    return createErrorResponse(ERROR_TYPES.VALIDATION_ERROR, `不支持的操作类型: ${operation}`)
            }

            // 更新用户的星座统计信息
            try {
                await updateUserConstellationStats(openid, operation, starPosition)
            } catch (statsError) {
                console.error('更新用户星座统计失败:', statsError)
                // 不影响主流程
            }

            return {
                success: true,
                operation,
                recordId,
                starPosition: operation !== 'delete' ? starPosition : null,
                constellationData: operation !== 'delete' ? constellationData : null,
                updateResult: operationResult,
                timestamp: new Date().toISOString()
            }

        } catch (dbError) {
            console.error('数据库操作失败:', dbError)

            await logError(dbError, operationContext, {
                openid,
                recordId,
                operation,
                executionTime: Date.now() - startTime
            })

            if (dbError.message.includes('timeout')) {
                return createErrorResponse(ERROR_TYPES.TIMEOUT_ERROR, '数据库操作超时，请重试')
            } else if (dbError.message.includes('network') || dbError.errCode === -1) {
                return createErrorResponse(ERROR_TYPES.NETWORK_ERROR, '网络连接失败，请检查网络后重试')
            } else {
                return createErrorResponse(ERROR_TYPES.DATABASE_ERROR, '数据库操作失败，请稍后重试', {
                    errorCode: dbError.errCode,
                    errorMessage: dbError.message
                })
            }
        }

    } catch (outerError) {
        console.error('云函数执行发生严重错误:', outerError)

        await logError(outerError, 'updateStarPosition_critical', {
            executionTime: Date.now() - startTime,
            originalContext: operationContext
        })

        return createErrorResponse(ERROR_TYPES.SYSTEM_ERROR, '系统发生严重错误，请联系技术支持', {
            errorCode: outerError.errCode || 'CRITICAL_ERROR',
            timestamp: new Date().toISOString()
        })
    }
}

// 更新用户星座统计信息
async function updateUserConstellationStats(openid, operation, starPosition) {
    try {
        const statsUpdate = {
            lastConstellationUpdate: db.serverDate()
        }

        switch (operation) {
            case 'create':
                statsUpdate.totalStars = _.inc(1)
                statsUpdate.firstStarDate = _.setOnInsert(db.serverDate())
                break

            case 'update':
                statsUpdate.lastStarUpdate = db.serverDate()
                break

            case 'delete':
                statsUpdate.totalStars = _.inc(-1)
                break
        }

        await db.collection('users').doc(openid).update({
            data: {
                constellationStats: statsUpdate
            },
            upsert: true
        })

        console.log('用户星座统计更新成功')
    } catch (error) {
        console.error('更新用户星座统计失败:', error)
        throw error
    }
}