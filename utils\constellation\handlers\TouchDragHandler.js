/**
 * 触摸拖拽处理器
 * 负责处理星星的拖拽操作和轨道约束
 */

class TouchDragHandler {
    constructor() {
        this.isDragging = false
        this.dragTarget = null
        this.startPosition = { x: 0, y: 0 }
        this.currentPosition = { x: 0, y: 0 }
        this.orbitConstraints = null
        this.dragThreshold = 10 // 拖拽阈值，防止误触
        this.onDragStart = null
        this.onDragMove = null
        this.onDragEnd = null
    }

    /**
     * 初始化触摸拖拽处理器
     */
    initialize() {
        console.log('初始化触摸拖拽处理器...')
        return { success: true }
    }

    /**
     * 开始拖拽操作
     */
    startDrag(element, startX, startY, constraints = null) {
        if (this.isDragging) {
            this.endDrag()
        }

        this.isDragging = true
        this.dragTarget = element
        this.startPosition = { x: startX, y: startY }
        this.currentPosition = { x: startX, y: startY }
        this.orbitConstraints = constraints

        // 添加拖拽样式
        if (element && element.style) {
            element.style.zIndex = '1000'
            element.style.transform = 'scale(1.1)'
            element.style.opacity = '0.8'
        }

        // 触发拖拽开始回调
        if (this.onDragStart) {
            this.onDragStart({
                element,
                startPosition: this.startPosition,
                constraints: this.orbitConstraints
            })
        }

        console.log('开始拖拽:', { startX, startY })
        return true
    }

    /**
     * 更新拖拽位置
     */
    updateDrag(currentX, currentY) {
        if (!this.isDragging) return false

        // 检查是否超过拖拽阈值
        const deltaX = currentX - this.startPosition.x
        const deltaY = currentY - this.startPosition.y
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)

        if (distance < this.dragThreshold) {
            return false
        }

        // 应用轨道约束
        const constrainedPosition = this.applyOrbitConstraints(currentX, currentY)
        this.currentPosition = constrainedPosition

        // 更新元素位置
        if (this.dragTarget && this.dragTarget.style) {
            this.dragTarget.style.left = `${constrainedPosition.x}px`
            this.dragTarget.style.top = `${constrainedPosition.y}px`
        }

        // 触发拖拽移动回调
        if (this.onDragMove) {
            this.onDragMove({
                element: this.dragTarget,
                currentPosition: constrainedPosition,
                originalPosition: this.startPosition,
                deltaX: constrainedPosition.x - this.startPosition.x,
                deltaY: constrainedPosition.y - this.startPosition.y
            })
        }

        return true
    }

    /**
     * 结束拖拽操作
     */
    endDrag() {
        if (!this.isDragging) return false

        const dragData = {
            element: this.dragTarget,
            startPosition: this.startPosition,
            endPosition: this.currentPosition,
            deltaX: this.currentPosition.x - this.startPosition.x,
            deltaY: this.currentPosition.y - this.startPosition.y
        }

        // 恢复元素样式
        if (this.dragTarget && this.dragTarget.style) {
            this.dragTarget.style.zIndex = ''
            this.dragTarget.style.transform = ''
            this.dragTarget.style.opacity = ''
        }

        // 触发拖拽结束回调
        if (this.onDragEnd) {
            this.onDragEnd(dragData)
        }

        // 重置状态
        this.isDragging = false
        this.dragTarget = null
        this.startPosition = { x: 0, y: 0 }
        this.currentPosition = { x: 0, y: 0 }
        this.orbitConstraints = null

        console.log('结束拖拽:', dragData)
        return dragData
    }

    /**
     * 应用轨道约束
     */
    applyOrbitConstraints(x, y) {
        if (!this.orbitConstraints) {
            return { x, y }
        }

        const { type, center, radius, bounds } = this.orbitConstraints

        switch (type) {
            case 'circular':
                return this.constrainToCircularOrbit(x, y, center, radius)
            
            case 'rectangular':
                return this.constrainToRectangularBounds(x, y, bounds)
            
            case 'elliptical':
                return this.constrainToEllipticalOrbit(x, y, center, radius)
            
            default:
                return { x, y }
        }
    }

    /**
     * 约束到圆形轨道
     */
    constrainToCircularOrbit(x, y, center, radius) {
        const deltaX = x - center.x
        const deltaY = y - center.y
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)

        if (distance <= radius) {
            return { x, y }
        }

        // 将点投影到圆周上
        const angle = Math.atan2(deltaY, deltaX)
        return {
            x: center.x + Math.cos(angle) * radius,
            y: center.y + Math.sin(angle) * radius
        }
    }

    /**
     * 约束到矩形边界
     */
    constrainToRectangularBounds(x, y, bounds) {
        return {
            x: Math.max(bounds.left, Math.min(bounds.right, x)),
            y: Math.max(bounds.top, Math.min(bounds.bottom, y))
        }
    }

    /**
     * 约束到椭圆轨道
     */
    constrainToEllipticalOrbit(x, y, center, radius) {
        const deltaX = x - center.x
        const deltaY = y - center.y
        
        // 简化的椭圆约束（使用不同的x和y半径）
        const radiusX = radius.x || radius
        const radiusY = radius.y || radius
        
        const normalizedX = deltaX / radiusX
        const normalizedY = deltaY / radiusY
        const distance = Math.sqrt(normalizedX * normalizedX + normalizedY * normalizedY)

        if (distance <= 1) {
            return { x, y }
        }

        // 投影到椭圆边界
        const angle = Math.atan2(normalizedY, normalizedX)
        return {
            x: center.x + Math.cos(angle) * radiusX,
            y: center.y + Math.sin(angle) * radiusY
        }
    }

    /**
     * 设置拖拽回调函数
     */
    setDragCallbacks(callbacks) {
        this.onDragStart = callbacks.onDragStart || null
        this.onDragMove = callbacks.onDragMove || null
        this.onDragEnd = callbacks.onDragEnd || null
    }

    /**
     * 获取当前拖拽状态
     */
    getDragState() {
        return {
            isDragging: this.isDragging,
            dragTarget: this.dragTarget,
            startPosition: { ...this.startPosition },
            currentPosition: { ...this.currentPosition },
            hasConstraints: !!this.orbitConstraints
        }
    }

    /**
     * 取消当前拖拽
     */
    cancelDrag() {
        if (!this.isDragging) return false

        // 恢复到起始位置
        if (this.dragTarget && this.dragTarget.style) {
            this.dragTarget.style.left = `${this.startPosition.x}px`
            this.dragTarget.style.top = `${this.startPosition.y}px`
            this.dragTarget.style.zIndex = ''
            this.dragTarget.style.transform = ''
            this.dragTarget.style.opacity = ''
        }

        // 重置状态
        this.isDragging = false
        this.dragTarget = null
        this.startPosition = { x: 0, y: 0 }
        this.currentPosition = { x: 0, y: 0 }
        this.orbitConstraints = null

        console.log('取消拖拽操作')
        return true
    }

    /**
     * 设置拖拽阈值
     */
    setDragThreshold(threshold) {
        this.dragThreshold = Math.max(0, threshold)
    }

    /**
     * 检查点是否在轨道约束内
     */
    isPointInConstraints(x, y) {
        if (!this.orbitConstraints) return true

        const constrainedPoint = this.applyOrbitConstraints(x, y)
        const tolerance = 5 // 5像素容差

        return Math.abs(constrainedPoint.x - x) <= tolerance && 
               Math.abs(constrainedPoint.y - y) <= tolerance
    }

    /**
     * 销毁拖拽处理器
     */
    destroy() {
        if (this.isDragging) {
            this.cancelDrag()
        }
        
        this.onDragStart = null
        this.onDragMove = null
        this.onDragEnd = null
        
        console.log('触摸拖拽处理器已销毁')
    }
}

// 创建全局实例
const touchDragHandler = new TouchDragHandler()

export default touchDragHandler
