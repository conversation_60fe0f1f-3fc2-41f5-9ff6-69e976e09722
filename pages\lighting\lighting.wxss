/* 微光点灯仪式 - 样式文件 */

/* 基础容器 */
.lighting-container {
  width: 100vw;
  height: 100vh;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #000;
}

/* 星空背景系统 */
.starry-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

/* 深空背景渐变 */
.deep-space {
  position: absolute;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    45deg,
    #000000 0%,
    #0a0a1e 25%,
    #1a1a3a 50%,
    #2a2a4a 75%,
    #000000 100%
  );
  opacity: 0.8;
}

/* 星星层级系统 */
.stars {
  position: absolute;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(2px 2px at 20px 30px, rgba(255,255,255,0.8), transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.6), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(255,255,255,0.9), transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.7), transparent),
    radial-gradient(2px 2px at 160px 30px, rgba(255,255,255,0.8), transparent);
  background-repeat: repeat;
  background-size: 200px 100px;
  animation: sparkle 8s linear infinite;
}

.stars-small {
  background-size: 150px 80px;
  animation-duration: 6s;
  opacity: 0.7;
}

.stars-medium {
  background-size: 250px 120px;
  animation-duration: 10s;
  animation-delay: 2s;
  opacity: 0.5;
}

.stars-large {
  background-size: 300px 150px;
  animation-duration: 12s;
  animation-delay: 4s;
  opacity: 0.3;
}

/* 星星闪烁动画 */
@keyframes sparkle {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 1; }
}

/* 步骤容器基础样式 */
.step {
  position: relative;
  z-index: 10;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transform: translateY(50rpx);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.step.active {
  opacity: 1;
  transform: translateY(0);
}

/* 通用文字样式 */
.main-text {
  font-size: 48rpx;
  font-weight: 300;
  color: rgba(255, 255, 255, 0.95);
  letter-spacing: 2rpx;
  line-height: 1.5;
  text-align: center;
  display: block;
  margin-bottom: 20rpx;
}

.sub-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.7);
  letter-spacing: 1rpx;
  line-height: 1.6;
  text-align: center;
  display: block;
  margin-bottom: 40rpx;
}

.detail-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
  letter-spacing: 1rpx;
  line-height: 1.6;
  text-align: center;
  display: block;
  margin-top: 30rpx;
  padding: 0 60rpx;
}

/* 步骤1: 欢迎页面 */
.welcome-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.welcome-text {
  margin-bottom: 100rpx;
}

.continue-hint {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.5);
  animation: float 3s ease-in-out infinite;
}

/* 浮动动画 */
@keyframes float {
  0%, 100% { transform: translateY(0); opacity: 0.5; }
  50% { transform: translateY(-10rpx); opacity: 0.8; }
}

/* 淡入动画 */
.animate-fade-in {
  animation: fadeIn 2s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(30rpx); }
  to { opacity: 1; transform: translateY(0); }
}

/* 步骤2: 点灯页面 */
.lighting-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.lamp-container {
  position: relative;
  margin-bottom: 120rpx;
}

/* 灯的基础样式 */
.lamp {
  position: relative;
  width: 120rpx;
  height: 160rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}

.lamp-body {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80rpx;
  height: 100rpx;
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0.1) 100%
  );
  border-radius: 40rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  transition: all 1.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 灯光效果 */
.lamp-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80rpx;
  height: 100rpx;
  background: radial-gradient(
    ellipse at center,
    rgba(255, 218, 112, 0) 0%,
    rgba(255, 218, 112, 0.1) 30%,
    rgba(255, 218, 112, 0.3) 60%,
    rgba(255, 218, 112, 0) 100%
  );
  border-radius: 40rpx;
  opacity: 0;
  transform: translate(-50%, -50%) scale(1);
  transition: all 1.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.lamp-glow.glowing {
  opacity: 1;
  transform: translate(-50%, -50%) scale(2.5);
  background: radial-gradient(
    ellipse at center,
    rgba(255, 218, 112, 0.3) 0%,
    rgba(255, 218, 112, 0.2) 30%,
    rgba(255, 218, 112, 0.1) 60%,
    rgba(255, 218, 112, 0) 100%
  );
}

/* 点亮时的灯体变化 */
.lamp.lighting .lamp-body {
  background: linear-gradient(
    180deg,
    rgba(255, 218, 112, 0.9) 0%,
    rgba(255, 235, 156, 1) 50%,
    rgba(255, 218, 112, 0.9) 100%
  );
  border-color: rgba(255, 218, 112, 0.8);
  box-shadow: 
    0 0 20rpx rgba(255, 218, 112, 0.4),
    0 0 40rpx rgba(255, 218, 112, 0.2),
    inset 0 0 20rpx rgba(255, 235, 156, 0.3);
}

/* 光线扩散效果 */
.light-rays {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200rpx;
  height: 200rpx;
  opacity: 0;
  transition: all 2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.light-rays.expanding {
  opacity: 1;
  transform: translate(-50%, -50%) scale(3);
}

.ray {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 2rpx;
  height: 60rpx;
  background: linear-gradient(
    to bottom,
    rgba(255, 218, 112, 0.8) 0%,
    rgba(255, 218, 112, 0) 100%
  );
  transform-origin: center bottom;
}

.ray-1 { transform: translate(-50%, -100%) rotate(0deg); }
.ray-2 { transform: translate(-50%, -100%) rotate(90deg); }
.ray-3 { transform: translate(-50%, -100%) rotate(180deg); }
.ray-4 { transform: translate(-50%, -100%) rotate(270deg); }

/* 点灯文字 */
.lighting-text {
  opacity: 0;
  transform: translateY(20rpx);
  transition: all 1s ease-out 1s;
}

.lighting-text.show {
  opacity: 1;
  transform: translateY(0);
}

/* 步骤3&4: 命名页面 */
.name-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 0 60rpx;
}

/* 小灯显示 */
.lamp.small {
  width: 80rpx;
  height: 120rpx;
  margin-bottom: 80rpx;
}

.lamp.small .lamp-body {
  width: 60rpx;
  height: 80rpx;
}

.lamp.small .lamp-glow {
  width: 60rpx;
  height: 80rpx;
}

/* 头像容器 */
.avatar-container {
  position: relative;
  margin-bottom: 80rpx;
}

.avatar-silhouette {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0.1) 100%
  );
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.avatar-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(2);
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background: radial-gradient(
    circle at center,
    rgba(255, 218, 112, 0.2) 0%,
    rgba(255, 218, 112, 0.1) 50%,
    rgba(255, 218, 112, 0) 100%
  );
  animation: pulse 3s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: translate(-50%, -50%) scale(2); opacity: 0.5; }
  50% { transform: translate(-50%, -50%) scale(2.5); opacity: 0.8; }
}

/* 输入框样式 */
.input-container {
  position: relative;
  margin: 60rpx 0;
  width: 100%;
  max-width: 500rpx;
}

.lamp-name-input,
.self-name-input {
  width: 100%;
  height: 80rpx;
  background: transparent;
  border: none;
  outline: none;
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.9);
  text-align: center;
  letter-spacing: 2rpx;
}

.input-placeholder {
  color: rgba(255, 255, 255, 0.4);
}

.input-underline {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 2rpx;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 218, 112, 0.8) 50%,
    transparent 100%
  );
  transition: width 0.3s ease;
}

.input-container input:focus + .input-underline {
  width: 100%;
}

/* 继续按钮 */
.continue-btn {
  margin-top: 80rpx;
  padding: 20rpx 60rpx;
  background: transparent;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 50rpx;
  opacity: 0.5;
  transform: scale(0.95);
  transition: all 0.3s ease;
}

.continue-btn.active {
  opacity: 1;
  transform: scale(1);
  border-color: rgba(255, 218, 112, 0.6);
  background: rgba(255, 218, 112, 0.1);
  box-shadow: 0 0 20rpx rgba(255, 218, 112, 0.2);
}

.continue-btn text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  letter-spacing: 1rpx;
}

.continue-btn.active text {
  color: rgba(255, 255, 255, 0.95);
}

/* 步骤5: 问题页面 */
.questions-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  height: 100%;
  padding: 120rpx 60rpx 80rpx;
}

.question-header {
  text-align: center;
  margin-bottom: 80rpx;
}

.question-number {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.5);
  display: block;
  margin-bottom: 20rpx;
}

.question-title {
  font-size: 36rpx;
  font-weight: 300;
  color: rgba(255, 255, 255, 0.95);
  letter-spacing: 1rpx;
  line-height: 1.5;
  display: block;
  margin-bottom: 20rpx;
}

.question-desc {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.6);
  line-height: 1.6;
  display: block;
}

/* 选项容器 */
.options-container {
  width: 100%;
  max-width: 600rpx;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.option-item {
  display: flex;
  align-items: center;
  padding: 30rpx 40rpx;
  background: rgba(255, 255, 255, 0.05);
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  transition: all 0.3s ease;
  cursor: pointer;
}

.option-item.selected {
  background: rgba(255, 218, 112, 0.1);
  border-color: rgba(255, 218, 112, 0.5);
  box-shadow: 0 0 15rpx rgba(255, 218, 112, 0.2);
}

.option-icon {
  position: relative;
  width: 24rpx;
  height: 24rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.4);
  border-radius: 50%;
  margin-right: 30rpx;
  transition: all 0.3s ease;
}

.option-item.selected .option-icon {
  border-color: rgba(255, 218, 112, 0.8);
}

.option-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 12rpx;
  height: 12rpx;
  background: rgba(255, 218, 112, 0.9);
  border-radius: 50%;
  opacity: 0;
  transform: translate(-50%, -50%) scale(0);
  transition: all 0.3s ease;
}

.option-glow.active {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
}

.option-text {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
  letter-spacing: 1rpx;
}

.option-item.selected .option-text {
  color: rgba(255, 255, 255, 0.95);
}

/* 步骤6: 完成页面 */
.complete-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
}

.celebration-animation {
  position: relative;
  margin-bottom: 100rpx;
}

.lamp.celebration {
  width: 140rpx;
  height: 180rpx;
  animation: celebrationPulse 2s ease-in-out infinite;
}

@keyframes celebrationPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* 庆祝粒子效果 */
.particles {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 300rpx;
  height: 300rpx;
}

.particle {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 6rpx;
  height: 6rpx;
  background: rgba(255, 218, 112, 0.8);
  border-radius: 50%;
  transform-origin: 0 150rpx;
  animation: particleFloat 3s ease-out infinite;
  animation-delay: var(--delay);
  transform: rotate(var(--rotation)) translateY(-150rpx);
}

@keyframes particleFloat {
  0% {
    opacity: 1;
    transform: rotate(var(--rotation)) translateY(-150rpx) scale(1);
  }
  100% {
    opacity: 0;
    transform: rotate(var(--rotation)) translateY(-250rpx) scale(0.5);
  }
}

.complete-text {
  margin-bottom: 80rpx;
}

/* 进入按钮 */
.enter-btn {
  padding: 25rpx 80rpx;
  background: linear-gradient(
    135deg,
    rgba(255, 218, 112, 0.2) 0%,
    rgba(255, 218, 112, 0.3) 100%
  );
  border: 2rpx solid rgba(255, 218, 112, 0.6);
  border-radius: 50rpx;
  box-shadow: 
    0 0 20rpx rgba(255, 218, 112, 0.3),
    inset 0 0 15rpx rgba(255, 218, 112, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.enter-btn:active {
  transform: scale(0.98);
  box-shadow: 
    0 0 15rpx rgba(255, 218, 112, 0.4),
    inset 0 0 20rpx rgba(255, 218, 112, 0.2);
}

.enter-btn text {
  font-size: 32rpx;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.95);
  letter-spacing: 2rpx;
}

/* 响应式调整 */
@media (max-width: 750px) {
  .main-text {
    font-size: 40rpx;
  }
  
  .lamp-container {
    margin-bottom: 80rpx;
  }
  
  .questions-content {
    padding: 80rpx 40rpx 60rpx;
  }
}