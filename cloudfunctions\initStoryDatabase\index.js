const cloud = require('wx-server-sdk');

cloud.init({
    env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
    const { action } = event;

    try {
        switch (action) {
            case 'initAllData':
                return await initAllData();
            case 'clearAll':
                return await clearAllData();
            default:
                return { success: false, error: '未知操作' };
        }
    } catch (error) {
        console.error('initStoryDatabase error:', error);
        return { success: false, error: error.message };
    }
};

// 初始化所有数据 - 嵌套结构
async function initAllData() {
    const charactersData = [
        {
            type: 'character',
            name: '熊猫噗噗',
            image: 'cloud://cloudbase-8gji862jcfb501e7.636c-cloudbase-8gji862jcfb501e7-1365531166/images/story_icon_pupu.png',
            tagline: '寻找安宁与勇气的烘焙师',
            description: '在所有关切的声音里，他低下头，用自己圆滚滚的身体，轻轻地、却很坚决地，挤开了围拢的乡亲们。',
            order: 1,
            isActive: true,
            chapters: [
                {
                    id: 1,
                    title: '竹林里的邀请函',
                    isFree: true,
                    cost: 0,
                    order: 1,
                    content: {
                        title: '竹林里的邀请函',
                        text: `熊猫村一年一度的"滚滚美食节"，热闹得像一锅沸腾的甜粥，空气里挤满了各种食物的香气和兴奋的交谈声。

噗噗的灶台前总是最拥挤的。

"噗噗呀！"三叔公的大嗓门像敲锣，他抱着一个快比他脑袋还大的蜂蜜罐子，热情地嚷道，"听三叔公的，多放蜜！甜滋滋的，才是让熊开心的味道啊！"金灿灿的蜜糖眼看就要淌进锅里。

"哎哟，可不能光听他的！"戴着碎花头巾的七婶婆灵活地挤进来，举着一篮子带着泥土清香的绿草，"小孩子吃太甜不好，加这个'清心草'，味道才正！"她的话语带着不容置疑的关心。

很快，噗噗的头顶就被"蜂蜜派"和"草药派"的声音填满了。他们不是争吵，只是各自坚持着那份"为你好"的心意，热切地塞进噗噗的耳朵里。

噗噗感觉自己像被塞得太满的竹筒。小小的脑袋嗡嗡作响，连锅里竹笋欢快的"咕嘟"声都听不清了。

往常遇到这种情况，噗噗总是手足无措，最后只能任由大家把他的小厨房变成一锅滋味混杂的"热闹汤"。

但今天，不知怎的，一股小小的烦躁涌了上来。他不想再做那个被所有人夸赞、自己却并不真正喜欢的"热闹面包"了。他只想安安静静地，烤一块只属于自己的、带着竹林清风的饼干。

于是，噗噗做了一件他从未做过的事。

他轻轻放下了手里的小锅铲，"嗒"的一声轻响。然后，在所有关切的声音里，他低下头，用自己圆滚滚的身体，轻轻地、却很坚决地，挤开了围拢的乡亲们，头也不回地跑掉了。

他跑进了那片熟悉的、属于他的安静竹林。风拂过竹叶，发出温柔的沙沙声。噗噗靠在一棵粗壮的竹子上，长长地、深深地舒了一口气，仿佛要把胸腔里所有的嘈杂都吐出去。

就在这时，一阵清脆悦耳的自行车铃声，由远及近，叮铃铃地响着，打破了竹林的静谧。

噗噗好奇地望过去。只见一辆鲜亮的小红车，灵巧地在竹林间穿梭。车座上，是一个毛茸茸的、雪白的身影。

自行车稳稳地停在噗噗面前。噗噗看清了，那是一只非常精神的小狗，一身蓬松雪白的毛发，头上还戴着一顶帅气的邮差帽。他看起来像一团会动的、干净的云朵。

没等噗噗本能地后退，那只萨摩耶小狗已经利落地跳下车，脸上绽开一个阳光般灿烂的笑容。

"你好呀！请问是噗噗先生吗？"他的声音清脆又充满活力，像山涧里跳跃的溪水。

噗噗愣愣地点点头。

"太好啦！我叫奇奇，是微光镇的信使！"小狗开心地说着，从车前挂着的邮差包里，取出一封散发着淡淡奶香的信封。"这里有你的一封信，请签收！"

噗噗接过信，心里满是困惑。他实在想不起谁会给他写信。他小心翼翼地拆开信封，里面信纸上的字迹圆润温柔：

"亲爱的噗噗，
你愿不愿意相信，有这样一个地方——
那里没有嘈杂的嗡嗡声，也没有七嘴八舌的建议，只有一个属于你的、永远飘散着面包香气的、暖融融的小厨房。
你愿意来这里看一看吗？
——你远方的朋友"

噗噗的目光落在最后的落款上。

"我……远方的朋友？"他喃喃自语，声音里充满了不解。他在脑海里努力搜寻，却找不到一个能对上号的身影。

然而，比这份困惑更强烈的，是一种巨大的、难以抗拒的向往。那个被描述的地方——那个只有面包香气、没有纷扰的厨房——像一块暖烘烘的磁石，深深地吸引着他心底某个柔软的角落。

他犹豫着，但内心深处，一个清晰的声音已经替他做出了回答：

"我……我愿意去看看。"

就在噗噗在心里重重应下"我愿意"的瞬间，一阵奇异的、带着青草与星辉气息的暖风，忽然温柔地将他包裹。

眼前的一切开始变得模糊、流动。高大的翠竹、绿色的光影、头顶的蓝天……都化作了旋转的、发光的粒子，如同梦幻的光点之舞。

仅仅是一眨眼的功夫，光芒散去。

噗噗发现自己已不在熟悉的竹林里。

他正站在一个美得让他几乎忘记呼吸的小镇入口。

天空中飘浮着的，是棉花糖般柔软的粉色与紫色星云。脚下的小路由光滑的鹅卵石铺就，缝隙间流淌着如液态月光般清澈的小溪，发出细微悦耳的叮咚声。路边的灯柱是一朵朵倒悬的、发着柔和光芒的铃兰花。远处的房屋造型各异，有的像圆润的蘑菇，有的像精致的茶壶，屋顶的烟囱里，正袅袅升起各种香甜味道的彩色炊烟。

整个世界安静而温柔，空气中弥漫着烘焙的甜香和不知名晚香花的芬芳。

噗噗看得呆住了，他从未见过如此奇妙的地方。他震撼地、小声地"哇"了一下，然后猛地转头看向身边——那只同样一脸惊奇的小白狗正站在旁边。

"奇奇……"噗噗第一次主动开口，声音因为紧张和巨大的困惑而微微发颤，"这……这里是哪里？我们……我们是怎么到这里来的？"

奇奇——那只快乐的萨摩耶信使，对他露出了一个更加灿烂、仿佛能驱散一切阴霾的笑容，尾巴在身后欢快地摇摆着。

"噗噗，"他开心地说，"欢迎来到微光镇。"`,
                        audioUrl: 'cloud://cloudbase-8gji862jcfb501e7.636c-cloudbase-8gji862jcfb501e7-1365531166/story/audio/pupu/001.mp3'
                    }
                },
                {
                    id: 2,
                    title: '叮咚声里的南瓜梦',
                    isFree: true,
                    cost: 0,
                    order: 2,
                    content: {
                        title: '叮咚声里的南瓜梦',
                        text: '第二章内容暂未添加...',
                        audioUrl: 'cloud://cloudbase-8gji862jcfb501e7.636c-cloudbase-8gji862jcfb501e7-1365531166/story/audio/pupu/002.mp3'
                    }
                },
                {
                    id: 3,
                    title: '第一块自由饼干',
                    isFree: true,
                    cost: 0,
                    order: 3,
                    content: {
                        title: '第一块自由饼干',
                        text: '第三章内容暂未添加...',
                        audioUrl: 'cloud://cloudbase-8gji862jcfb501e7.636c-cloudbase-8gji862jcfb501e7-1365531166/story/audio/pupu/003.mp3'
                    }
                },
                {
                    id: 4,
                    title: '噗噗的闪闪发亮愿望单',
                    isFree: false,
                    cost: 2,
                    order: 4,
                    content: {
                        title: '噗噗的闪闪发亮愿望单',
                        text: '第四章内容暂未添加...',
                        audioUrl: 'cloud://cloudbase-8gji862jcfb501e7.636c-cloudbase-8gji862jcfb501e7-1365531166/story/audio/pupu/004.mp3'
                    }
                },
                {
                    id: 5,
                    title: '噗噗的"超级大冒险"',
                    isFree: false,
                    cost: 2,
                    order: 5,
                    content: {
                        title: '噗噗的"超级大冒险"',
                        text: '第五章内容暂未添加...',
                        audioUrl: 'cloud://cloudbase-8gji862jcfb501e7.636c-cloudbase-8gji862jcfb501e7-1365531166/story/audio/pupu/005.mp3'
                    }
                },
                {
                    id: 6,
                    title: '竹叶本子里的知音',
                    isFree: false,
                    cost: 2,
                    order: 6,
                    content: {
                        title: '竹叶本子里的知音',
                        text: '第六章内容暂未添加...',
                        audioUrl: 'cloud://cloudbase-8gji862jcfb501e7.636c-cloudbase-8gji862jcfb501e7-1365531166/story/audio/pupu/006.mp3'
                    }
                },
                {
                    id: 7,
                    title: '魔法厨房的诞生',
                    isFree: false,
                    cost: 2,
                    order: 7,
                    content: {
                        title: '魔法厨房的诞生',
                        text: '第七章内容暂未添加...',
                        audioUrl: 'cloud://cloudbase-8gji862jcfb501e7.636c-cloudbase-8gji862jcfb501e7-1365531166/story/audio/pupu/007.mp3'
                    }
                },
                {
                    id: 8,
                    title: '奇怪的奇奇',
                    isFree: false,
                    cost: 2,
                    order: 8,
                    content: {
                        title: '奇怪的奇奇',
                        text: '第八章内容暂未添加...',
                        audioUrl: 'cloud://cloudbase-8gji862jcfb501e7.636c-cloudbase-8gji862jcfb501e7-1365531166/story/audio/pupu/008.mp3'
                    }
                },
                {
                    id: 9,
                    title: '甜甜魔法好像失效了',
                    isFree: false,
                    cost: 2,
                    order: 9,
                    content: {
                        title: '甜甜魔法好像失效了',
                        text: '第九章内容暂未添加...',
                        audioUrl: 'cloud://cloudbase-8gji862jcfb501e7.636c-cloudbase-8gji862jcfb501e7-1365531166/story/audio/pupu/009.mp3'
                    }
                },
                {
                    id: 10,
                    title: '一个拥抱的重量',
                    isFree: false,
                    cost: 2,
                    order: 10,
                    content: {
                        title: '一个拥抱的重量',
                        text: '第十章内容暂未添加...',
                        audioUrl: 'cloud://cloudbase-8gji862jcfb501e7.636c-cloudbase-8gji862jcfb501e7-1365531166/story/audio/pupu/010.mp3'
                    }
                }
            ],
            createdAt: new Date(),
            updatedAt: new Date()
        },
        {
            type: 'character',
            name: '橘猫团团',
            image: 'cloud://cloudbase-8gji862jcfb501e7.636c-cloudbase-8gji862jcfb501e7-1365531166/images/story_icon_tuantuan.png',
            tagline: '描绘内心星空的小小创作者',
            description: '"我……我不想再抓鱼了。"团团对着自己的影子，用一种很轻、但又无比坚定的声音，下了她猫生中第一个决定。',
            order: 2,
            isActive: true,
            chapters: [
                {
                    id: 1,
                    title: '小橘猫团团与第一颗光点',
                    isFree: true,
                    cost: 0,
                    order: 1,
                    content: {
                        title: '小橘猫团团与第一颗光点',
                        text: '团团的故事内容暂未添加...',
                        audioUrl: 'cloud://cloudbase-8gji862jcfb501e7.636c-cloudbase-8gji862jcfb501e7-1365531166/story/audio/tuantuan/001.mp3'
                    }
                },
                {
                    id: 2,
                    title: '熊猫噗噗与一间属于自己的小屋',
                    isFree: true,
                    cost: 0,
                    order: 2,
                    content: {
                        title: '熊猫噗噗与一间属于自己的小屋',
                        text: '团团第二章内容暂未添加...',
                        audioUrl: 'cloud://cloudbase-8gji862jcfb501e7.636c-cloudbase-8gji862jcfb501e7-1365531166/story/audio/tuantuan/002.mp3'
                    }
                },
                {
                    id: 3,
                    title: '会打呼噜的云和长耳朵的鞋',
                    isFree: true,
                    cost: 0,
                    order: 3,
                    content: {
                        title: '会打呼噜的云和长耳朵的鞋',
                        text: '团团第三章内容暂未添加...',
                        audioUrl: 'cloud://cloudbase-8gji862jcfb501e7.636c-cloudbase-8gji862jcfb501e7-1365531166/story/audio/tuantuan/003.mp3'
                    }
                }
            ],
            createdAt: new Date(),
            updatedAt: new Date()
        },
        {
            type: 'character',
            name: '狐狸栗栗',
            image: 'cloud://cloudbase-8gji862jcfb501e7.636c-cloudbase-8gji862jcfb501e7-1365531166/images/story_icon_lili.png',
            tagline: '收集世间美好的杂货铺老板娘',
            description: '她看着玻璃里的那个自己，第一次，没有立刻去想明天那堆积如山的事情。一股深深的疲惫感，像涨潮的海水，悄悄地漫了上来。',
            order: 3,
            isActive: true,
            chapters: [
                {
                    id: 1,
                    title: '小狐狸栗栗与停不下来的星光',
                    isFree: true,
                    cost: 0,
                    order: 1,
                    content: {
                        title: '小狐狸栗栗与停不下来的星光',
                        text: '栗栗的故事内容暂未添加...',
                        audioUrl: 'cloud://cloudbase-8gji862jcfb501e7.636c-cloudbase-8gji862jcfb501e7-1365531166/story/audio/lili/001.mp3'
                    }
                }
            ],
            createdAt: new Date(),
            updatedAt: new Date()
        },
        {
            type: 'character',
            name: '小狗奇奇',
            image: 'cloud://cloudbase-8gji862jcfb501e7.636c-cloudbase-8gji862jcfb501e7-1365531166/images/story_icon_qiqi.png',
            tagline: '传递温暖与快乐的白日梦想家',
            description: '他长长地舒了一口气，那口气，仿佛把心里所有的吵闹，都一起吐了出去。然后，他第一次，允许自己的嘴角，不用弯得那么高了。',
            order: 4,
            isActive: true,
            chapters: [
                {
                    id: 1,
                    title: '最喧闹的派对与最安静的邀请函',
                    isFree: true,
                    cost: 0,
                    order: 1,
                    content: {
                        title: '最喧闹的派对与最安静的邀请函',
                        text: '奇奇的故事内容暂未添加...',
                        audioUrl: 'cloud://cloudbase-8gji862jcfb501e7.636c-cloudbase-8gji862jcfb501e7-1365531166/story/audio/qiqi/001.mp3'
                    }
                }
            ],
            createdAt: new Date(),
            updatedAt: new Date()
        }
    ];

    try {
        // 先清空现有数据
        const existingData = await db.collection('stories').get();
        if (existingData.data.length > 0) {
            // 逐个删除现有数据
            for (const doc of existingData.data) {
                await db.collection('stories').doc(doc._id).remove();
            }
        }

        // 插入新数据 - 使用add方法让数据库自动生成ID
        const insertResults = [];
        for (let i = 0; i < charactersData.length; i++) {
            const character = charactersData[i];
            const result = await db.collection('stories').add({
                data: character
            });
            insertResults.push(result);
        }

        return { success: true, message: `成功初始化 ${charactersData.length} 个角色及其章节数据到 stories 集合` };
    } catch (error) {
        console.error('初始化数据失败:', error);
        return { success: false, error: error.message };
    }
}

// 清空所有数据
async function clearAllData() {
    try {
        const res = await db.collection('stories').get();
        if (res.data.length > 0) {
            // 逐个删除数据
            for (const doc of res.data) {
                await db.collection('stories').doc(doc._id).remove();
            }
            return { success: true, message: `成功清空 ${res.data.length} 条数据` };
        } else {
            return { success: true, message: '集合为空，无需清空' };
        }
    } catch (error) {
        return { success: false, error: error.message };
    }
}