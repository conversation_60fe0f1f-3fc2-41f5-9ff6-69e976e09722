# 三问仪式分析和监控功能实施文档

## 概述

本文档描述了为三问仪式功能开发的完整分析和监控系统，包括数据收集、分析计算、异常检测和仪表板展示等功能。

## 功能特性

### 1. 核心分析功能

#### 1.1 三问参与度分析

- **完成率统计**: 计算用户完成三问仪式的比例
- **各问题参与率**: 分别统计三个问题的回答率和跳过率
- **平均会话时间**: 计算用户完成三问的平均耗时
- **退出点分析**: 识别用户最常退出的环节

#### 1.2 AI 日记生成分析

- **请求率统计**: 用户请求生成 AI 日记的比例
- **成功率监控**: AI 日记生成的成功率
- **质量指标**: 生成日记的平均字数和质量分布
- **性能监控**: 生成时间和备用方案使用率

#### 1.3 定时消息投递分析

- **创建率统计**: 用户创建定时消息的比例
- **投递成功率**: 消息成功投递的比例
- **渠道分析**: 不同投递渠道的效果对比
- **重试机制监控**: 重试次数和成功率统计

### 2. 用户行为分析

#### 2.1 层级性能对比

- **按用户层级统计**: 不同层级用户的参与模式
- **完成率对比**: 各层级用户的完成率差异
- **功能使用偏好**: 不同层级用户对 AI 日记等功能的使用情况

#### 2.2 个人行为分析

- **参与一致性**: 用户参与三问的规律性分析
- **偏好问题识别**: 用户最喜欢回答的问题类型
- **使用趋势**: 用户参与度的变化趋势

### 3. 异常检测和告警

#### 3.1 系统异常检测

- **完成率异常**: 完成率突然下降的检测
- **性能异常**: 响应时间异常增长的监控
- **错误率监控**: AI 生成失败率过高的告警

#### 3.2 用户行为异常

- **参与度异常**: 用户参与度突然变化的检测
- **使用模式异常**: 异常的使用模式识别

## 技术实现

### 1. 数据库设计

#### 1.1 三问指标集合 (three_questions_metrics)

```javascript
{
  _openid: String,           // 用户ID
  userLevel: Number,         // 用户层级

  // 参与度指标
  startedAt: Date,           // 开始时间
  completedAt: Date,         // 完成时间
  totalTime: Number,         // 总耗时(毫秒)

  // 各问题参与情况
  question1Answered: Boolean, // 第一问是否回答
  question1Skipped: Boolean,  // 第一问是否跳过
  question1Time: Number,      // 第一问耗时

  question2Answered: Boolean, // 第二问是否回答
  question2Skipped: Boolean,  // 第二问是否跳过
  question2Time: Number,      // 第二问耗时

  question3Answered: Boolean, // 第三问是否回答
  question3Skipped: Boolean,  // 第三问是否跳过
  question3Time: Number,      // 第三问耗时

  // AI日记生成相关
  requestedDiary: Boolean,    // 是否请求生成日记
  diaryGenerated: Boolean,    // 是否成功生成
  diaryGenerationTime: Number,// 生成耗时
  diaryGenerationSuccess: Boolean, // 生成是否成功
  diaryFallbackUsed: Boolean, // 是否使用备用方案
  diaryWordCount: Number,     // 日记字数

  // 定时消息相关
  scheduledMessage: Boolean,  // 是否创建定时消息
  messageScheduleSuccess: Boolean, // 消息调度是否成功

  // 完成状态
  completed: Boolean,         // 是否完成全部流程
  exitPoint: String,          // 退出点(如果未完成)

  // 关联信息
  dialogueTheme: String,      // 对话主题
  sourceRecordId: String,     // 关联的三问记录ID

  createTime: Date           // 创建时间
}
```

#### 1.2 用户消息集合 (user_messages)

```javascript
{
  openid: String,            // 用户ID
  type: String,              // 消息类型
  title: String,             // 消息标题
  content: String,           // 消息内容
  isRead: Boolean,           // 是否已读
  priority: String,          // 优先级
  sourceType: String,        // 来源类型
  sourceId: String,          // 来源ID
  createTime: Date          // 创建时间
}
```

### 2. 云函数架构

#### 2.1 tieringAnalytics 云函数

主要的分析处理云函数，包含以下功能：

**核心分析方法:**

- `recordThreeQuestionsMetrics()` - 记录三问指标
- `getThreeQuestionsAnalytics()` - 获取三问分析数据
- `getThreeQuestionsParticipationRate()` - 获取参与度统计
- `getAIDiaryGenerationMetrics()` - 获取 AI 日记生成指标
- `getScheduledMessageDeliveryStats()` - 获取定时消息投递统计
- `getUserBehaviorAnalysis()` - 获取用户行为分析

**仪表板支持:**

- `getDashboardData()` - 获取综合仪表板数据

#### 2.2 initTieringAnalytics 云函数

数据库初始化云函数，负责：

- 创建分析相关的数据库集合
- 设置必要的索引
- 初始化系统性能记录

### 3. 集成方式

#### 3.1 在 saveThreeQuestions 中集成指标记录

```javascript
// 记录三问仪式分析指标
const analyticsData = {
  openid: openid,
  userLevel: processedData.userLevel,
  // ... 其他指标数据
};

await cloud.callFunction({
  name: "tieringAnalytics",
  data: {
    type: "recordThreeQuestionsMetrics",
    data: analyticsData,
  },
});
```

#### 3.2 前端页面集成

在三问页面中添加时间记录和状态跟踪：

```javascript
// 记录开始时间
const startTime = Date.now();

// 记录各问题的回答时间
const questionTimes = [];

// 在提交时计算总时间
const totalTime = Date.now() - startTime;
```

## 部署指南

### 1. 云函数部署

#### 1.1 部署 tieringAnalytics 云函数

```bash
# 进入云函数目录
cd cloudfunctions/tieringAnalytics

# 安装依赖
npm install

# 部署云函数
tcb fn deploy tieringAnalytics
```

#### 1.2 部署 initTieringAnalytics 云函数

```bash
# 进入云函数目录
cd cloudfunctions/initTieringAnalytics

# 安装依赖
npm install

# 部署云函数
tcb fn deploy initTieringAnalytics
```

### 2. 数据库初始化

#### 2.1 运行初始化云函数

```javascript
// 在小程序中调用初始化
wx.cloud.callFunction({
  name: "initTieringAnalytics",
  success: (res) => {
    console.log("数据库初始化成功:", res.result);
  },
});
```

### 3. 权限配置

#### 3.1 数据库权限设置

确保以下集合的读写权限正确配置：

- `three_questions_metrics` - 仅创建者可读写
- `user_messages` - 仅创建者可读写
- `scheduled_messages` - 仅创建者可读写

#### 3.2 云函数权限

确保 tieringAnalytics 云函数有以下权限：

- 数据库读写权限
- 调用其他云函数权限

## 使用示例

### 1. 获取三问分析数据

```javascript
wx.cloud.callFunction({
  name: "tieringAnalytics",
  data: {
    type: "getThreeQuestionsAnalytics",
    startDate: "2024-01-01",
    endDate: "2024-01-31",
    userLevel: 3,
  },
  success: (res) => {
    const analytics = res.result.data.analytics;
    console.log("完成率:", analytics.completionRate);
    console.log("第一问参与率:", analytics.question1.participationRate);
    // ... 处理其他分析数据
  },
});
```

### 2. 获取用户行为分析

```javascript
wx.cloud.callFunction({
  name: "tieringAnalytics",
  data: {
    type: "getUserBehaviorAnalysis",
    openid: "user_openid",
    startDate: "2024-01-01",
    endDate: "2024-01-31",
  },
  success: (res) => {
    const behavior = res.result.data;
    console.log(
      "参与率:",
      behavior.participationPattern.threeQuestionsParticipationRate
    );
    console.log("一致性分数:", behavior.trends.consistencyScore);
  },
});
```

### 3. 获取仪表板数据

```javascript
wx.cloud.callFunction({
  name: "tieringAnalytics",
  data: {
    type: "getDashboardData",
  },
  success: (res) => {
    const dashboard = res.result.data;
    console.log("今日三问数:", dashboard.overview.todayThreeQuestions);
    console.log("三问指标:", dashboard.threeQuestionsMetrics);
    console.log("AI日记指标:", dashboard.aiDiaryMetrics);
  },
});
```

## 监控和维护

### 1. 性能监控

- 定期检查分析查询的响应时间
- 监控数据库存储使用情况
- 关注云函数的调用频率和成功率

### 2. 数据清理

建议设置定期任务清理过期的分析数据：

```javascript
// 清理30天前的指标数据
const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
await db
  .collection("three_questions_metrics")
  .where({
    createTime: db.command.lt(thirtyDaysAgo),
  })
  .remove();
```

### 3. 异常告警

配置关键指标的告警阈值：

- 完成率低于 50%
- AI 日记生成成功率低于 70%
- 定时消息投递成功率低于 90%

## 扩展建议

### 1. 实时监控

- 集成实时数据流处理
- 添加实时告警机制
- 支持实时仪表板更新

### 2. 高级分析

- 用户行为预测模型
- 个性化推荐算法
- A/B 测试框架集成

### 3. 可视化增强

- 图表组件集成
- 交互式数据探索
- 自定义报表生成

## 总结

三问仪式分析和监控功能为产品提供了全面的数据洞察能力，帮助了解用户行为模式、优化功能设计、提升用户体验。通过持续的数据收集和分析，可以不断改进三问仪式的设计和实现，提高用户参与度和满意度。
